# Proxy Manager API 文档

本文档详细介绍了 Proxy Manager 的 API 接口。

**基础 URL**: `/api/v1`

## 认证 (Authentication)

所有需要认证的接口，都需要在请求头中加入 `Authorization` 字段，值为 `Bearer <your_jwt_token>`。

---

### 1. 用户注册

*   **POST** `/auth/register`
*   **描述**: 创建一个新用户账户。
*   **认证**: 无需
*   **请求体** (`application/json`):
    ```json
    {
      "username": "testuser",
      "email": "<EMAIL>",
      "password": "yoursecurepassword",
      "role": "user"
    }
    ```
*   **响应 (201 Created)**:
    ```json
    {
      "message": "User registered successfully",
      "data": {
        "id": "user-uuid",
        "username": "testuser",
        "email": "<EMAIL>",
        "role": "user",
        "status": "active",
        "created_at": "2023-10-27T10:00:00Z"
      }
    }
    ```

### 2. 用户登录

*   **POST** `/auth/login`
*   **描述**: 使用用户名和密码登录，获取 JWT。
*   **认证**: 无需
*   **请求体** (`application/json`):
    ```json
    {
      "username": "testuser",
      "password": "yoursecurepassword"
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Login successful",
      "data": {
        "access_token": "your_jwt_token",
        "expires_in": 3600,
        "user": {
          "id": "user-uuid",
          "username": "testuser",
          "email": "<EMAIL>",
          "role": "user",
          "status": "active",
          "created_at": "2023-10-27T10:00:00Z",
          "last_login": "2023-10-28T10:00:00Z"
        }
      }
    }
    ```

### 3. 获取用户资料

*   **GET** `/auth/profile`
*   **描述**: 获取当前登录用户的个人资料。
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": {
        "id": "user-uuid",
        "username": "testuser",
        "email": "<EMAIL>",
        "role": "user",
        "status": "active",
        "created_at": "2023-10-27T10:00:00Z",
        "last_login": "2023-10-28T10:00:00Z"
      }
    }
    ```

---

## API 密钥管理 (API Key Management)

### 1. 获取 API 密钥列表

*   **GET** `/user/apikeys`
*   **描述**: 获取用户的所有 API 密钥。
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": [
        {
          "id": "key-uuid",
          "name": "My Test Key",
          "key_prefix": "pm_test_****",
          "is_active": true,
          "created_at": "2023-10-27T10:00:00Z"
        }
      ]
    }
    ```

### 2. 创建 API 密钥

*   **POST** `/user/apikeys`
*   **描述**: 创建一个新的 API 密钥。
*   **认证**: 需要
*   **请求体** (`application/json`):
    ```json
    {
      "name": "My New Key",
      "expires_in_days": 30
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": {
        "api_key": "pm_xxxxxxxxxxxxxxxxxxxx",
        "id": "new-key-uuid",
        "name": "My New Key"
      }
    }
    ```

### 3. 更新 API 密钥

*   **PUT** `/user/apikeys/:id`
*   **描述**: 更新一个已存在的 API 密钥。
*   **认证**: 需要
*   **请求体** (`application/json`):
    ```json
    {
      "name": "Updated Key Name",
      "is_active": false
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "data": {
        "id": "key-uuid",
        "name": "Updated Key Name",
        "is_active": false
      }
    }
    ```

### 4. 删除 API 密钥

*   **DELETE** `/user/apikeys/:id`
*   **描述**: 删除一个 API 密钥。
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "success": true,
      "message": "API key deleted successfully"
    }
    ```

---

## 代理管理 (Proxy Management)

### 1. 获取代理列表

*   **GET** `/proxies`
*   **描述**: 获取所有代理的列表。
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": [
        {
          "id": "proxy-uuid",
          "host": "127.0.0.1",
          "port": 8080,
          "type": "http",
          "status": "active"
        }
      ],
      "total": 1
    }
    ```

### 2. 添加代理

*   **POST** `/proxies`
*   **描述**: 添加一个新的代理。
*   **认证**: 需要, **管理员权限**
*   **请求体** (`application/json`):
    ```json
    {
      "host": "127.0.0.1",
      "port": 8081,
      "type": "socks5"
    }
    ```
*   **响应 (201 Created)**:
    ```json
    {
      "message": "Proxy added successfully",
      "data": {
        "id": "new-proxy-uuid",
        "host": "127.0.0.1",
        "port": 8081,
        "type": "socks5"
      }
    }
    ```

### 3. 删除代理

*   **DELETE** `/proxies/:id`
*   **描述**: 删除一个代理。
*   **认证**: 需要, **管理员权限**
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Proxy deleted successfully"
    }
    ```

### 4. 批量导入代理

*   **POST** `/proxies/batch-import`
*   **描述**: 批量导入代理。
*   **认证**: 需要, **管理员权限**
*   **请求体** (`application/json`):
    ```json
    [
      { "host": "*********", "port": 8080, "type": "http" },
      { "host": "*********", "port": 8080, "type": "http" }
    ]
    ```
*   **响应 (200 OK)**:
    ```json
    {
        "message": "Batch import completed",
        "success": 2,
        "failed": 0,
        "errors": []
    }
    ```

### 5. 健康检查

*   **POST** `/proxies/:id/health-check`
*   **描述**: 对单个代理进行健康检查。
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": {
        "proxy_id": "proxy-uuid",
        "status": "active",
        "response_time": 120,
        "checked_at": "2023-10-27T10:00:00Z"
      }
    }
    ```

---

## 标签管理 (Tag Management)

### 1. 创建标签

*   **POST** `/tags`
*   **描述**: 创建一个新标签。
*   **认证**: 需要, **管理员权限**
*   **请求体** (`application/json`):
    ```json
    {
      "name": "High-Speed",
      "color": "#FF0000"
    }
    ```
*   **响应 (201 Created)**:
    ```json
    {
      "message": "Tag created successfully",
      "data": {
        "id": "tag-uuid",
        "name": "High-Speed",
        "color": "#FF0000"
      }
    }
    ```

### 2. 获取所有标签

*   **GET** `/tags`
*   **描述**: 获取所有标签的列表。
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": [
        {
          "id": "tag-uuid",
          "name": "High-Speed",
          "color": "#FF0000"
        }
      ],
      "total": 1
    }
    ```

### 3. 为代理分配标签

*   **POST** `/tags/assign`
*   **描述**: 为一个或多个代理分配一个或多个标签。
*   **认证**: 需要, **管理员权限**
*   **请求体** (`application/json`):
    ```json
    {
      "proxy_ids": ["proxy-uuid-1", "proxy-uuid-2"],
      "tag_ids": ["tag-uuid-1", "tag-uuid-2"]
    }
    ```
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Tag assignment completed",
      "success": 2,
      "total": 2
    }
    ```

---

## 任务管理 (Task Management)

### 1. 创建任务

*   **POST** `/tasks`
*   **描述**: 创建一个新任务。
*   **认证**: 需要
*   **请求体** (`application/json`):
    ```json
    {
      "name": "My Scraping Task",
      "url": "http://example.com",
      "method": "GET",
      "proxy_strategy": "round_robin"
    }
    ```
*   **响应 (201 Created)**:
    ```json
    {
      "message": "Task created successfully",
      "data": {
        "id": "task-uuid",
        "name": "My Scraping Task",
        "status": "pending"
      }
    }
    ```

### 2. 获取用户的所有任务

*   **GET** `/tasks`
*   **描述**: 获取当前用户的所有任务列表。
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "data": [
        {
          "id": "task-uuid",
          "name": "My Scraping Task",
          "status": "completed",
          "created_at": "2023-10-27T10:00:00Z"
        }
      ],
      "total": 1
    }
    ```

### 3. 取消任务

*   **PATCH** `/tasks/:id/cancel`
*   **描述**: 取消一个正在进行或等待中的任务。
*   **认证**: 需要
*   **响应 (200 OK)**:
    ```json
    {
      "message": "Task cancelled successfully"
    }
    ```
