# 代理采集器独立运行指南

## 概述

为了解决大规模代理验证（200万+）对主服务性能的影响，我们将代理采集器拆分为独立模块。现在可以通过以下两种方式运行采集器：

1. **独立命令行工具** - 推荐用于大规模采集
2. **API端点控制** - 通过Web API启动采集任务

## 快速开始

### 1. 构建采集器

```bash
# 构建独立采集器
make build-collector

# 或构建所有组件
make build-all
```

### 2. 运行采集器

#### 方式一：命令行运行

```bash
# 一次性采集任务（默认）
./build/proxyFlow-collector

# 立即执行采集
./build/proxyFlow-collector -immediate

# 以守护进程模式运行
./build/proxyFlow-collector -daemon

# 强制重新全量运行（忽略缓存和进度）
./build/proxyFlow-collector -force

# 强制立即采集（忽略所有缓存和进度）
./build/proxyFlow-collector -immediate -force

# 强制守护进程模式（重新开始）
./build/proxyFlow-collector -daemon -force

# 使用自定义配置
./build/proxyFlow-collector -config config/config.yaml -log-level debug

# 查看帮助
./build/proxyFlow-collector -h
```

#### 方式二：通过Makefile

```bash
# 运行一次性采集
make run-collector

# 立即采集
make run-collector-now

# 守护进程模式
make run-collector-daemon

# 自定义配置
make run-collector-config

# 强制重新全量运行（推荐用于200万代理）
make run-collector-force

# 强制立即采集
make run-collector-force-now

# 强制守护进程模式
make run-collector-force-daemon
```

#### 方式三：API端点控制

```bash
# 通过API启动独立采集任务
curl -X POST http://localhost:8080/api/v1/collector/standalone \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d '{
    "priority": 3,
    "immediate": true,
    "batch_size": 1000,
    "concurrent_workers": 200
  }'

# 通过API启动强制全量采集（推荐用于200万代理）
curl -X POST http://localhost:8080/api/v1/collector/standalone \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d '{
    "priority": 4,
    "immediate": true,
    "force": true,
    "batch_size": 1000,
    "concurrent_workers": 500
  }'
```

## 命令行选项

| 选项 | 描述 | 默认值 |
|------|------|--------|
| `-config` | 配置文件路径 | `config/config.yaml` |
| `-immediate` | 立即执行一次采集 | `false` |
| `-daemon` | 以守护进程模式运行 | `false` |
| `-force` | 强制重新全量运行（忽略缓存和进度） | `false` |
| `-log-level` | 日志级别 | `info` |
| `-version` | 显示版本信息 | - |

## API端点

### 独立采集任务
- **POST** `/api/v1/collector/standalone`
- **说明**: 启动独立的代理采集任务，适用于大规模代理验证
- **权限**: 需要Admin角色

### 任务管理
- **GET** `/api/v1/collector/tasks` - 获取任务列表
- **GET** `/api/v1/collector/tasks/{taskId}` - 获取任务详情
- **GET** `/api/v1/collector/tasks/{taskId}/progress` - 获取任务进度
- **POST** `/api/v1/collector/tasks/{taskId}/pause` - 暂停任务
- **POST** `/api/v1/collector/tasks/{taskId}/resume` - 恢复任务
- **POST** `/api/v1/collector/tasks/{taskId}/cancel` - 取消任务

## 配置参数

在 `config/config.yaml` 中的 `collector` 部分：

```yaml
collector:
  enabled: true
  concurrent_workers: 200          # 并发工作器数量
  batch_size: 1000                # 批处理大小
  verification_timeout: 10s       # 验证超时时间
  request_timeout: 5s             # 请求超时时间
  cache_enabled: true             # 启用缓存
  enable_progress_display: true   # 启用进度显示
  memory_limit_mb: 2048          # 内存限制(MB)
  worker_pool_size: 500          # 工作池大小
```

## 优势对比

### 独立运行 vs 集成运行

| 特性 | 独立运行 | 集成运行 |
|------|----------|----------|
| **性能影响** | ✅ 不影响主服务 | ❌ 可能影响主服务性能 |
| **资源控制** | ✅ 独立资源管理 | ❌ 与主服务共享资源 |
| **可扩展性** | ✅ 可多实例运行 | ❌ 单实例限制 |
| **故障隔离** | ✅ 故障不影响主服务 | ❌ 可能导致主服务不稳定 |
| **监控调试** | ✅ 独立日志和指标 | ❌ 与主服务日志混合 |

### 推荐使用场景

- ✅ **使用独立运行**: 
  - 大规模代理验证（>10万）
  - 生产环境部署
  - 需要高可用性
  - 资源敏感的环境

- ✅ **使用Force模式 (`--force`)**:
  - **200万代理全量验证**
  - 需要从零开始重新验证所有代理
  - 怀疑缓存数据不准确
  - 定期全量更新代理状态

- ⚠️ **使用API控制**:
  - 小规模采集（<1万）
  - 开发测试环境
  - 需要与Web界面集成

## 监控和日志

### 进度监控
- 独立采集器支持实时进度显示
- 支持断点续传机制
- 提供详细的性能指标

### 日志配置
```bash
# 启用详细日志
./build/proxyFlow-collector -log-level debug

# 查看采集进度
tail -f logs/collector.log
```

### 性能指标
- 验证速度: ~500-2000 代理/秒（取决于硬件）
- 内存使用: 可配置限制
- 断点续传: 自动保存进度

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库配置
   ./build/proxyFlow-collector -config config/config.yaml
   ```

2. **内存不足**
   ```yaml
   # 调整配置文件
   collector:
     memory_limit_mb: 1024
     concurrent_workers: 100
   ```

3. **进度丢失**
   - 采集器支持断点续传
   - 进度自动保存在 `freeProxy/verification_progress.json`

## 升级指南

### 从集成模式迁移到独立模式

1. 构建新的采集器
2. 停止主服务中的采集器
3. 使用独立采集器

配置无需更改，完全兼容现有配置。

---

**注意**: 大规模代理验证建议使用独立采集器以确保最佳性能和稳定性。