# 📋 ProxyFlow 完整初始化指南

本指南将帮助您从零开始设置 ProxyFlow 项目，包括环境准备、配置生成、数据库初始化和首次启动。

## 🎯 快速开始（推荐）

### 一键初始化
```bash
# 1. 克隆项目
git clone <repository-url>
cd proxyFlow

# 2. 一键初始化
make quick-start
```

就这么简单！系统会自动完成所有初始化步骤。

## 📋 详细初始化流程

### 步骤 1: 环境依赖检查

```bash
# 检查环境依赖
make check-env
```

**必需依赖：**
- Go 1.22+
- Node.js 16+
- Docker（用于数据库）
- Git

**可选依赖：**
- pnpm（推荐的包管理器）
- air（Go 热重载工具）

### 步骤 2: 项目初始化

```bash
# 运行初始化脚本
make init
```

**初始化过程包括：**
- ✅ 环境依赖检查
- ✅ 生成安全的 JWT 密钥和 API 密钥
- ✅ 创建 `.env` 环境变量文件
- ✅ 复制配置文件模板
- ✅ 安装项目依赖（Go + Node.js）
- ✅ 创建管理员账户

### 步骤 3: 配置验证

```bash
# 验证配置是否正确
make validate-config
```

### 步骤 4: 启动服务

**方式 1: 一键部署（推荐）**
```bash
# 开发环境部署
make deploy

# 生产环境部署
make deploy-prod
```

**方式 2: Docker 部署**
```bash
# 基础服务
make docker-deploy

# 包含监控的完整服务
make docker-deploy-full
```

**方式 3: 手动启动**
```bash
# 启动数据库
./scripts/dev.sh start

# 启动后端
make dev-backend

# 启动前端（新终端）
make dev-frontend
```

## 🔧 配置说明

### 环境变量文件 (.env)

初始化后会自动生成 `.env` 文件，包含：

```bash
# 必需的敏感信息
PROXY_MANAGER_JWT_SECRET="auto-generated-jwt-secret"
PROXY_MANAGER_SUPER_API_KEY="auto-generated-api-key"
PROXY_MANAGER_POSTGRES_PASSWORD="proxy_manager_2025"

# 管理员账户
PROXY_MANAGER_ADMIN_USERNAME="admin"
PROXY_MANAGER_ADMIN_PASSWORD="auto-generated-password"

# 可选配置（使用默认值）
# PROXY_MANAGER_SERVER_PORT="8080"
# PROXY_MANAGER_LOG_LEVEL="info"
```

### 配置文件 (config/config.yaml)

包含应用的业务配置，如超时设置、重试策略等。

## 🌐 访问应用

### 开发环境
- **后端 API**: http://localhost:8080
- **前端开发**: http://localhost:5173
- **API 文档**: http://localhost:8080/api/docs

### 生产环境
- **完整应用**: http://localhost:8080
- **API 文档**: http://localhost:8080/api/docs
- **监控指标**: http://localhost:8080/metrics

### 管理员登录
- **用户名**: admin
- **密码**: 查看 `.env` 文件中的 `PROXY_MANAGER_ADMIN_PASSWORD`

## 🛠️ 常用命令

### 开发命令
```bash
make help              # 查看所有可用命令
make check-env          # 检查环境依赖
make init              # 初始化项目
make validate-config   # 验证配置
make dev               # 启动开发环境
make test              # 运行测试
make build             # 构建项目
```

### 部署命令
```bash
make deploy            # 开发环境部署
make deploy-prod       # 生产环境部署
make docker-deploy     # Docker 部署
make stop-all          # 停止所有服务
```

### 管理命令
```bash
make import-proxies    # 导入代理数据
make generate-keys     # 生成新密钥
make switch-migration  # 切换数据库迁移方案
make reset             # 完整重置项目
```

## 🔍 故障排除

### 常见问题

**Q: 初始化失败，提示环境依赖不满足**
```bash
# 检查具体缺失的依赖
make check-env

# 安装缺失的依赖后重新初始化
make init
```

**Q: 数据库连接失败**
```bash
# 检查数据库服务状态
docker ps | grep postgres

# 重启数据库服务
./scripts/dev.sh stop
./scripts/dev.sh start
```

**Q: 端口被占用**
```bash
# 检查端口占用
lsof -i :8080
lsof -i :5173

# 停止占用进程或修改配置文件中的端口
```

**Q: 配置验证失败**
```bash
# 查看详细错误信息
go run scripts/validate_config.go

# 重新生成配置
rm .env config/config.yaml
make init
```

### 日志查看

```bash
# 查看应用日志
make logs

# 查看 Docker 日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f proxy-manager-app
```

### 完整重置

如果遇到无法解决的问题，可以完整重置项目：

```bash
# ⚠️ 这将删除所有配置和数据
make reset

# 重新初始化
make init
```

## 🚀 生产环境部署

### Docker Compose 部署（推荐）

```bash
# 1. 初始化项目
make init

# 2. 部署生产环境
make docker-deploy-full

# 3. 验证部署
curl http://localhost:8080/health
```

### 手动部署

```bash
# 1. 构建项目
make build

# 2. 设置生产环境变量
export PROXY_MANAGER_SERVER_MODE="release"
export PROXY_MANAGER_LOG_LEVEL="warn"

# 3. 启动服务
./build/proxyFlow
```

## 📊 监控和维护

### 健康检查
```bash
# 应用健康检查
curl http://localhost:8080/health

# 数据库连接检查
make validate-config
```

### 性能监控
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000
- **应用指标**: http://localhost:8080/metrics

### 日常维护
```bash
# 查看服务状态
docker-compose ps

# 更新代理数据
make import-proxies

# 备份数据库
docker exec proxy-manager-postgres pg_dump -U postgres proxy_manager > backup.sql
```

## 🔐 安全建议

1. **修改默认密码**: 首次登录后立即修改管理员密码
2. **保护密钥文件**: 确保 `.env` 文件不被提交到版本控制
3. **定期轮换密钥**: 定期使用 `make generate-keys` 生成新密钥
4. **启用 HTTPS**: 生产环境建议配置反向代理启用 HTTPS
5. **网络安全**: 限制数据库端口的外部访问

## 📚 相关文档

- [配置管理指南](CONFIG_MIGRATION_GUIDE.md)
- [生产环境部署指南](PRODUCTION_DEPLOYMENT.md)
- [API 文档](api.md)
- [故障排除](TROUBLESHOOTING.md)
