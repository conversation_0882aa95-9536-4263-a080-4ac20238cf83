# 代理验证进度显示功能

## 概述

本项目集成了基于 `mpb` (Multi Progress Bar) 库的实时进度显示功能，为代理验证过程提供用户友好的命令行界面。

## 功能特性

### 🎯 核心功能
- **实时进度显示**：动态更新验证进度，显示已完成/总数
- **简洁单进度条**：优化的单一进度条，清晰易读
- **预估剩余时间**：自动计算并显示 ETA (Estimated Time of Arrival)
- **验证统计**：准确的成功/失败统计信息
- **验证速度**：精确的验证速度计算（代理/秒）
- **日志分离**：进度条与日志信息分离显示，避免混乱

### 🎨 界面元素
- **进度条**：可视化进度条，支持自定义宽度
- **百分比显示**：精确的百分比进度
- **动画效果**：旋转加载指示器和完成标记
- **颜色支持**：支持终端颜色显示
- **实时更新**：高频率更新，流畅的用户体验

## 配置选项

在 `config/config.yaml` 中添加以下配置：

```yaml
collector:
  # 进度显示配置
  enable_progress_display: true     # 启用进度显示
  progress_update_interval: 200ms   # 进度更新间隔（减少闪烁）
  progress_bar_width: 60           # 进度条宽度（适合大多数终端）
  show_detailed_progress: false    # 简化显示，不显示详细信息
```

### 配置说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enable_progress_display` | bool | true | 是否启用进度显示 |
| `progress_update_interval` | duration | 200ms | 进度更新间隔（推荐200ms减少闪烁） |
| `progress_bar_width` | int | 60 | 进度条宽度（字符数，推荐60适合终端） |
| `show_detailed_progress` | bool | false | 是否显示详细信息（推荐false简化显示） |

## 显示效果

### 典型显示界面

```text
代理验证 245/1000  [=====>---------------------]   25 % |  02:15 |   45/s
```

### 界面元素说明

**简化的单一进度条**：
- `代理验证`：简洁的标签
- `245/1000`：已完成/总数
- `[=====>---------------------]`：可视化进度条
- `25 %`：百分比进度
- `02:15`：预估剩余时间（ETA）
- `45/s`：当前验证速度（代理/秒）

## 技术实现

### 核心组件

1. **VerificationProgressDisplay**：主要的进度显示器类
2. **ProgressUpdate**：进度更新数据结构
3. **BatchInfo**：批次信息结构
4. **ProxyInfo**：代理信息结构

### 集成方式

进度显示功能已完全集成到现有的验证流程中：

```go
// 创建进度显示器
progressDisplay := NewVerificationProgressDisplay(config, logger, totalProxies)
defer progressDisplay.Stop()

// 在验证过程中更新进度
progressDisplay.Update(ProgressUpdate{
    Type: "proxy_complete",
    ProxyInfo: &ProxyInfo{
        Host:    proxy.Host,
        Port:    proxy.Port,
        Success: result.Success,
    },
})
```

### 性能考虑

- **异步更新**：进度更新在独立的 goroutine 中处理
- **缓冲通道**：使用带缓冲的通道避免阻塞
- **高效渲染**：mpb 库优化的渲染机制
- **可配置频率**：可调整更新间隔以平衡性能和体验

## 使用场景

### 1. 大规模代理验证
适用于验证数千个代理的场景，提供清晰的进度反馈。

### 2. 批量处理监控
实时监控批处理进度，了解当前处理状态。

### 3. 性能分析
通过速度显示和 ETA 估算，分析验证性能。

### 4. 用户体验优化
提供友好的命令行界面，避免"黑盒"操作。

## 故障排除

### 常见问题

1. **进度条不显示**
   - 检查 `enable_progress_display` 配置
   - 确认终端支持 ANSI 转义序列

2. **更新频率过高/过低**
   - 调整 `progress_update_interval` 配置
   - 建议范围：50ms - 500ms

3. **进度条宽度不合适**
   - 根据终端宽度调整 `progress_bar_width`
   - 建议范围：60 - 120 字符

4. **性能影响**
   - 如果验证性能受影响，可以：
     - 增加更新间隔
     - 禁用详细进度显示
     - 完全禁用进度显示

### 调试模式

设置环境变量启用调试：

```bash
export PROXY_MANAGER_LOG_LEVEL=debug
```

## 最佳实践

1. **合理配置更新间隔**：平衡用户体验和性能
2. **适配终端宽度**：根据使用环境调整进度条宽度
3. **监控性能影响**：在高负载场景下监控性能指标
4. **优雅降级**：在不支持的环境中自动禁用进度显示

## 技术依赖

- **mpb v8**：多进度条库
- **Go 1.21+**：Go 语言版本要求
- **ANSI 终端**：支持 ANSI 转义序列的终端

## 更新日志

- **v1.0.0**：初始版本，支持基本进度显示
- **v1.1.0**：添加批次进度和详细统计
- **v1.2.0**：优化性能和用户体验
