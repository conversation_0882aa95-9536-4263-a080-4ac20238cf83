#!/bin/bash
# 测试修复后的进度系统

set -e

echo "🚀 测试修复后的进度文件和进度条系统..."

# 清理之前的进度文件
if [ -f "freeProxy/verification_progress.json" ]; then
    echo "清理现有进度文件..."
    rm -f "freeProxy/verification_progress.json"
fi

echo "启动后台代理收集服务（测试断点续传）..."

# 启动服务并等待一段时间
timeout 30s go run cmd/main.go &
SERVER_PID=$!

echo "服务PID: $SERVER_PID"

# 等待服务启动
sleep 5

# 中断服务模拟断电
echo "模拟断电中断..."
kill -INT $SERVER_PID || true
wait $SERVER_PID 2>/dev/null || true

# 检查进度文件是否存在
if [ -f "freeProxy/verification_progress.json" ]; then
    echo "✅ 进度文件已创建"
    echo "进度文件内容："
    cat "freeProxy/verification_progress.json" | head -20
    
    # 重新启动服务测试恢复
    echo "测试断点续传功能..."
    timeout 20s go run cmd/main.go &
    SERVER_PID=$!
    
    sleep 10
    kill -INT $SERVER_PID || true
    wait $SERVER_PID 2>/dev/null || true
    
    echo "✅ 断点续传测试完成"
else
    echo "❌ 进度文件未创建"
fi

# 清理
if [ -f "freeProxy/verification_progress.json" ]; then
    echo "清理测试文件..."
    rm -f "freeProxy/verification_progress.json"
fi

echo "🎉 进度系统测试完成!"