package main

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"log"
	"os"
)

// generateSecureJWTSecret 生成安全的JWT密钥
func generateSecureJWTSecret() (string, error) {
	// 生成64字节的随机数据以确保足够的熵
	bytes := make([]byte, 64)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// 使用base64编码生成可读的密钥
	secret := base64.URLEncoding.EncodeToString(bytes)
	
	// 确保密钥包含所需的字符类型
	if len(secret) < 32 {
		return "", fmt.Errorf("generated secret too short")
	}
	
	return secret, nil
}

// generateSecureAPIKey 生成安全的API密钥
func generateSecureAPIKey() (string, error) {
	// 生成32字节的随机数据
	bytes := make([]byte, 32)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// 使用base64编码生成可读的API密钥
	return base64.URLEncoding.EncodeToString(bytes), nil
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run generate_jwt_secret.go [jwt|api|both]")
		fmt.Println("  jwt  - Generate JWT secret only")
		fmt.Println("  api  - Generate API key only") 
		fmt.Println("  both - Generate both JWT secret and API key")
		os.Exit(1)
	}

	mode := os.Args[1]

	switch mode {
	case "jwt":
		secret, err := generateSecureJWTSecret()
		if err != nil {
			log.Fatalf("Failed to generate JWT secret: %v", err)
		}
		fmt.Printf("Generated secure JWT secret:\n%s\n", secret)
		fmt.Printf("\nAdd this to your environment variables:\n")
		fmt.Printf("export PROXY_MANAGER_JWT_SECRET=\"%s\"\n", secret)

	case "api":
		apiKey, err := generateSecureAPIKey()
		if err != nil {
			log.Fatalf("Failed to generate API key: %v", err)
		}
		fmt.Printf("Generated secure API key:\n%s\n", apiKey)
		fmt.Printf("\nAdd this to your environment variables:\n")
		fmt.Printf("export PROXY_MANAGER_SUPER_API_KEY=\"%s\"\n", apiKey)

	case "both":
		secret, err := generateSecureJWTSecret()
		if err != nil {
			log.Fatalf("Failed to generate JWT secret: %v", err)
		}
		
		apiKey, err := generateSecureAPIKey()
		if err != nil {
			log.Fatalf("Failed to generate API key: %v", err)
		}
		
		fmt.Printf("Generated secure JWT secret:\n%s\n\n", secret)
		fmt.Printf("Generated secure API key:\n%s\n\n", apiKey)
		fmt.Printf("Add these to your environment variables:\n")
		fmt.Printf("export PROXY_MANAGER_JWT_SECRET=\"%s\"\n", secret)
		fmt.Printf("export PROXY_MANAGER_SUPER_API_KEY=\"%s\"\n", apiKey)

	default:
		fmt.Printf("Unknown mode: %s\n", mode)
		fmt.Println("Use 'jwt', 'api', or 'both'")
		os.Exit(1)
	}

	fmt.Printf("\n⚠️  SECURITY WARNING:\n")
	fmt.Printf("- Keep these secrets secure and never commit them to version control\n")
	fmt.Printf("- Use different secrets for different environments (dev/staging/prod)\n")
	fmt.Printf("- Rotate secrets regularly for enhanced security\n")
}
