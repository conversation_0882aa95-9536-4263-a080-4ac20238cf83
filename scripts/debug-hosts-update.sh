#!/bin/bash

# 直接观察 ProcessedHosts 更新过程的测试

set -e

echo "🔍 调试 ProcessedHosts 更新过程"
echo "================================"

# 清理
rm -f freeProxy/verification_progress.json

echo "运行程序并观察相关日志..."
echo "特别关注 ProgressSaver 和 ProcessedHosts 相关信息"

# 运行10秒并捕获所有相关日志
timeout 10s go run cmd/main.go 2>&1 | grep -E "(ProgressSaver|processed_hosts_count|ProcessedHosts|Triggering.*save|saved_hosts_count|FinalSave)" || true

echo
echo "检查生成的进度文件："

if [ -f "freeProxy/verification_progress.json" ]; then
    echo "✅ 进度文件存在"
    
    if command -v jq >/dev/null 2>&1; then
        processed=$(jq -r '.processed_proxies' freeProxy/verification_progress.json)
        hosts_count=$(jq -r '.processed_hosts | length' freeProxy/verification_progress.json)
        
        echo "处理的代理数: $processed"
        echo "ProcessedHosts 记录数: $hosts_count"
        
        if [ "$hosts_count" -eq 0 ]; then
            echo "❌ ProcessedHosts 仍为空 {}"
            echo "完整进度文件内容:"
            jq . freeProxy/verification_progress.json
        else
            echo "✅ ProcessedHosts 有数据！"
            echo "前5个记录:"
            jq -r '.processed_hosts | to_entries | limit(5; .[]) | .key' freeProxy/verification_progress.json
        fi
    else
        cat freeProxy/verification_progress.json
    fi
else
    echo "❌ 进度文件不存在"
fi

echo
echo "分析：如果日志中显示 processed_hosts_count > 0 但文件中为空，"
echo "这说明存在时序问题或保存逻辑有误"