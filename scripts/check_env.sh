#!/bin/bash

# ProxyFlow 环境依赖检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔧 $1${NC}"; }

echo -e "${CYAN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                ProxyFlow 环境依赖检查                      ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# 检查结果统计
CHECKS_PASSED=0
CHECKS_FAILED=0
WARNINGS=0

# 检查函数
check_command() {
    local cmd=$1
    local name=$2
    local min_version=$3
    local install_hint=$4
    
    if command -v "$cmd" >/dev/null 2>&1; then
        local version=$($cmd --version 2>/dev/null | head -1 || echo "unknown")
        log_success "$name 已安装: $version"
        ((CHECKS_PASSED++))
        return 0
    else
        log_error "$name 未安装"
        if [ -n "$install_hint" ]; then
            echo "   安装提示: $install_hint"
        fi
        ((CHECKS_FAILED++))
        return 1
    fi
}

# 检查 Go
log_step "检查 Go 环境..."
if check_command "go" "Go" "1.22+" "https://golang.org/dl/"; then
    go_version=$(go version | grep -o 'go[0-9]\+\.[0-9]\+' | sed 's/go//')
    if [ "$(printf '%s\n' "1.22" "$go_version" | sort -V | head -n1)" = "1.22" ]; then
        log_success "Go 版本满足要求 (>= 1.22)"
    else
        log_warning "Go 版本较低，建议升级到 1.22+"
        ((WARNINGS++))
    fi
fi

# 检查 Node.js
log_step "检查 Node.js 环境..."
if check_command "node" "Node.js" "16+" "https://nodejs.org/"; then
    node_version=$(node --version | sed 's/v//')
    if [ "$(printf '%s\n' "16.0.0" "$node_version" | sort -V | head -n1)" = "16.0.0" ]; then
        log_success "Node.js 版本满足要求 (>= 16)"
    else
        log_warning "Node.js 版本较低，建议升级到 16+"
        ((WARNINGS++))
    fi
fi

# 检查包管理器
log_step "检查包管理器..."
if command -v pnpm >/dev/null 2>&1; then
    log_success "pnpm 已安装 (推荐)"
elif command -v npm >/dev/null 2>&1; then
    log_success "npm 已安装"
    log_info "建议安装 pnpm 以获得更好的性能: npm install -g pnpm"
else
    log_error "未找到 npm 或 pnpm"
    ((CHECKS_FAILED++))
fi

# 检查 Docker
log_step "检查 Docker 环境..."
if check_command "docker" "Docker" "" "https://docs.docker.com/get-docker/"; then
    if docker info >/dev/null 2>&1; then
        log_success "Docker 服务正在运行"
    else
        log_warning "Docker 已安装但服务未运行，请启动 Docker"
        ((WARNINGS++))
    fi
fi

# 检查 Git
log_step "检查 Git..."
check_command "git" "Git" "" "https://git-scm.com/downloads"

# 检查端口占用
log_step "检查端口占用..."
check_port() {
    local port=$1
    local service=$2
    if lsof -i :$port >/dev/null 2>&1; then
        log_warning "端口 $port 被占用 ($service)"
        ((WARNINGS++))
    else
        log_success "端口 $port 可用 ($service)"
    fi
}

check_port 8080 "后端服务"
check_port 5173 "前端开发服务器"
check_port 5432 "PostgreSQL"
check_port 6379 "Redis"

# 检查磁盘空间
log_step "检查磁盘空间..."
available_space=$(df . | tail -1 | awk '{print $4}')
if [ "$available_space" -gt 1048576 ]; then  # 1GB in KB
    log_success "磁盘空间充足"
else
    log_warning "磁盘空间不足，建议至少保留 1GB 空间"
    ((WARNINGS++))
fi

# 总结
echo ""
echo -e "${CYAN}📊 检查结果总结:${NC}"
echo "✅ 通过: $CHECKS_PASSED"
echo "❌ 失败: $CHECKS_FAILED"
echo "⚠️  警告: $WARNINGS"
echo ""

if [ $CHECKS_FAILED -eq 0 ]; then
    log_success "环境检查通过！可以继续初始化项目"
    echo ""
    echo -e "${CYAN}🚀 下一步:${NC}"
    echo "运行初始化脚本: ./scripts/init.sh"
    exit 0
else
    log_error "环境检查失败，请先安装缺失的依赖"
    echo ""
    echo -e "${CYAN}📋 安装指南:${NC}"
    echo "• Go: https://golang.org/dl/"
    echo "• Node.js: https://nodejs.org/"
    echo "• Docker: https://docs.docker.com/get-docker/"
    exit 1
fi
