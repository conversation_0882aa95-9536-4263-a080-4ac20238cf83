# ProxyFlow 脚本说明

本目录包含 ProxyFlow 项目的各种实用脚本，用于简化开发、部署和管理流程。

## 🚀 快速开始

### 一键初始化（推荐）
```bash
# 给脚本执行权限
chmod +x scripts/init.sh

# 运行一键初始化
./scripts/init.sh
```

这个脚本会自动完成：
- ✅ 生成安全的 JWT Secret 和 Super API Key
- ✅ 创建 .env 环境变量文件
- ✅ 复制配置文件模板
- ✅ 安装项目依赖

## 📁 脚本文件说明

### 🔧 初始化和配置

#### `init.sh` - 一键初始化脚本
**功能**: 项目首次设置的完整自动化脚本
**用法**: `./scripts/init.sh`
**包含**:
- 生成安全密钥
- 创建配置文件
- 安装依赖
- 环境检查

#### `generate_jwt_secret.go` - 密钥生成工具
**功能**: 生成安全的 JWT Secret 和 API Key
**用法**:
```bash
# 生成 JWT Secret
go run scripts/generate_jwt_secret.go jwt

# 生成 API Key
go run scripts/generate_jwt_secret.go api

# 同时生成两个
go run scripts/generate_jwt_secret.go both
```

### 🐳 开发环境

#### `dev.sh` - 数据库服务管理
**功能**: 启动/停止 PostgreSQL 和 Redis 容器
**用法**:
```bash
# 启动数据库服务
./scripts/dev.sh start

# 停止数据库服务
./scripts/dev.sh stop
```

## 🔄 典型工作流程

### 首次设置
```bash
# 1. 一键初始化
./scripts/init.sh

# 2. 启动数据库服务
./scripts/dev.sh start

# 3. 启动开发环境
# 后端
 go run cmd/main.go
# 前端
 cd web && npm run dev
```

### 日常开发
```bash
# 启动数据库服务（如未运行）
./scripts/dev.sh start

# 启动后端
 go run cmd/main.go
# 启动前端
 cd web && npm run dev
```

### 导入代理数据
```bash
# 确保有 webshare_proxy.json 文件
go run scripts/import_webshare_proxies.go
```

## 🛠️ 环境要求

- **Go 1.22+**: 运行 Go 脚本
- **Node.js 16+**: 前端开发
- **Docker**: 数据库容器（可选，也可本地安装）
- **PostgreSQL 12+**: 数据存储
- **Redis 6.0+**: 缓存和队列

## 📝 配置文件

### `.env` 文件（自动生成）
```bash
PROXY_MANAGER_JWT_SECRET="your-jwt-secret"
PROXY_MANAGER_SUPER_API_KEY="your-super-api-key"
PROXY_MANAGER_POSTGRES_PASSWORD="your-db-password"
```

### `config/config.yaml`
项目主配置文件，包含服务器、数据库、Redis 等配置。

## 🔒 安全注意事项

1. **保护密钥**: `.env` 文件包含敏感信息，请勿提交到版本控制
2. **生产环境**: 生产部署时请修改默认密码
3. **API Key**: Super API Key 拥有最高权限，请妥善保管

## ❓ 常见问题

**Q: 初始化失败怎么办？**
A: 检查 Go 和 Node.js 版本，确保满足最低要求

**Q: 数据库连接失败？**
A: 确保 PostgreSQL 和 Redis 服务正在运行

**Q: 端口被占用？**
A: 检查 8080 (后端) 和 5173 (前端) 端口是否被其他程序占用

**Q: 如何重置配置？**
A: 删除 `.env` 和 `config/config.yaml` 文件，重新运行 `./scripts/init.sh`