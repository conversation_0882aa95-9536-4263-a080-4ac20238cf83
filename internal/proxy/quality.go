package proxy

import (
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"strings"
	"time"

	"proxyFlow/internal/models"

	"github.com/sirupsen/logrus"
)

// QualityAssessment 质量评估服务
type QualityAssessment struct {
	logger *logrus.Logger
	client *http.Client
}

// NewQualityAssessment 创建质量评估服务
func NewQualityAssessment(logger *logrus.Logger) *QualityAssessment {
	return &QualityAssessment{
		logger: logger,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// AssessProxyQuality 评估代理质量
func (qa *QualityAssessment) AssessProxyQuality(ctx context.Context, proxy *models.Proxy) (*models.QualityScore, error) {
	qa.logger.WithField("proxy_id", proxy.ID).Info("Starting quality assessment")

	// 并发执行各项测试
	speedChan := make(chan float64, 1)
	stabilityChan := make(chan float64, 1)
	anonymityChan := make(chan string, 1)

	// 速度测试
	go func() {
		score := qa.assessSpeed(ctx, proxy)
		speedChan <- score
	}()

	// 稳定性测试
	go func() {
		score := qa.assessStability(ctx, proxy)
		stabilityChan <- score
	}()

	// 匿名性测试
	go func() {
		level := qa.assessAnonymity(ctx, proxy)
		anonymityChan <- level
	}()

	// 等待所有测试完成
	speedScore := <-speedChan
	stabilityScore := <-stabilityChan
	anonymityLevel := <-anonymityChan

	// 计算可靠性评分（基于历史数据）
	reliabilityScore := qa.calculateReliabilityScore(proxy)

	// 计算综合评分
	overallScore := qa.calculateOverallScore(speedScore, stabilityScore, reliabilityScore, anonymityLevel)

	qualityScore := &models.QualityScore{
		Overall:        overallScore,
		Speed:          speedScore,
		Stability:      stabilityScore,
		Reliability:    reliabilityScore,
		AnonymityLevel: anonymityLevel,
	}

	qa.logger.WithFields(logrus.Fields{
		"proxy_id":        proxy.ID,
		"overall_score":   overallScore,
		"speed_score":     speedScore,
		"stability_score": stabilityScore,
		"reliability":     reliabilityScore,
		"anonymity":       anonymityLevel,
	}).Info("Quality assessment completed")

	return qualityScore, nil
}

// assessSpeed 评估代理速度
func (qa *QualityAssessment) assessSpeed(ctx context.Context, proxy *models.Proxy) float64 {
	// 测试多个目标的响应时间
	testURLs := []string{
		"http://httpbin.org/ip",
		"http://www.google.com",
		"http://www.github.com",
	}

	var totalTime time.Duration
	successCount := 0

	for _, testURL := range testURLs {
		start := time.Now()
		if qa.testProxySpeed(proxy, testURL) {
			duration := time.Since(start)
			totalTime += duration
			successCount++
		}
	}

	if successCount == 0 {
		return 0.0
	}

	avgTime := totalTime / time.Duration(successCount)

	// 根据平均响应时间计算评分 (0-1)
	// < 1s: 1.0, 1-3s: 0.8, 3-5s: 0.6, 5-10s: 0.4, >10s: 0.2
	avgSeconds := avgTime.Seconds()
	switch {
	case avgSeconds < 1:
		return 1.0
	case avgSeconds < 3:
		return 0.8
	case avgSeconds < 5:
		return 0.6
	case avgSeconds < 10:
		return 0.4
	default:
		return 0.2
	}
}

// testProxySpeed 测试代理速度
func (qa *QualityAssessment) testProxySpeed(proxy *models.Proxy, testURL string) bool {
	proxyURL := fmt.Sprintf("%s://%s:%d", proxy.Type, proxy.Host, proxy.Port)
	if proxy.Username != "" && proxy.Password != "" {
		proxyURL = fmt.Sprintf("%s://%s:%s@%s:%d", proxy.Type, proxy.Username, proxy.Password, proxy.Host, proxy.Port)
	}

	parsedProxyURL, err := url.Parse(proxyURL)
	if err != nil {
		return false
	}

	client := &http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			Proxy: http.ProxyURL(parsedProxyURL),
		},
	}

	resp, err := client.Get(testURL)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}

// assessStability 评估代理稳定性
func (qa *QualityAssessment) assessStability(ctx context.Context, proxy *models.Proxy) float64 {
	// 进行多次连接测试
	testCount := 5
	successCount := 0

	for i := 0; i < testCount; i++ {
		if qa.testProxyConnection(proxy) {
			successCount++
		}
		time.Sleep(500 * time.Millisecond) // 间隔测试
	}

	return float64(successCount) / float64(testCount)
}

// testProxyConnection 测试代理连接
func (qa *QualityAssessment) testProxyConnection(proxy *models.Proxy) bool {
	address := fmt.Sprintf("%s:%d", proxy.Host, proxy.Port)
	conn, err := net.DialTimeout("tcp", address, 5*time.Second)
	if err != nil {
		return false
	}
	defer conn.Close()
	return true
}

// assessAnonymity 评估代理匿名性
func (qa *QualityAssessment) assessAnonymity(ctx context.Context, proxy *models.Proxy) string {
	// 获取真实IP
	realIP := qa.getRealIP()
	if realIP == "" {
		return "unknown"
	}

	// 通过代理获取IP
	proxyIP := qa.getIPThroughProxy(proxy)
	if proxyIP == "" {
		return "unknown"
	}

	// 检查是否暴露真实IP
	if strings.Contains(proxyIP, realIP) {
		return "transparent" // 透明代理
	}

	// 检查是否有代理相关的HTTP头
	headers := qa.getHeadersThroughProxy(proxy)
	if qa.hasProxyHeaders(headers) {
		return "anonymous" // 普通匿名代理
	}

	return "elite" // 高匿代理
}

// getRealIP 获取真实IP
func (qa *QualityAssessment) getRealIP() string {
	resp, err := qa.client.Get("http://httpbin.org/ip")
	if err != nil {
		return ""
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return ""
	}

	// 简单解析JSON获取IP
	bodyStr := string(body)
	if strings.Contains(bodyStr, "origin") {
		parts := strings.Split(bodyStr, "\"")
		for i, part := range parts {
			if part == "origin" && i+2 < len(parts) {
				return parts[i+2]
			}
		}
	}
	return ""
}

// getIPThroughProxy 通过代理获取IP
func (qa *QualityAssessment) getIPThroughProxy(proxy *models.Proxy) string {
	proxyURL := fmt.Sprintf("%s://%s:%d", proxy.Type, proxy.Host, proxy.Port)
	if proxy.Username != "" && proxy.Password != "" {
		proxyURL = fmt.Sprintf("%s://%s:%s@%s:%d", proxy.Type, proxy.Username, proxy.Password, proxy.Host, proxy.Port)
	}

	parsedProxyURL, err := url.Parse(proxyURL)
	if err != nil {
		return ""
	}

	client := &http.Client{
		Timeout: 15 * time.Second,
		Transport: &http.Transport{
			Proxy: http.ProxyURL(parsedProxyURL),
		},
	}

	resp, err := client.Get("http://httpbin.org/ip")
	if err != nil {
		return ""
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return ""
	}

	return string(body)
}

// getHeadersThroughProxy 通过代理获取HTTP头
func (qa *QualityAssessment) getHeadersThroughProxy(proxy *models.Proxy) map[string]string {
	proxyURL := fmt.Sprintf("%s://%s:%d", proxy.Type, proxy.Host, proxy.Port)
	if proxy.Username != "" && proxy.Password != "" {
		proxyURL = fmt.Sprintf("%s://%s:%s@%s:%d", proxy.Type, proxy.Username, proxy.Password, proxy.Host, proxy.Port)
	}

	parsedProxyURL, err := url.Parse(proxyURL)
	if err != nil {
		return nil
	}

	client := &http.Client{
		Timeout: 15 * time.Second,
		Transport: &http.Transport{
			Proxy: http.ProxyURL(parsedProxyURL),
		},
	}

	resp, err := client.Get("http://httpbin.org/headers")
	if err != nil {
		return nil
	}
	defer resp.Body.Close()

	// 这里简化处理，实际应该解析JSON
	headers := make(map[string]string)
	return headers
}

// hasProxyHeaders 检查是否有代理相关的HTTP头
func (qa *QualityAssessment) hasProxyHeaders(headers map[string]string) bool {
	proxyHeaders := []string{
		"X-Forwarded-For",
		"X-Real-IP",
		"Via",
		"X-Proxy-ID",
		"HTTP_VIA",
		"HTTP_X_FORWARDED_FOR",
	}

	for _, header := range proxyHeaders {
		if _, exists := headers[header]; exists {
			return true
		}
	}
	return false
}

// calculateReliabilityScore 计算可靠性评分
func (qa *QualityAssessment) calculateReliabilityScore(proxy *models.Proxy) float64 {
	// 基于历史数据计算可靠性
	if proxy.UseCount == 0 {
		return 0.5 // 新代理给予中等评分
	}

	// 基于失败次数和使用次数计算
	failureRate := float64(proxy.Failures) / float64(proxy.UseCount+int64(proxy.Failures))
	reliabilityScore := 1.0 - failureRate

	// 确保评分在0-1范围内
	if reliabilityScore < 0 {
		reliabilityScore = 0
	}
	if reliabilityScore > 1 {
		reliabilityScore = 1
	}

	return reliabilityScore
}

// calculateOverallScore 计算综合评分
func (qa *QualityAssessment) calculateOverallScore(speed, stability, reliability float64, anonymity string) float64 {
	// 权重分配
	speedWeight := 0.3
	stabilityWeight := 0.3
	reliabilityWeight := 0.25
	anonymityWeight := 0.15

	// 匿名性评分转换
	anonymityScore := qa.anonymityLevelToScore(anonymity)

	// 加权平均
	overallScore := speed*speedWeight +
		stability*stabilityWeight +
		reliability*reliabilityWeight +
		anonymityScore*anonymityWeight

	// 确保评分在0-1范围内
	if overallScore < 0 {
		overallScore = 0
	}
	if overallScore > 1 {
		overallScore = 1
	}

	return overallScore
}

// anonymityLevelToScore 将匿名等级转换为评分
func (qa *QualityAssessment) anonymityLevelToScore(level string) float64 {
	switch level {
	case "elite":
		return 1.0
	case "anonymous":
		return 0.7
	case "transparent":
		return 0.3
	default:
		return 0.0
	}
}

// BatchAssessQuality 批量评估代理质量
func (qa *QualityAssessment) BatchAssessQuality(ctx context.Context, proxies []*models.Proxy) ([]*models.QualityScore, error) {
	results := make([]*models.QualityScore, len(proxies))

	// 使用goroutine池进行并发评估
	semaphore := make(chan struct{}, 10) // 限制并发数
	resultChan := make(chan struct {
		index int
		score *models.QualityScore
		err   error
	}, len(proxies))

	// 启动评估任务
	for i, proxy := range proxies {
		go func(index int, p *models.Proxy) {
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			score, err := qa.AssessProxyQuality(ctx, p)
			resultChan <- struct {
				index int
				score *models.QualityScore
				err   error
			}{index, score, err}
		}(i, proxy)
	}

	// 收集结果
	for i := 0; i < len(proxies); i++ {
		result := <-resultChan
		if result.err != nil {
			qa.logger.WithError(result.err).WithField("proxy_index", result.index).Error("Failed to assess proxy quality")
			// 为失败的评估设置默认值
			results[result.index] = &models.QualityScore{
				Overall:        0.0,
				Speed:          0.0,
				Stability:      0.0,
				Reliability:    0.0,
				AnonymityLevel: "unknown",
			}
		} else {
			results[result.index] = result.score
		}
	}

	return results, nil
}
