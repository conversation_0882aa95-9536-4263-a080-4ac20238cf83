package models

import (
	"time"
)

// TaskStatus 任务状态
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"
	TaskStatusRunning   TaskStatus = "running"
	TaskStatusCompleted TaskStatus = "completed"
	TaskStatusFailed    TaskStatus = "failed"
	TaskStatusCancelled TaskStatus = "cancelled"
)

// TaskPriority 任务优先级
type TaskPriority int

const (
	TaskPriorityLow    TaskPriority = 1
	TaskPriorityNormal TaskPriority = 2
	TaskPriorityHigh   TaskPriority = 3
	TaskPriorityUrgent TaskPriority = 4
)

// ProxyStrategy 代理分配策略
type ProxyStrategy string

const (
	ProxyStrategyRoundRobin ProxyStrategy = "round_robin"
	ProxyStrategyLeastUsed  ProxyStrategy = "least_used"
	ProxyStrategyRandom     ProxyStrategy = "random"
	ProxyStrategyWeighted   ProxyStrategy = "weighted"
)

// Task 任务模型 (数据库版本)
type Task struct {
	ID           string     `json:"id" redis:"id" db:"id"`
	UserID       string     `json:"user_id" redis:"user_id" db:"user_id"`
	Name         string     `json:"name" redis:"name" db:"name"`
	Description  string     `json:"description,omitempty" redis:"description" db:"description"`
	Type         string     `json:"type" redis:"type" db:"type"`
	Status       TaskStatus `json:"status" redis:"status" db:"status"`
	Config       string     `json:"config,omitempty" redis:"config" db:"config"` // JSON string
	Result       string     `json:"result,omitempty" redis:"result" db:"result"` // JSON string
	Progress     int        `json:"progress" redis:"progress" db:"progress"`
	CreatedAt    time.Time  `json:"created_at" redis:"created_at" db:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at" redis:"updated_at" db:"updated_at"`
	StartedAt    *time.Time `json:"started_at,omitempty" redis:"started_at" db:"started_at"`
	CompletedAt  *time.Time `json:"completed_at,omitempty" redis:"completed_at" db:"completed_at"`
	ErrorMessage string     `json:"error_message,omitempty" redis:"error_message" db:"error_message"`
}

// LegacyTask 原有的任务模型 (Redis版本，保持向后兼容)
type LegacyTask struct {
	ID            string            `json:"id" redis:"id"`
	Name          string            `json:"name" redis:"name"`
	URL           string            `json:"url" redis:"url"`
	Method        string            `json:"method" redis:"method"`
	Headers       map[string]string `json:"headers" redis:"headers"`
	Body          string            `json:"body" redis:"body"`
	ProxyID       string            `json:"proxy_id" redis:"proxy_id"`
	ProxyStrategy ProxyStrategy     `json:"proxy_strategy" redis:"proxy_strategy"`
	Status        TaskStatus        `json:"status" redis:"status"`
	Priority      TaskPriority      `json:"priority" redis:"priority"`
	RetryCount    int               `json:"retry_count" redis:"retry_count"`
	MaxRetries    int               `json:"max_retries" redis:"max_retries"`
	Result        string            `json:"result" redis:"result"`
	Error         string            `json:"error" redis:"error"`
	CreatedAt     time.Time         `json:"created_at" redis:"created_at"`
	UpdatedAt     time.Time         `json:"updated_at" redis:"updated_at"`
	StartedAt     time.Time         `json:"started_at" redis:"started_at"`
	CompletedAt   time.Time         `json:"completed_at" redis:"completed_at"`
	Timeout       int               `json:"timeout" redis:"timeout"` // 秒
	UserID        string            `json:"user_id" redis:"user_id"`
}

// TaskRequest 创建任务请求
type TaskRequest struct {
	Name          string            `json:"name" binding:"required"`
	URL           string            `json:"url" binding:"required,url"`
	Method        string            `json:"method" binding:"required,oneof=GET POST PUT DELETE PATCH"`
	Headers       map[string]string `json:"headers,omitempty"`
	Body          string            `json:"body,omitempty"`
	ProxyStrategy ProxyStrategy     `json:"proxy_strategy" binding:"required,oneof=round_robin least_used random weighted"`
	Priority      TaskPriority      `json:"priority,omitempty"`
	MaxRetries    int               `json:"max_retries,omitempty"`
	Timeout       int               `json:"timeout,omitempty"`
}

// TaskResponse 任务响应
type TaskResponse struct {
	ID          string       `json:"id"`
	Name        string       `json:"name"`
	URL         string       `json:"url"`
	Method      string       `json:"method"`
	Status      TaskStatus   `json:"status"`
	Priority    TaskPriority `json:"priority"`
	RetryCount  int          `json:"retry_count"`
	Result      string       `json:"result"`
	Error       string       `json:"error"`
	CreatedAt   time.Time    `json:"created_at"`
	StartedAt   time.Time    `json:"started_at"`
	CompletedAt time.Time    `json:"completed_at"`
	ProxyID     string       `json:"proxy_id"`
}

// TaskStats 任务统计
type TaskStats struct {
	Total     int `json:"total"`
	Pending   int `json:"pending"`
	Running   int `json:"running"`
	Completed int `json:"completed"`
	Failed    int `json:"failed"`
	Cancelled int `json:"cancelled"`
}

// TaskResult 任务执行结果
type TaskResult struct {
	TaskID          string            `json:"task_id"`
	Status          TaskStatus        `json:"status"`
	StatusCode      int               `json:"status_code"`
	ResponseBody    string            `json:"response_body"`
	ResponseHeaders map[string]string `json:"response_headers"`
	Error           string            `json:"error,omitempty"`
	ProxyID         string            `json:"proxy_id"`
	Duration        int64             `json:"duration"` // 毫秒
	CompletedAt     time.Time         `json:"completed_at"`
}
