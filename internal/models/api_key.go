package models

import (
	"time"
)

// APIKey API密钥模型
type APIKey struct {
	ID                 string                 `json:"id" db:"id"`
	UserID             string                 `json:"user_id" db:"user_id"`
	Name               string                 `json:"name" db:"name"`
	KeyHash            string                 `json:"-" db:"key_hash"` // 不在JSON中暴露
	KeyPrefix          string                 `json:"key_prefix" db:"key_prefix"`
	FullAPIKey         *string                `json:"-" db:"full_api_key"` // 完整的API密钥，不在JSON中暴露
	Permissions        map[string]interface{} `json:"permissions" db:"permissions"`
	IsActive           bool                   `json:"is_active" db:"is_active"`
	ExpiresAt          *time.Time             `json:"expires_at" db:"expires_at"`
	LastUsedAt         *time.Time             `json:"last_used_at" db:"last_used_at"`
	UsageCount         int64                  `json:"usage_count" db:"usage_count"`
	RateLimitPerMinute int                    `json:"rate_limit_per_minute" db:"rate_limit_per_minute"`
	AllowedIPs         []string               `json:"allowed_ips" db:"allowed_ips"`
	Metadata           map[string]interface{} `json:"metadata" db:"metadata"`
	CreatedAt          time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt          time.Time              `json:"updated_at" db:"updated_at"`
}

// APIKeyInfo 前端API密钥信息（包含完整密钥）
type APIKeyInfo struct {
	ID                 string                 `json:"id"`
	Name               string                 `json:"name"`
	KeyPrefix          string                 `json:"key_prefix"`
	MaskedAPIKey       string                 `json:"masked_api_key"`
	APIKey             *string                `json:"api_key,omitempty"` // 完整的API密钥，可能为空
	Permissions        map[string]interface{} `json:"permissions"`
	IsActive           bool                   `json:"is_active"`
	ExpiresAt          *time.Time             `json:"expires_at"`
	LastUsedAt         *time.Time             `json:"last_used_at"`
	UsageCount         int64                  `json:"usage_count"`
	RateLimitPerMinute int                    `json:"rate_limit_per_minute"`
	CreatedAt          time.Time              `json:"created_at"`

	// 兼容旧版本前端字段
	APIKeyCreated    time.Time `json:"api_key_created"`
	APIKeyLastUsed   time.Time `json:"api_key_last_used"`
	APIKeyUsageCount int64     `json:"api_key_usage_count"`
}

// APIKeyResponse 创建API密钥时的响应（包含完整密钥）
type APIKeyResponse struct {
	APIKeyInfo
	APIKey string `json:"api_key"` // 完整的API密钥，仅在创建时返回
}

// CreateAPIKeyRequest 创建API密钥请求
type CreateAPIKeyRequest struct {
	Name               string                 `json:"name" binding:"required"`
	ExpiresInDays      *int                   `json:"expires_in_days"`
	Permissions        map[string]interface{} `json:"permissions"`
	RateLimitPerMinute *int                   `json:"rate_limit_per_minute"`
	AllowedIPs         []string               `json:"allowed_ips"`
}

// UpdateAPIKeyRequest 更新API密钥请求
type UpdateAPIKeyRequest struct {
	Name               *string                `json:"name"`
	IsActive           *bool                  `json:"is_active"`
	Permissions        map[string]interface{} `json:"permissions"`
	RateLimitPerMinute *int                   `json:"rate_limit_per_minute"`
	AllowedIPs         []string               `json:"allowed_ips"`
}

// APIKeyUsageLog API密钥使用记录
type APIKeyUsageLog struct {
	ID                string    `json:"id" db:"id"`
	APIKeyID          string    `json:"api_key_id" db:"api_key_id"`
	Endpoint          string    `json:"endpoint" db:"endpoint"`
	Method            string    `json:"method" db:"method"`
	IPAddress         string    `json:"ip_address" db:"ip_address"`
	UserAgent         string    `json:"user_agent" db:"user_agent"`
	ResponseStatus    int       `json:"response_status" db:"response_status"`
	ResponseTimeMs    int       `json:"response_time_ms" db:"response_time_ms"`
	RequestSizeBytes  int       `json:"request_size_bytes" db:"request_size_bytes"`
	ResponseSizeBytes int       `json:"response_size_bytes" db:"response_size_bytes"`
	CreatedAt         time.Time `json:"created_at" db:"created_at"`
}

// GetDecryptedKey 获取完整密钥（简化方案：直接返回存储的密钥）
func (ak *APIKey) GetDecryptedKey() *string {
	if ak.FullAPIKey == nil || *ak.FullAPIKey == "" {
		return nil
	}

	// 简化方案：直接返回存储的完整密钥
	return ak.FullAPIKey
}

// ToAPIKeyInfo 转换为前端API密钥信息
func (ak *APIKey) ToAPIKeyInfo() *APIKeyInfo {
	info := &APIKeyInfo{
		ID:                 ak.ID,
		Name:               ak.Name,
		KeyPrefix:          ak.KeyPrefix,
		MaskedAPIKey:       ak.GetMaskedKey(),
		APIKey:             ak.GetDecryptedKey(), // 包含完整密钥
		Permissions:        ak.Permissions,
		IsActive:           ak.IsActive,
		ExpiresAt:          ak.ExpiresAt,
		LastUsedAt:         ak.LastUsedAt,
		UsageCount:         ak.UsageCount,
		RateLimitPerMinute: ak.RateLimitPerMinute,
		CreatedAt:          ak.CreatedAt,

		// 兼容旧版本前端字段
		APIKeyCreated:    ak.CreatedAt,
		APIKeyUsageCount: ak.UsageCount,
	}

	if ak.LastUsedAt != nil {
		info.APIKeyLastUsed = *ak.LastUsedAt
	}

	return info
}

// GetMaskedKey 获取掩码后的API密钥
func (ak *APIKey) GetMaskedKey() string {
	if ak.KeyPrefix == "" {
		return "****"
	}
	return ak.KeyPrefix + "..." + "****"
}

// IsExpired 检查API密钥是否过期
func (ak *APIKey) IsExpired() bool {
	if ak.ExpiresAt == nil {
		return false
	}
	return ak.ExpiresAt.Before(time.Now())
}

// IsValid 检查API密钥是否有效
func (ak *APIKey) IsValid() bool {
	return ak.IsActive && !ak.IsExpired()
}
