package models

import (
	"encoding/json"
	"time"
)

// AlertSeverity 告警严重级别
type AlertSeverity string

const (
	AlertSeverityInfo     AlertSeverity = "info"
	AlertSeverityWarning  AlertSeverity = "warning"
	AlertSeverityCritical AlertSeverity = "critical"
)

// AlertStatus 告警状态
type AlertStatus string

const (
	AlertStatusFiring   AlertStatus = "firing"
	AlertStatusResolved AlertStatus = "resolved"
)

// AlertCondition 告警条件
type AlertCondition string

const (
	AlertConditionGreater      AlertCondition = ">"
	AlertConditionLess         AlertCondition = "<"
	AlertConditionGreaterEqual AlertCondition = ">="
	AlertConditionLessEqual    AlertCondition = "<="
	AlertConditionEqual        AlertCondition = "="
	AlertConditionNotEqual     AlertCondition = "!="
)

// NotificationChannel 通知渠道类型
type NotificationChannel string

const (
	NotificationChannelEmail   NotificationChannel = "email"
	NotificationChannelSMS     NotificationChannel = "sms"
	NotificationChannelWebhook NotificationChannel = "webhook"
	NotificationChannelSlack   NotificationChannel = "slack"
)

// AlertRule 告警规则
type AlertRule struct {
	ID                   string                         `json:"id" db:"id"`
	Name                 string                         `json:"name" db:"name"`
	Description          string                         `json:"description,omitempty" db:"description"`
	Metric               string                         `json:"metric" db:"metric"`
	Condition            AlertCondition                 `json:"condition" db:"condition"`
	Threshold            float64                        `json:"threshold" db:"threshold"`
	Duration             int                            `json:"duration" db:"duration"` // 持续时间(秒)
	Severity             AlertSeverity                  `json:"severity" db:"severity"`
	Enabled              bool                           `json:"enabled" db:"enabled"`
	NotificationChannels map[NotificationChannel]interface{} `json:"notification_channels" db:"notification_channels"`
	CreatedAt            time.Time                      `json:"created_at" db:"created_at"`
	UpdatedAt            time.Time                      `json:"updated_at" db:"updated_at"`
}

// Alert 告警记录
type Alert struct {
	ID           string        `json:"id" db:"id"`
	RuleID       string        `json:"rule_id" db:"rule_id"`
	MetricValue  float64       `json:"metric_value" db:"metric_value"`
	Status       AlertStatus   `json:"status" db:"status"`
	Message      string        `json:"message" db:"message"`
	FiredAt      time.Time     `json:"fired_at" db:"fired_at"`
	ResolvedAt   *time.Time    `json:"resolved_at,omitempty" db:"resolved_at"`
	NotificationSent bool      `json:"notification_sent" db:"notification_sent"`
	Metadata     json.RawMessage `json:"metadata,omitempty" db:"metadata"`
}

// AlertRuleRequest 告警规则创建请求
type AlertRuleRequest struct {
	Name                 string                         `json:"name" binding:"required"`
	Description          string                         `json:"description,omitempty"`
	Metric               string                         `json:"metric" binding:"required"`
	Condition            AlertCondition                 `json:"condition" binding:"required"`
	Threshold            float64                        `json:"threshold" binding:"required"`
	Duration             int                            `json:"duration,omitempty"`
	Severity             AlertSeverity                  `json:"severity" binding:"required"`
	Enabled              *bool                          `json:"enabled,omitempty"`
	NotificationChannels map[NotificationChannel]interface{} `json:"notification_channels,omitempty"`
}

// AlertRuleResponse 告警规则响应
type AlertRuleResponse struct {
	ID                   string                         `json:"id"`
	Name                 string                         `json:"name"`
	Description          string                         `json:"description,omitempty"`
	Metric               string                         `json:"metric"`
	Condition            AlertCondition                 `json:"condition"`
	Threshold            float64                        `json:"threshold"`
	Duration             int                            `json:"duration"`
	Severity             AlertSeverity                  `json:"severity"`
	Enabled              bool                           `json:"enabled"`
	NotificationChannels map[NotificationChannel]interface{} `json:"notification_channels,omitempty"`
	CreatedAt            time.Time                      `json:"created_at"`
	UpdatedAt            time.Time                      `json:"updated_at"`
}

// AlertResponse 告警响应
type AlertResponse struct {
	ID              string        `json:"id"`
	RuleID          string        `json:"rule_id"`
	RuleName        string        `json:"rule_name,omitempty"`
	MetricValue     float64       `json:"metric_value"`
	Status          AlertStatus   `json:"status"`
	Message         string        `json:"message"`
	Severity        AlertSeverity `json:"severity,omitempty"`
	FiredAt         time.Time     `json:"fired_at"`
	ResolvedAt      *time.Time    `json:"resolved_at,omitempty"`
	NotificationSent bool         `json:"notification_sent"`
	Duration        *int64        `json:"duration,omitempty"` // 持续时间(秒)
}

// EmailNotificationConfig 邮件通知配置
type EmailNotificationConfig struct {
	Recipients []string `json:"recipients"`
	Subject    string   `json:"subject,omitempty"`
	Template   string   `json:"template,omitempty"`
}

// SMSNotificationConfig 短信通知配置
type SMSNotificationConfig struct {
	PhoneNumbers []string `json:"phone_numbers"`
	Template     string   `json:"template,omitempty"`
}

// WebhookNotificationConfig Webhook通知配置
type WebhookNotificationConfig struct {
	URL     string            `json:"url"`
	Method  string            `json:"method,omitempty"`
	Headers map[string]string `json:"headers,omitempty"`
	Payload string            `json:"payload,omitempty"`
}

// SlackNotificationConfig Slack通知配置
type SlackNotificationConfig struct {
	WebhookURL string `json:"webhook_url"`
	Channel    string `json:"channel,omitempty"`
	Username   string `json:"username,omitempty"`
	IconEmoji  string `json:"icon_emoji,omitempty"`
}

// AlertMetrics 告警指标定义
var AlertMetrics = map[string]string{
	"proxy_availability_rate": "代理可用率",
	"proxy_response_time":     "代理响应时间",
	"proxy_failure_rate":      "代理失败率",
	"system_cpu_usage":        "系统CPU使用率",
	"system_memory_usage":     "系统内存使用率",
	"active_proxy_count":      "活跃代理数量",
	"failed_proxy_count":      "失败代理数量",
	"task_failure_rate":       "任务失败率",
	"api_error_rate":          "API错误率",
}

// ToResponse 转换为响应格式
func (r *AlertRule) ToResponse() *AlertRuleResponse {
	return &AlertRuleResponse{
		ID:                   r.ID,
		Name:                 r.Name,
		Description:          r.Description,
		Metric:               r.Metric,
		Condition:            r.Condition,
		Threshold:            r.Threshold,
		Duration:             r.Duration,
		Severity:             r.Severity,
		Enabled:              r.Enabled,
		NotificationChannels: r.NotificationChannels,
		CreatedAt:            r.CreatedAt,
		UpdatedAt:            r.UpdatedAt,
	}
}

// ToResponse 转换为响应格式
func (a *Alert) ToResponse(ruleName string, severity AlertSeverity) *AlertResponse {
	response := &AlertResponse{
		ID:              a.ID,
		RuleID:          a.RuleID,
		RuleName:        ruleName,
		MetricValue:     a.MetricValue,
		Status:          a.Status,
		Message:         a.Message,
		Severity:        severity,
		FiredAt:         a.FiredAt,
		ResolvedAt:      a.ResolvedAt,
		NotificationSent: a.NotificationSent,
	}

	// 计算持续时间
	if a.ResolvedAt != nil {
		duration := a.ResolvedAt.Unix() - a.FiredAt.Unix()
		response.Duration = &duration
	} else if a.Status == AlertStatusFiring {
		duration := time.Now().Unix() - a.FiredAt.Unix()
		response.Duration = &duration
	}

	return response
}

// IsActive 检查告警是否活跃
func (a *Alert) IsActive() bool {
	return a.Status == AlertStatusFiring
}

// GetDuration 获取告警持续时间
func (a *Alert) GetDuration() time.Duration {
	if a.ResolvedAt != nil {
		return a.ResolvedAt.Sub(a.FiredAt)
	}
	return time.Since(a.FiredAt)
}
