package models

import "time"

// Permission 权限模型
type Permission struct {
	ID          string    `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`                   // 权限名称，如 "proxy:read", "proxy:write"
	Resource    string    `json:"resource" db:"resource"`           // 资源类型，如 "proxy", "user", "task"
	Action      string    `json:"action" db:"action"`               // 操作类型，如 "read", "write", "delete"
	Description string    `json:"description" db:"description"`     // 权限描述
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// Role 角色模型（扩展版）
type Role struct {
	ID          string    `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`                   // 角色名称
	DisplayName string    `json:"display_name" db:"display_name"`   // 显示名称
	Description string    `json:"description" db:"description"`     // 角色描述
	IsSystem    bool      `json:"is_system" db:"is_system"`         // 是否为系统角色（不可删除）
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	CreatedBy   *string   `json:"created_by,omitempty" db:"created_by"`
}

// RolePermission 角色权限关联
type RolePermission struct {
	ID           string    `json:"id" db:"id"`
	RoleID       string    `json:"role_id" db:"role_id"`
	PermissionID string    `json:"permission_id" db:"permission_id"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	CreatedBy    *string   `json:"created_by,omitempty" db:"created_by"`
}

// UserRole 用户角色关联（支持多角色）
type UserRoleAssignment struct {
	ID         string    `json:"id" db:"id"`
	UserID     string    `json:"user_id" db:"user_id"`
	RoleID     string    `json:"role_id" db:"role_id"`
	AssignedAt time.Time `json:"assigned_at" db:"assigned_at"`
	AssignedBy *string   `json:"assigned_by,omitempty" db:"assigned_by"`
	ExpiresAt  *time.Time `json:"expires_at,omitempty" db:"expires_at"` // 角色过期时间
}

// UserPermission 用户直接权限（覆盖角色权限）
type UserPermission struct {
	ID           string    `json:"id" db:"id"`
	UserID       string    `json:"user_id" db:"user_id"`
	PermissionID string    `json:"permission_id" db:"permission_id"`
	Granted      bool      `json:"granted" db:"granted"`             // true=授予，false=拒绝
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	CreatedBy    *string   `json:"created_by,omitempty" db:"created_by"`
	ExpiresAt    *time.Time `json:"expires_at,omitempty" db:"expires_at"`
}

// Team 团队模型
type Team struct {
	ID          string    `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	CreatedBy   *string   `json:"created_by,omitempty" db:"created_by"`
}

// TeamMember 团队成员
type TeamMember struct {
	ID       string    `json:"id" db:"id"`
	TeamID   string    `json:"team_id" db:"team_id"`
	UserID   string    `json:"user_id" db:"user_id"`
	RoleID   string    `json:"role_id" db:"role_id"`           // 在团队中的角色
	JoinedAt time.Time `json:"joined_at" db:"joined_at"`
	AddedBy  *string   `json:"added_by,omitempty" db:"added_by"`
}

// ResourceAccess 资源访问控制
type ResourceAccess struct {
	ID         string    `json:"id" db:"id"`
	ResourceID string    `json:"resource_id" db:"resource_id"`     // 资源ID（如代理ID）
	ResourceType string  `json:"resource_type" db:"resource_type"` // 资源类型
	SubjectID  string    `json:"subject_id" db:"subject_id"`       // 主体ID（用户或团队）
	SubjectType string   `json:"subject_type" db:"subject_type"`   // 主体类型（user/team）
	Permission string    `json:"permission" db:"permission"`       // 权限类型
	CreatedAt  time.Time `json:"created_at" db:"created_at"`
	CreatedBy  *string   `json:"created_by,omitempty" db:"created_by"`
}

// 权限常量定义
const (
	// 资源类型
	ResourceProxy  = "proxy"
	ResourceUser   = "user"
	ResourceTask   = "task"
	ResourceTag    = "tag"
	ResourceTeam   = "team"
	ResourceSystem = "system"

	// 操作类型
	ActionRead   = "read"
	ActionWrite  = "write"
	ActionDelete = "delete"
	ActionExecute = "execute"
	ActionManage = "manage"

	// 预定义权限
	PermissionProxyRead    = "proxy:read"
	PermissionProxyWrite   = "proxy:write"
	PermissionProxyDelete  = "proxy:delete"
	PermissionProxyManage  = "proxy:manage"
	PermissionUserRead     = "user:read"
	PermissionUserWrite    = "user:write"
	PermissionUserDelete   = "user:delete"
	PermissionUserManage   = "user:manage"
	PermissionTaskRead     = "task:read"
	PermissionTaskWrite    = "task:write"
	PermissionTaskDelete   = "task:delete"
	PermissionTaskExecute  = "task:execute"
	PermissionTagRead      = "tag:read"
	PermissionTagWrite     = "tag:write"
	PermissionTagDelete    = "tag:delete"
	PermissionTeamRead     = "team:read"
	PermissionTeamWrite    = "team:write"
	PermissionTeamDelete   = "team:delete"
	PermissionTeamManage   = "team:manage"
	PermissionSystemRead   = "system:read"
	PermissionSystemWrite  = "system:write"
	PermissionSystemManage = "system:manage"

	// 预定义角色
	RoleSuperAdmin = "super_admin"
	RoleAdmin      = "admin"
	RoleUser       = "user"
	RoleGuest      = "guest"
	RoleTeamLead   = "team_lead"
	RoleTeamMember = "team_member"
)

// 请求类型
type RoleRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=50"`
	DisplayName string `json:"display_name" binding:"required,min=1,max=100"`
	Description string `json:"description,omitempty"`
}

type PermissionRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	Resource    string `json:"resource" binding:"required,min=1,max=50"`
	Action      string `json:"action" binding:"required,min=1,max=50"`
	Description string `json:"description,omitempty"`
}

type AssignRoleRequest struct {
	UserID    string     `json:"user_id" binding:"required"`
	RoleID    string     `json:"role_id" binding:"required"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
}

type AssignPermissionRequest struct {
	UserID       string     `json:"user_id" binding:"required"`
	PermissionID string     `json:"permission_id" binding:"required"`
	Granted      bool       `json:"granted"`
	ExpiresAt    *time.Time `json:"expires_at,omitempty"`
}

type TeamRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	Description string `json:"description,omitempty"`
}

type TeamMemberRequest struct {
	UserID string `json:"user_id" binding:"required"`
	RoleID string `json:"role_id" binding:"required"`
}

// UserWithRoles 带角色信息的用户
type UserWithRoles struct {
	User
	Roles       []*Role       `json:"roles,omitempty"`
	Permissions []*Permission `json:"permissions,omitempty"`
	Teams       []*Team       `json:"teams,omitempty"`
}

// RoleWithPermissions 带权限信息的角色
type RoleWithPermissions struct {
	Role
	Permissions []*Permission `json:"permissions,omitempty"`
}

// TeamWithMembers 带成员信息的团队
type TeamWithMembers struct {
	Team
	Members []*UserWithRoles `json:"members,omitempty"`
}
