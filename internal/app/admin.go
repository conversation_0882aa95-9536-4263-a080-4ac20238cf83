package app

import (
	"context"
	"fmt"
	"os"
	"time"

	"proxyFlow/internal/auth"
	"proxyFlow/internal/models"
	"proxyFlow/internal/repository"
	"proxyFlow/pkg/config"

	"github.com/sirupsen/logrus"
)

// initializeAdminUser 初始化管理员用户
func initializeAdminUser(ctx context.Context, repoManager repository.RepositoryManager, cfg *config.Config, logger *logrus.Logger) error {
	// 从环境变量获取管理员账户信息
	adminUsername := os.Getenv("PROXY_MANAGER_ADMIN_USERNAME")
	adminPassword := os.Getenv("PROXY_MANAGER_ADMIN_PASSWORD")

	if adminUsername == "" {
		adminUsername = "admin" // 默认用户名
	}

	if adminPassword == "" {
		adminPassword = "admin123" // 默认密码
	}

	// 检查管理员用户是否已存在
	existingUser, err := repoManager.GetUser().GetByUsername(ctx, adminUsername)
	if err != nil {
		return fmt.Errorf("failed to check existing admin user: %w", err)
	}

	if existingUser != nil {
		logger.WithField("username", adminUsername).Info("Admin user already exists, skipping creation")
		return nil
	}

	// 哈希密码
	hashedPassword, err := auth.HashPassword(adminPassword)
	if err != nil {
		return fmt.Errorf("failed to hash admin password: %w", err)
	}

	// 创建管理员用户（使用新API密钥系统）
	now := time.Now()
	adminUser := &models.User{
		Username:  adminUsername,
		Email:     adminUsername + "@proxymanager.local", // 默认邮箱
		Password:  hashedPassword,
		Role:      models.UserRoleAdmin,
		Status:    models.UserStatusActive,
		CreatedAt: now,
		UpdatedAt: now,
		// 不再设置旧版本的API密钥字段
		APIKey:           "",
		APIKeyCreated:    time.Time{},
		APIKeyLastUsed:   time.Time{},
		APIKeyUsageCount: 0,
	}

	// 保存管理员用户
	if err := repoManager.GetUser().Create(ctx, adminUser); err != nil {
		return fmt.Errorf("failed to create admin user: %w", err)
	}

	// 为管理员创建默认API密钥（使用新系统）
	if err := createDefaultAPIKeyForAdmin(ctx, repoManager, adminUser.ID, logger); err != nil {
		logger.WithError(err).Warn("Failed to create default API key for admin user")
		// 不返回错误，因为用户已创建成功
	}

	logger.WithFields(logrus.Fields{
		"username": adminUsername,
		"user_id":  adminUser.ID,
	}).Info("Admin user created successfully")

	return nil
}

// createDefaultAPIKeyForAdmin 为管理员创建默认API密钥
func createDefaultAPIKeyForAdmin(ctx context.Context, repoManager repository.RepositoryManager, userID string, logger *logrus.Logger) error {
	// 生成API密钥
	apiKeyString, err := auth.GenerateAPIKey()
	if err != nil {
		return fmt.Errorf("failed to generate API key: %w", err)
	}

	// 计算密钥哈希
	hash, err := auth.HashPassword(apiKeyString)
	if err != nil {
		return fmt.Errorf("failed to hash API key: %w", err)
	}

	// 提取密钥前缀（前8个字符）
	keyPrefix := ""
	if len(apiKeyString) >= 8 {
		keyPrefix = apiKeyString[:8]
	}

	// 创建API密钥记录
	now := time.Now()
	apiKey := &models.APIKey{
		UserID:     userID,
		Name:       "Default Admin API Key",
		KeyHash:    hash,
		KeyPrefix:  keyPrefix,
		FullAPIKey: &apiKeyString,
		Permissions: map[string]interface{}{
			"proxies": []string{"read", "write", "admin"},
			"tasks":   []string{"read", "write", "admin"},
			"users":   []string{"read", "write", "admin"},
		},
		IsActive:           true,
		ExpiresAt:          nil, // 永不过期
		UsageCount:         0,
		RateLimitPerMinute: 10000,      // 高速率限制
		AllowedIPs:         []string{}, // 无IP限制
		Metadata: map[string]interface{}{
			"created_by": "system",
			"purpose":    "default_admin_key",
		},
		CreatedAt: now,
		UpdatedAt: now,
	}

	// 保存API密钥
	if err := repoManager.GetAPIKey().Create(ctx, apiKey); err != nil {
		return fmt.Errorf("failed to create API key: %w", err)
	}

	logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"api_key_id": apiKey.ID,
		"key_prefix": keyPrefix,
	}).Info("Default API key created for admin user")

	return nil
}
