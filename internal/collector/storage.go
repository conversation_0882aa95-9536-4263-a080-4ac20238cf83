package collector

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"proxyFlow/internal/models"

	"github.com/sirupsen/logrus"
)

// saveProxies 保存代理到数据库
func (c *ProxyCollector) saveProxies(ctx context.Context, proxies []*CollectedProxy) (savedCount, duplicateCount int, err error) {
	c.logger.WithField("proxy_count", len(proxies)).Info("Starting to save proxies to database")

	var saved, duplicates int

	for _, collectedProxy := range proxies {
		// 检查是否已存在
		existing, err := c.proxyRepo.GetByHostPort(ctx, collectedProxy.Host, collectedProxy.Port)
		if err != nil {
			c.logger.WithError(err).WithFields(logrus.Fields{
				"host": collectedProxy.Host,
				"port": collectedProxy.Port,
			}).Error("Failed to check existing proxy")
			continue
		}

		if existing != nil {
			// 代理已存在，更新信息
			if err := c.updateExistingProxy(ctx, existing, collectedProxy); err != nil {
				c.logger.WithError(err).WithFields(logrus.Fields{
					"host": collectedProxy.Host,
					"port": collectedProxy.Port,
				}).Error("Failed to update existing proxy")
				continue
			}
			duplicates++
		} else {
			// 创建新代理
			if err := c.createNewProxy(ctx, collectedProxy); err != nil {
				c.logger.WithError(err).WithFields(logrus.Fields{
					"host": collectedProxy.Host,
					"port": collectedProxy.Port,
				}).Error("Failed to create new proxy")
				continue
			}
			saved++
		}
	}

	c.logger.WithFields(logrus.Fields{
		"saved_count":     saved,
		"duplicate_count": duplicates,
		"total_processed": len(proxies),
	}).Info("Finished saving proxies to database")

	return saved, duplicates, nil
}

// createNewProxy 创建新代理
func (c *ProxyCollector) createNewProxy(ctx context.Context, collectedProxy *CollectedProxy) error {
	now := time.Now()

	// 计算质量分数
	responseTime := int64(0)
	if collectedProxy.ResponseTime != nil {
		responseTime = *collectedProxy.ResponseTime
	}

	qualityScore := c.calculateQualityScore(responseTime, models.AnonymityLevelUnknown, collectedProxy.Type)
	if collectedProxy.Verified {
		// 如果有验证结果，使用更准确的匿名级别
		qualityScore = c.calculateQualityScore(responseTime, collectedProxy.AnonymityLevel, collectedProxy.Type)
	}

	proxy := &models.Proxy{
		Host:         collectedProxy.Host,
		Port:         collectedProxy.Port,
		Type:         collectedProxy.Type,
		Username:     collectedProxy.Username,
		Password:     collectedProxy.Password,
		Status:       collectedProxy.Status,
		Failures:     0,
		LastCheck:    &now,
		CreatedAt:    now,
		UpdatedAt:    now,
		UseCount:     0,
		ResponseTime: collectedProxy.ResponseTime,
		Weight:       1, // 默认权重

		// 质量评估信息
		QualityScore:     qualityScore,
		SpeedScore:       c.calculateSpeedScore(*collectedProxy.ResponseTime),
		StabilityScore:   0.5,       // 新代理默认稳定性分数
		AnonymityLevel:   "unknown", // 初始匿名级别
		ReliabilityScore: 0.5,       // 新代理默认可靠性分数
		LastQualityCheck: &now,

		// 场景化分组信息
		Scenario: "auto_collected", // 标记为自动采集
		Priority: 1,                // 默认优先级
	}

	// 如果验证成功，更新最后成功时间
	if collectedProxy.Verified {
		proxy.LastSuccess = &now
	}

	return c.proxyRepo.Create(ctx, proxy)
}

// updateExistingProxy 更新现有代理
func (c *ProxyCollector) updateExistingProxy(ctx context.Context, existing *models.Proxy, collectedProxy *CollectedProxy) error {
	now := time.Now()

	// 更新基本信息
	existing.UpdatedAt = now
	existing.LastCheck = &now

	// 如果新采集的代理验证成功
	if collectedProxy.Verified {
		existing.Status = models.ProxyStatusActive
		existing.LastSuccess = &now
		existing.Failures = 0 // 重置失败次数

		// 更新响应时间
		if collectedProxy.ResponseTime != nil {
			existing.ResponseTime = collectedProxy.ResponseTime
			existing.SpeedScore = c.calculateSpeedScore(*collectedProxy.ResponseTime)
		}

		// 更新质量分数
		responseTime := int64(0)
		if collectedProxy.ResponseTime != nil {
			responseTime = *collectedProxy.ResponseTime
		}
		existing.QualityScore = c.calculateQualityScore(responseTime, models.AnonymityLevel(existing.AnonymityLevel), collectedProxy.Type)
		existing.LastQualityCheck = &now

		// 更新可靠性分数（基于历史成功率）
		existing.ReliabilityScore = c.calculateReliabilityScore(existing)
	} else {
		// 验证失败，增加失败次数
		existing.Failures++
		if existing.Failures >= 3 {
			existing.Status = models.ProxyStatusFailed
		}
	}

	// 更新认证信息（如果有变化）
	if collectedProxy.Username != "" {
		existing.Username = collectedProxy.Username
		existing.Password = collectedProxy.Password
	}

	return c.proxyRepo.Update(ctx, existing)
}

// calculateSpeedScore 计算速度分数
func (c *ProxyCollector) calculateSpeedScore(responseTime int64) float64 {
	// 基于响应时间计算速度分数 (0-1)
	if responseTime <= 500 {
		return 1.0
	} else if responseTime <= 1000 {
		return 0.9
	} else if responseTime <= 2000 {
		return 0.8
	} else if responseTime <= 3000 {
		return 0.7
	} else if responseTime <= 5000 {
		return 0.6
	} else if responseTime <= 8000 {
		return 0.4
	} else if responseTime <= 10000 {
		return 0.2
	} else {
		return 0.1
	}
}

// calculateReliabilityScore 计算可靠性分数
func (c *ProxyCollector) calculateReliabilityScore(proxy *models.Proxy) float64 {
	// 基于使用次数和失败次数计算可靠性
	totalAttempts := proxy.UseCount + int64(proxy.Failures)
	if totalAttempts == 0 {
		return 0.5 // 默认分数
	}

	successRate := float64(proxy.UseCount) / float64(totalAttempts)
	return successRate
}

// ensureDataDir 确保数据目录存在
func (c *ProxyCollector) ensureDataDir() error {
	dir := filepath.Dir(c.config.RawDataPath)
	return os.MkdirAll(dir, 0755)
}

// saveJSONFile 保存JSON文件
func (c *ProxyCollector) saveJSONFile(filepath string, data interface{}) error {
	file, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(data); err != nil {
		return fmt.Errorf("failed to encode JSON: %w", err)
	}

	return nil
}

// CleanupOldProxies 清理旧的无效代理
func (c *ProxyCollector) CleanupOldProxies(ctx context.Context, maxAge time.Duration) error {
	c.logger.Info("Starting cleanup of old invalid proxies")

	// 这里需要扩展 ProxyFilters 来支持时间过滤
	// 暂时使用简单的方法
	// TODO: 实现基于时间的代理清理逻辑

	c.logger.Info("Proxy cleanup completed")
	return nil
}

// GetCollectionHistory 获取采集历史
func (c *ProxyCollector) GetCollectionHistory(limit int) ([]*CollectionStats, error) {
	// 这里可以实现从文件或数据库读取历史记录
	// 暂时返回当前统计
	stats := c.GetStats()
	return []*CollectionStats{stats}, nil
}

// UpdateProxyGeoLocation 更新代理地理位置信息
func (c *ProxyCollector) UpdateProxyGeoLocation(ctx context.Context, proxy *models.Proxy) error {
	if !c.config.EnableGeoLocation {
		return nil
	}

	// 这里可以集成地理位置API服务
	// 例如 IP-API, GeoIP2 等
	// 暂时跳过实现

	c.logger.WithFields(logrus.Fields{
		"host": proxy.Host,
		"port": proxy.Port,
	}).Debug("Geo-location update skipped (not implemented)")

	return nil
}

// ValidateProxyQuality 验证代理质量
func (c *ProxyCollector) ValidateProxyQuality(proxy *CollectedProxy) bool {
	if !proxy.Verified {
		return false
	}

	if proxy.ResponseTime == nil {
		return false
	}

	// 检查响应时间是否在可接受范围内
	if *proxy.ResponseTime > 10000 { // 10秒
		return false
	}

	// 计算质量分数
	responseTime := int64(0)
	if proxy.ResponseTime != nil {
		responseTime = *proxy.ResponseTime
	}
	qualityScore := c.calculateQualityScore(responseTime, proxy.AnonymityLevel, proxy.Type)

	// 检查是否达到最低质量要求
	return qualityScore >= c.config.MinQualityScore
}
