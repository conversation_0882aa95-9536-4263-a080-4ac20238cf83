package collector

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"proxyFlow/internal/models"

	"github.com/sirupsen/logrus"
)

type ProxyVerificationStatus string

const (
	ProxyStatusUnknown    ProxyVerificationStatus = "unknown"
	ProxyStatusValid      ProxyVerificationStatus = "valid"
	ProxyStatusInvalid    ProxyVerificationStatus = "invalid"
	ProxyStatusProcessing ProxyVerificationStatus = "processing"
)

type CachedProxyEntry struct {
	Host           string                  `json:"host"`
	Port           int                     `json:"port"`
	Type           models.ProxyType        `json:"type"`
	Status         ProxyVerificationStatus `json:"status"`
	LastVerified   time.Time               `json:"last_verified"`
	ResponseTime   *int64                  `json:"response_time,omitempty"`
	Error          string                  `json:"error,omitempty"`
	AnonymityLevel models.AnonymityLevel   `json:"anonymity_level,omitempty"`
	QualityScore   float64                 `json:"quality_score,omitempty"`

	// 用于统计的字段
	VerifyCount  int       `json:"verify_count"`  // 验证次数
	SuccessCount int       `json:"success_count"` // 成功次数
	FirstSeen    time.Time `json:"first_seen"`    // 首次发现时间
	LastUpdated  time.Time `json:"last_updated"`  // 最后更新时间
}

// Cache 统一的代理缓存系统
type Cache struct {
	// 配置
	cachePath        string
	validDuration    time.Duration // 短期缓存有效期（如30分钟）
	progressDuration time.Duration // 进度缓存有效期（如24小时）

	// 数据存储
	entries map[string]*CachedProxyEntry
	mutex   sync.RWMutex
	logger  *logrus.Logger

	// 统计信息
	stats CacheStats

	// 进度追踪
	sessionInfo SessionInfo
}

// CacheStats 缓存统计信息
type CacheStats struct {
	TotalEntries   int       `json:"total_entries"`
	ValidEntries   int       `json:"valid_entries"`
	InvalidEntries int       `json:"invalid_entries"`
	ExpiredEntries int       `json:"expired_entries"`
	CacheHits      int64     `json:"cache_hits"`
	CacheMisses    int64     `json:"cache_misses"`
	LastCleanup    time.Time `json:"last_cleanup"`
	FileSize       int64     `json:"file_size_bytes"`
}

// SessionInfo 会话进度信息
type SessionInfo struct {
	SessionID        string    `json:"session_id"`
	StartTime        time.Time `json:"start_time"`
	LastUpdateTime   time.Time `json:"last_update_time"`
	TotalProxies     int       `json:"total_proxies"`
	ProcessedProxies int       `json:"processed_proxies"`
	ValidProxies     int       `json:"valid_proxies"`
	InvalidProxies   int       `json:"invalid_proxies"`
	BatchSize        int       `json:"batch_size"`
	Version          string    `json:"version"`
}

// CacheData 缓存文件数据结构
type CacheData struct {
	Entries     map[string]*CachedProxyEntry `json:"entries"`
	Stats       CacheStats                   `json:"stats"`
	SessionInfo SessionInfo                  `json:"session_info"`
	CreatedAt   time.Time                    `json:"created_at"`
	UpdatedAt   time.Time                    `json:"updated_at"`
	Version     string                       `json:"version"`
}

// NewCache 创建统一的代理缓存
func NewCache(cachePath string, validDuration, progressDuration time.Duration, logger *logrus.Logger) *Cache {
	cache := &Cache{
		cachePath:        cachePath,
		validDuration:    validDuration,
		progressDuration: progressDuration,
		entries:          make(map[string]*CachedProxyEntry),
		logger:           logger,
		stats: CacheStats{
			LastCleanup: time.Now(),
		},
		sessionInfo: SessionInfo{
			SessionID: fmt.Sprintf("session_%d", time.Now().UnixNano()),
			StartTime: time.Now(),
			Version:   "2.0",
		},
	}

	// 确保缓存目录存在
	if err := os.MkdirAll(filepath.Dir(cachePath), 0755); err != nil {
		logger.WithError(err).Warn("Failed to create cache directory")
	}

	// 加载现有缓存
	cache.Load()

	return cache
}

// Load 从文件加载缓存
func (c *Cache) Load() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	data, err := os.ReadFile(c.cachePath)
	if err != nil {
		if !os.IsNotExist(err) {
			c.logger.WithError(err).Warn("Failed to read cache file")
		}
		return nil // 文件不存在或读取失败，使用空缓存
	}

	var cacheData CacheData
	if err := json.Unmarshal(data, &cacheData); err != nil {
		c.logger.WithError(err).Error("Failed to unmarshal cache data, starting fresh")
		return nil
	}

	// 恢复数据（不再检查整体缓存过期，而是在条目级别处理）
	c.entries = cacheData.Entries
	c.stats = cacheData.Stats
	c.sessionInfo = cacheData.SessionInfo

	// 清理过期条目（区分短期有效性缓存和长期进度缓存）
	c.cleanupExpiredEntriesSelectively()

	c.logger.WithFields(logrus.Fields{
		"total_entries":   len(c.entries),
		"valid_entries":   c.stats.ValidEntries,
		"invalid_entries": c.stats.InvalidEntries,
		"session_id":      c.sessionInfo.SessionID,
		"cache_age":       time.Since(cacheData.UpdatedAt),
	}).Info("Cache loaded successfully")

	return nil
}

// Save 保存缓存到文件
func (c *Cache) Save() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 更新统计信息
	c.updateStats()

	cacheData := CacheData{
		Entries:     c.entries,
		Stats:       c.stats,
		SessionInfo: c.sessionInfo,
		CreatedAt:   c.sessionInfo.StartTime,
		UpdatedAt:   time.Now(),
		Version:     "2.0",
	}

	data, err := json.MarshalIndent(cacheData, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal cache data: %w", err)
	}

	if err := os.WriteFile(c.cachePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write cache file: %w", err)
	}

	c.stats.FileSize = int64(len(data))

	c.logger.WithFields(logrus.Fields{
		"entries_count": len(c.entries),
		"file_size":     c.stats.FileSize,
		"cache_path":    c.cachePath,
	}).Debug("Cache saved successfully")

	return nil
}

// GetProxyStatus 获取代理状态
func (c *Cache) GetProxyStatus(host string, port int) (ProxyVerificationStatus, *CachedProxyEntry) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	key := fmt.Sprintf("%s:%d", host, port)
	entry, exists := c.entries[key]
	if !exists {
		c.stats.CacheMisses++
		return ProxyStatusUnknown, nil
	}

	// 检查短期缓存是否过期
	if time.Since(entry.LastVerified) > c.validDuration {
		c.stats.CacheMisses++
		return ProxyStatusUnknown, entry // 返回过期的条目供参考
	}

	c.stats.CacheHits++
	return entry.Status, entry
}

// IsValid 检查代理是否有效（兼容旧接口）
func (c *Cache) IsValid(host string, port int) bool {
	status, _ := c.GetProxyStatus(host, port)
	return status == ProxyStatusValid
}

// UpdateProxyResult 更新代理验证结果
func (c *Cache) UpdateProxyResult(proxy *CollectedProxy, isValid bool, responseTime *int64, error string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	key := fmt.Sprintf("%s:%d", proxy.Host, proxy.Port)
	now := time.Now()

	entry, exists := c.entries[key]
	if !exists {
		entry = &CachedProxyEntry{
			Host:        proxy.Host,
			Port:        proxy.Port,
			Type:        proxy.Type,
			FirstSeen:   now,
			VerifyCount: 0,
		}
		c.entries[key] = entry
	}

	// 更新验证结果
	entry.LastVerified = now
	entry.LastUpdated = now
	entry.ResponseTime = responseTime
	entry.Error = error
	entry.VerifyCount++

	if isValid {
		entry.Status = ProxyStatusValid
		entry.SuccessCount++
		// 清除错误信息
		entry.Error = ""
	} else {
		entry.Status = ProxyStatusInvalid
	}

	// 更新匿名级别和质量分数（如果有的话）
	if proxy.AnonymityLevel != "" {
		entry.AnonymityLevel = proxy.AnonymityLevel
	}
	entry.QualityScore = proxy.QualityScore

}

func (c *Cache) SaveAsync() {
	go func() {
		if err := c.Save(); err != nil {
			c.logger.WithError(err).Debug("Failed to save cache asynchronously")
		}
	}()
}

// Set 设置代理状态（兼容旧接口）
func (c *Cache) Set(host string, port int, isValid bool) {
	proxy := &CollectedProxy{
		Host: host,
		Port: port,
		Type: models.ProxyTypeHTTP, // 默认类型
	}

	var responseTime *int64
	if isValid {
		defaultTime := int64(1000) // 默认1秒响应时间
		responseTime = &defaultTime
	}

	c.UpdateProxyResult(proxy, isValid, responseTime, "")
}

// IsProcessed 检查代理是否已被处理（断点续传支持）
func (c *Cache) IsProcessed(host string, port int) bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	key := fmt.Sprintf("%s:%d", host, port)
	entry, exists := c.entries[key]
	if !exists {
		return false
	}

	// 检查进度缓存是否过期（只有超过长期进度缓存期限才认为未处理）
	if time.Since(entry.LastUpdated) > c.progressDuration {
		return false
	}

	// 只要条目存在且在进度缓存期内，就认为已处理过
	// 即使短期有效性过期（Status 变为 Unknown），也算已处理
	return entry.Status != ProxyStatusUnknown || entry.VerifyCount > 0
}

// GetValidProxies 获取所有有效的代理（用于断点续传）
func (c *Cache) GetValidProxies() []*CollectedProxy {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	var validProxies []*CollectedProxy
	for _, entry := range c.entries {
		// 只返回在进度缓存期内的有效代理
		if entry.Status == ProxyStatusValid &&
			time.Since(entry.LastUpdated) <= c.progressDuration {

			proxy := &CollectedProxy{
				Host:           entry.Host,
				Port:           entry.Port,
				Type:           entry.Type,
				Verified:       true,
				Status:         models.ProxyStatusActive,
				ResponseTime:   entry.ResponseTime,
				AnonymityLevel: entry.AnonymityLevel,
				QualityScore:   entry.QualityScore,
			}
			validProxies = append(validProxies, proxy)
		}
	}

	return validProxies
}

// UpdateSessionProgress 更新会话进度
func (c *Cache) UpdateSessionProgress(totalProxies, processedProxies, validProxies, invalidProxies int) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.sessionInfo.TotalProxies = totalProxies
	c.sessionInfo.ProcessedProxies = processedProxies
	c.sessionInfo.ValidProxies = validProxies
	c.sessionInfo.InvalidProxies = invalidProxies
	c.sessionInfo.LastUpdateTime = time.Now()
}

// GetSessionProgress 获取会话进度
func (c *Cache) GetSessionProgress() SessionInfo {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.sessionInfo
}

// Clear 清空所有缓存
func (c *Cache) Clear() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.entries = make(map[string]*CachedProxyEntry)
	c.stats = CacheStats{LastCleanup: time.Now()}
	c.sessionInfo = SessionInfo{
		SessionID: fmt.Sprintf("session_%d", time.Now().UnixNano()),
		StartTime: time.Now(),
		Version:   "2.0",
	}

	c.logger.Info("Cache cleared")
}

// ClearFile 清空缓存并删除文件
func (c *Cache) ClearFile() error {
	c.Clear()

	if err := os.Remove(c.cachePath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to remove cache file: %w", err)
	}

	c.logger.WithField("cache_path", c.cachePath).Info("Cache file removed")
	return nil
}

// GetSize 获取缓存大小
func (c *Cache) GetSize() int {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return len(c.entries)
}

// GetStats 获取缓存统计信息
func (c *Cache) GetStats() CacheStats {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	stats := c.stats
	stats.TotalEntries = len(c.entries)
	return stats
}

// cleanupExpiredEntriesSelectively 选择性清理过期条目（区分短期和长期缓存）
func (c *Cache) cleanupExpiredEntriesSelectively() {
	now := time.Now()
	expiredCount := 0
	progressExpiredCount := 0

	for key, entry := range c.entries {
		// 只有当条目超过长期进度缓存期限时才完全删除
		// 这样可以保留断点续验所需的进度信息
		if now.Sub(entry.LastUpdated) > c.progressDuration {
			delete(c.entries, key)
			progressExpiredCount++
		} else if now.Sub(entry.LastVerified) > c.validDuration {
			// 如果只是短期有效性过期，保留条目但标记为未知状态
			// 这样 IsProcessed 仍然可以识别已处理的代理
			if entry.Status == ProxyStatusValid {
				entry.Status = ProxyStatusUnknown
				expiredCount++
			}
		}
	}

	if expiredCount > 0 || progressExpiredCount > 0 {
		c.logger.WithFields(logrus.Fields{
			"validity_expired_count": expiredCount,
			"progress_expired_count": progressExpiredCount,
		}).Debug("Cleaned up expired cache entries")
	}

	c.stats.ExpiredEntries += progressExpiredCount
	c.stats.LastCleanup = now
}

// updateStats 更新统计信息（内部方法）
func (c *Cache) updateStats() {
	c.stats.TotalEntries = len(c.entries)
	c.stats.ValidEntries = 0
	c.stats.InvalidEntries = 0

	for _, entry := range c.entries {
		switch entry.Status {
		case ProxyStatusValid:
			c.stats.ValidEntries++
		case ProxyStatusInvalid:
			c.stats.InvalidEntries++
		}
	}
}
