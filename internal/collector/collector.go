package collector

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"proxyFlow/internal/models"
	"proxyFlow/internal/repository"
	"proxyFlow/pkg/config"
	"proxyFlow/pkg/database"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/sirupsen/logrus"
)

// ProxySource 代理源配置
type ProxySource struct {
	HTTP   []string `json:"http"`
	HTTPS  []string `json:"https"`
	SOCKS4 []string `json:"socks4"`
	SOCKS5 []string `json:"socks5"`
	VMESS  []string `json:"vmess"`
	VLESS  []string `json:"vless"`
}

// CollectedProxy 采集到的代理数据
type CollectedProxy struct {
	Host           string                `json:"host"`
	Port           int                   `json:"port"`
	Type           models.ProxyType      `json:"type"`
	Username       string                `json:"username,omitempty"`
	Password       string                `json:"password,omitempty"`
	Source         string                `json:"source"`
	CollectedAt    time.Time             `json:"collected_at"`
	Verified       bool                  `json:"verified"`
	ResponseTime   *int64                `json:"response_time,omitempty"`
	Status         models.ProxyStatus    `json:"status"`
	Error          string                `json:"error,omitempty"`
	QualityScore   float64               `json:"quality_score"`
	AnonymityLevel models.AnonymityLevel `json:"anonymity_level"`
}

// CollectionStats 采集统计
type CollectionStats struct {
	TotalSources   int       `json:"total_sources"`
	SuccessSources int       `json:"success_sources"`
	TotalProxies   int       `json:"total_proxies"`
	ValidProxies   int       `json:"valid_proxies"`
	DuplicateCount int       `json:"duplicate_count"`
	StartTime      time.Time `json:"start_time"`
	EndTime        time.Time `json:"end_time"`
	Duration       string    `json:"duration"`
	LastCollection time.Time `json:"last_collection"`

	// 新增进度监控字段
	CurrentPhase    string  `json:"current_phase"`    // 当前阶段
	ProcessedCount  int64   `json:"processed_count"`  // 已处理数量
	TotalCount      int64   `json:"total_count"`      // 总数量
	ProgressPercent float64 `json:"progress_percent"` // 进度百分比
	Speed           float64 `json:"speed"`            // 处理速度(个/秒)
	ETA             string  `json:"eta"`              // 预计完成时间

	// 详细统计
	SuccessRate  float64 `json:"success_rate"`  // 成功率
	FailureRate  float64 `json:"failure_rate"`  // 失败率
	TimeoutRate  float64 `json:"timeout_rate"`  // 超时率
	ErrorCount   int64   `json:"error_count"`   // 错误数量
	TimeoutCount int64   `json:"timeout_count"` // 超时数量

	// 内存使用情况
	MemoryUsageMB    float64 `json:"memory_usage_mb"`     // 当前内存使用(MB)
	MaxMemoryUsageMB float64 `json:"max_memory_usage_mb"` // 最大内存使用(MB)

	// 分片信息
	ShardCount      int `json:"shard_count"`      // 分片数量
	ActiveShards    int `json:"active_shards"`    // 活跃分片数
	CompletedShards int `json:"completed_shards"` // 完成分片数
}

// CollectionProgress 采集进度
type CollectionProgress struct {
	TaskID          string    `json:"task_id"`
	Phase           string    `json:"phase"`
	ProcessedCount  int64     `json:"processed_count"`
	TotalCount      int64     `json:"total_count"`
	ProgressPercent float64   `json:"progress_percent"`
	Speed           float64   `json:"speed"`
	ETA             string    `json:"eta"`
	StartTime       time.Time `json:"start_time"`
	LastUpdate      time.Time `json:"last_update"`
	Status          string    `json:"status"` // running, paused, cancelled, completed, error
	Message         string    `json:"message"`

	// 性能指标
	MemoryUsageMB float64 `json:"memory_usage_mb"`
	CPUUsage      float64 `json:"cpu_usage"`
	WorkerCount   int     `json:"worker_count"`
}

// CollectionTask 采集任务
type CollectionTask struct {
	ID          string                 `json:"id"`
	Type        TaskType               `json:"type"`     // 任务类型
	Priority    TaskPriority           `json:"priority"` // 任务优先级
	Status      string                 `json:"status"`
	Progress    *CollectionProgress    `json:"progress"`
	Stats       *CollectionStats       `json:"stats"`
	Config      map[string]interface{} `json:"config"`
	CreatedAt   time.Time              `json:"created_at"`
	ScheduledAt *time.Time             `json:"scheduled_at,omitempty"` // 计划执行时间
	StartedAt   *time.Time             `json:"started_at,omitempty"`
	CompletedAt *time.Time             `json:"completed_at,omitempty"`
	Error       string                 `json:"error,omitempty"`

	// 控制通道
	pauseCh  chan struct{} `json:"-"`
	resumeCh chan struct{} `json:"-"`
	cancelCh chan struct{} `json:"-"`

	// 内部状态
	isPaused   int32 `json:"-"` // 使用atomic操作
	isCanceled int32 `json:"-"` // 使用atomic操作
}

// Collector 代理采集器接口
type Collector interface {
	// Start 启动采集器
	Start(ctx context.Context) error
	// Stop 停止采集器
	Stop() error
	// CollectOnce 执行一次采集
	CollectOnce(ctx context.Context) (*CollectionStats, error)
	// GetStats 获取采集统计
	GetStats() *CollectionStats
	// IsRunning 检查是否正在运行
	IsRunning() bool

	// 新增任务控制接口
	StartTask(ctx context.Context, config map[string]interface{}) (*CollectionTask, error)
	PauseTask(taskID string) error
	ResumeTask(taskID string) error
	CancelTask(taskID string) error
	GetTask(taskID string) (*CollectionTask, error)
	ListTasks() []*CollectionTask

	// 进度监控接口
	GetProgress(taskID string) (*CollectionProgress, error)
	SubscribeProgress(taskID string) (<-chan *CollectionProgress, error)
}

// TaskType 任务类型
type TaskType string

const (
	TaskTypeScheduled TaskType = "scheduled" // 定时任务
	TaskTypeManual    TaskType = "manual"    // 手动任务
	TaskTypeImmediate TaskType = "immediate" // 立即任务
)

// TaskPriority 任务优先级
type TaskPriority int

const (
	PriorityLow    TaskPriority = 1
	PriorityNormal TaskPriority = 2
	PriorityHigh   TaskPriority = 3
	PriorityUrgent TaskPriority = 4
)

// TaskQueue 任务队列
type TaskQueue struct {
	tasks    []*CollectionTask
	mu       sync.RWMutex
	notifyCh chan struct{}
}

// ProxyCollector 代理采集器实现
type ProxyCollector struct {
	config      *config.CollectorConfig
	proxyRepo   repository.ProxyRepository
	redisClient *database.RedisClient
	logger      *logrus.Logger
	httpClient  *http.Client

	// 运行状态
	running bool
	mu      sync.RWMutex
	stopCh  chan struct{}

	// 统计信息
	stats   *CollectionStats
	statsMu sync.RWMutex

	// 统一任务管理
	taskQueue           *TaskQueue
	tasks               map[string]*CollectionTask
	tasksMu             sync.RWMutex
	currentTask         *CollectionTask
	currentTaskMu       sync.RWMutex
	taskExecutionMu     sync.Mutex // 确保同时只有一个任务执行
	scheduledTaskTicker *time.Ticker

	// 进度监控
	progressSubscribers map[string][]chan *CollectionProgress
	progressMu          sync.RWMutex

	// 性能优化
	workerPool    chan struct{}  // 工作池
	memoryMonitor *MemoryMonitor // 内存监控
	logSampler    *LogSampler    // 日志采样器

	// 分片处理
	shards []*CollectionShard

	// Prometheus 指标
	metricsCollected    prometheus.Counter
	metricsValid        prometheus.Counter
	metricsDuplicate    prometheus.Counter
	metricsErrors       prometheus.Counter
	metricsSourceErrors prometheus.Counter
	collectionDuration  prometheus.Histogram
}

// MemoryMonitor 内存监控器
type MemoryMonitor struct {
	limitMB   int64
	currentMB int64
	maxMB     int64
	mu        sync.RWMutex
	alertCh   chan struct{}
	lastGC    time.Time
}

// LogSampler 日志采样器
type LogSampler struct {
	rate    int
	counter int64
	mu      sync.Mutex
	logger  *logrus.Logger
}

// CollectionShard 采集分片
type CollectionShard struct {
	ID       int
	Proxies  []*CollectedProxy
	Progress *CollectionProgress
	Status   string // pending, processing, completed, error
	Error    string
	mu       sync.RWMutex
}

// Prometheus 指标定义
var (
	proxiesCollectedTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "proxy_collector_proxies_collected_total",
		Help: "Total number of proxies collected",
	})

	proxiesValidTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "proxy_collector_proxies_valid_total",
		Help: "Total number of valid proxies",
	})

	proxiesDuplicateTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "proxy_collector_proxies_duplicate_total",
		Help: "Total number of duplicate proxies",
	})

	collectionErrorsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "proxy_collector_errors_total",
		Help: "Total number of collection errors",
	})

	sourceErrorsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "proxy_collector_source_errors_total",
		Help: "Total number of source errors",
	})

	collectionDurationSeconds = promauto.NewHistogram(prometheus.HistogramOpts{
		Name:    "proxy_collector_collection_duration_seconds",
		Help:    "Duration of proxy collection in seconds",
		Buckets: prometheus.DefBuckets,
	})
)

// NewProxyCollector 创建新的代理采集器
func NewProxyCollector(
	config *config.CollectorConfig,
	proxyRepo repository.ProxyRepository,
	redisClient *database.RedisClient,
	logger *logrus.Logger,
) *ProxyCollector {
	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: config.RequestTimeout,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	// 创建内存监控器
	memoryMonitor := &MemoryMonitor{
		limitMB: int64(config.MemoryLimitMB),
		alertCh: make(chan struct{}, 1),
		lastGC:  time.Now(),
	}

	// 创建日志采样器
	logSampler := &LogSampler{
		rate:   config.LogSampleRate,
		logger: logger,
	}

	return &ProxyCollector{
		config:      config,
		proxyRepo:   proxyRepo,
		redisClient: redisClient,
		logger:      logger,
		httpClient:  httpClient,
		stopCh:      make(chan struct{}),
		stats: &CollectionStats{
			StartTime: time.Now(),
		},
		taskQueue:           NewTaskQueue(),
		tasks:               make(map[string]*CollectionTask),
		progressSubscribers: make(map[string][]chan *CollectionProgress),
		workerPool:          make(chan struct{}, config.WorkerPoolSize),
		memoryMonitor:       memoryMonitor,
		logSampler:          logSampler,
		metricsCollected:    proxiesCollectedTotal,
		metricsValid:        proxiesValidTotal,
		metricsDuplicate:    proxiesDuplicateTotal,
		metricsErrors:       collectionErrorsTotal,
		metricsSourceErrors: sourceErrorsTotal,
		collectionDuration:  collectionDurationSeconds,
	}
}

// Start 启动采集器
func (c *ProxyCollector) Start(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.running {
		return fmt.Errorf("collector is already running")
	}

	if !c.config.Enabled {
		c.logger.Info("Proxy collector is disabled")
		return nil
	}

	c.running = true
	c.logger.Info("Starting proxy collector with task queue")

	// 启动统一的任务处理循环
	go c.taskProcessingLoop(ctx)

	// 启动定时任务调度器
	go c.scheduledTaskLoop(ctx)

	return nil
}

// Stop 停止采集器
func (c *ProxyCollector) Stop() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.running {
		return nil
	}

	c.logger.Info("Stopping proxy collector")

	// 停止定时任务调度器
	if c.scheduledTaskTicker != nil {
		c.scheduledTaskTicker.Stop()
	}

	// 取消所有待执行的任务
	c.tasksMu.Lock()
	for _, task := range c.tasks {
		if task.Status == "pending" || task.Status == "running" {
			atomic.StoreInt32(&task.isCanceled, 1)
			select {
			case task.cancelCh <- struct{}{}:
			default:
			}
		}
	}
	c.tasksMu.Unlock()

	close(c.stopCh)
	c.running = false

	return nil
}

// IsRunning 检查是否正在运行
func (c *ProxyCollector) IsRunning() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.running
}

// GetStats 获取采集统计
func (c *ProxyCollector) GetStats() *CollectionStats {
	c.statsMu.RLock()
	defer c.statsMu.RUnlock()

	// 创建副本以避免并发访问问题
	stats := *c.stats
	return &stats
}

// taskProcessingLoop 统一的任务处理循环
func (c *ProxyCollector) taskProcessingLoop(ctx context.Context) {
	c.logger.Info("Starting unified task processing loop")

	for {
		select {
		case <-ctx.Done():
			c.logger.Info("Task processing loop stopped due to context cancellation")
			return
		case <-c.stopCh:
			c.logger.Info("Task processing loop stopped")
			return
		case <-c.taskQueue.GetNotifyChannel():
			// 处理队列中的任务
			c.processNextTask(ctx)
		}
	}
}

// scheduledTaskLoop 定时任务调度循环
func (c *ProxyCollector) scheduledTaskLoop(ctx context.Context) {
	c.logger.Info("Starting scheduled task loop")

	// 创建定时器
	c.scheduledTaskTicker = time.NewTicker(c.config.CollectInterval)
	defer c.scheduledTaskTicker.Stop()

	// 启动时创建一个初始的定时任务
	c.createScheduledTask("initial_collection")

	for {
		select {
		case <-ctx.Done():
			c.logger.Info("Scheduled task loop stopped due to context cancellation")
			return
		case <-c.stopCh:
			c.logger.Info("Scheduled task loop stopped")
			return
		case <-c.scheduledTaskTicker.C:
			// 创建定时采集任务
			c.createScheduledTask("scheduled_collection")
		}
	}
}

// processNextTask 处理下一个任务
func (c *ProxyCollector) processNextTask(ctx context.Context) {
	// 使用互斥锁确保同时只有一个任务执行
	c.taskExecutionMu.Lock()
	defer c.taskExecutionMu.Unlock()

	task := c.taskQueue.Dequeue()
	if task == nil {
		return
	}

	// 检查任务是否已被取消
	if atomic.LoadInt32(&task.isCanceled) == 1 {
		task.Status = "cancelled"
		task.Progress.Status = "cancelled"
		task.Progress.LastUpdate = time.Now()
		c.logger.WithField("task_id", task.ID).Info("Task was cancelled before execution")
		return
	}

	// 设置当前任务
	c.currentTaskMu.Lock()
	c.currentTask = task
	c.currentTaskMu.Unlock()

	// 执行任务
	c.executeTaskSafely(ctx, task)

	// 清除当前任务
	c.currentTaskMu.Lock()
	c.currentTask = nil
	c.currentTaskMu.Unlock()
}

// createScheduledTask 创建定时任务
func (c *ProxyCollector) createScheduledTask(reason string) {
	taskID := fmt.Sprintf("scheduled_%d", time.Now().UnixNano())

	task := &CollectionTask{
		ID:       taskID,
		Type:     TaskTypeScheduled,
		Priority: PriorityNormal,
		Status:   "pending",
		Config: map[string]interface{}{
			"reason": reason,
			"type":   "scheduled",
		},
		CreatedAt: time.Now(),
		pauseCh:   make(chan struct{}),
		resumeCh:  make(chan struct{}),
		cancelCh:  make(chan struct{}),
		Progress: &CollectionProgress{
			TaskID:     taskID,
			Status:     "pending",
			StartTime:  time.Now(),
			LastUpdate: time.Now(),
		},
		Stats: &CollectionStats{
			StartTime: time.Now(),
		},
	}

	c.tasksMu.Lock()
	c.tasks[taskID] = task
	c.tasksMu.Unlock()

	// 添加到队列
	c.taskQueue.Enqueue(task)

	c.logger.WithFields(map[string]interface{}{
		"task_id": taskID,
		"reason":  reason,
	}).Info("Created scheduled collection task")
}

// CollectOnce 执行一次采集
func (c *ProxyCollector) CollectOnce(ctx context.Context) (*CollectionStats, error) {
	startTime := time.Now()
	c.logger.Info("Starting proxy collection")

	// 重置统计信息
	c.statsMu.Lock()
	c.stats = &CollectionStats{
		StartTime: startTime,
	}
	c.statsMu.Unlock()

	// 加载代理源配置
	sources, err := c.loadProxySources()
	if err != nil {
		c.metricsErrors.Inc()
		return nil, fmt.Errorf("failed to load proxy sources: %w", err)
	}

	// 采集所有代理
	collectedProxies, err := c.collectFromSources(ctx, sources)
	if err != nil {
		c.metricsErrors.Inc()
		return nil, fmt.Errorf("failed to collect proxies: %w", err)
	}

	// 验证代理
	validProxies, err := c.verifyProxies(ctx, collectedProxies)
	if err != nil {
		c.metricsErrors.Inc()
		return nil, fmt.Errorf("failed to verify proxies: %w", err)
	}

	// 保存到数据库
	savedCount, duplicateCount, err := c.saveProxies(ctx, validProxies)
	if err != nil {
		c.metricsErrors.Inc()
		return nil, fmt.Errorf("failed to save proxies: %w", err)
	}

	// 保存原始数据（如果启用）
	if c.config.SaveRawData {
		if err := c.saveRawData(collectedProxies); err != nil {
			c.logger.WithError(err).Error("Failed to save raw data")
		}
	}

	// 更新统计信息
	endTime := time.Now()
	duration := endTime.Sub(startTime)

	c.statsMu.Lock()
	c.stats.TotalSources = c.getTotalSourceCount(sources)
	c.stats.TotalProxies = len(collectedProxies)
	c.stats.ValidProxies = savedCount
	c.stats.DuplicateCount = duplicateCount
	c.stats.EndTime = endTime
	c.stats.Duration = duration.String()
	c.stats.LastCollection = endTime
	stats := *c.stats
	c.statsMu.Unlock()

	// 更新 Prometheus 指标
	c.metricsCollected.Add(float64(len(collectedProxies)))
	c.metricsValid.Add(float64(savedCount))
	c.metricsDuplicate.Add(float64(duplicateCount))
	c.collectionDuration.Observe(duration.Seconds())

	c.logger.WithFields(logrus.Fields{
		"total_proxies":   len(collectedProxies),
		"valid_proxies":   savedCount,
		"duplicate_count": duplicateCount,
		"duration":        duration.String(),
	}).Info("Proxy collection completed")

	return &stats, nil
}

// loadProxySources 加载代理源配置
func (c *ProxyCollector) loadProxySources() (*ProxySource, error) {
	file, err := os.Open(c.config.SourcesFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open sources file: %w", err)
	}
	defer file.Close()

	var sources ProxySource
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&sources); err != nil {
		return nil, fmt.Errorf("failed to decode sources file: %w", err)
	}

	return &sources, nil
}

// getTotalSourceCount 获取总源数量
func (c *ProxyCollector) getTotalSourceCount(sources *ProxySource) int {
	return len(sources.HTTP) + len(sources.HTTPS) + len(sources.SOCKS4) + len(sources.SOCKS5)
}

// StartTask 启动新的采集任务
func (c *ProxyCollector) StartTask(ctx context.Context, config map[string]interface{}) (*CollectionTask, error) {
	taskID := fmt.Sprintf("manual_%d", time.Now().UnixNano())

	// 确定任务优先级
	priority := PriorityNormal
	if priorityVal, ok := config["priority"]; ok {
		if p, ok := priorityVal.(TaskPriority); ok {
			priority = p
		}
	}

	// 确定任务类型
	taskType := TaskTypeManual
	if typeVal, ok := config["immediate"]; ok {
		if immediate, ok := typeVal.(bool); ok && immediate {
			taskType = TaskTypeImmediate
			priority = PriorityHigh // 立即任务使用高优先级
		}
	}

	task := &CollectionTask{
		ID:        taskID,
		Type:      taskType,
		Priority:  priority,
		Status:    "pending",
		Config:    config,
		CreatedAt: time.Now(),
		pauseCh:   make(chan struct{}),
		resumeCh:  make(chan struct{}),
		cancelCh:  make(chan struct{}),
		Progress: &CollectionProgress{
			TaskID:     taskID,
			Status:     "pending",
			StartTime:  time.Now(),
			LastUpdate: time.Now(),
		},
		Stats: &CollectionStats{
			StartTime: time.Now(),
		},
	}

	c.tasksMu.Lock()
	if c.tasks == nil {
		c.tasks = make(map[string]*CollectionTask)
	}
	c.tasks[taskID] = task
	c.tasksMu.Unlock()

	// 添加到统一任务队列
	c.taskQueue.Enqueue(task)

	c.logger.WithFields(map[string]interface{}{
		"task_id":  taskID,
		"type":     taskType,
		"priority": priority,
	}).Info("Manual collection task created and queued")

	return task, nil
}

// PauseTask 暂停任务
func (c *ProxyCollector) PauseTask(taskID string) error {
	c.tasksMu.RLock()
	task, exists := c.tasks[taskID]
	c.tasksMu.RUnlock()

	if !exists {
		return fmt.Errorf("task %s not found", taskID)
	}

	if atomic.LoadInt32(&task.isPaused) == 1 {
		return fmt.Errorf("task %s is already paused", taskID)
	}

	atomic.StoreInt32(&task.isPaused, 1)
	task.Status = "paused"
	task.Progress.Status = "paused"
	task.Progress.LastUpdate = time.Now()

	select {
	case task.pauseCh <- struct{}{}:
	default:
	}

	c.logger.WithField("task_id", taskID).Info("Collection task paused")
	return nil
}

// ResumeTask 恢复任务
func (c *ProxyCollector) ResumeTask(taskID string) error {
	c.tasksMu.RLock()
	task, exists := c.tasks[taskID]
	c.tasksMu.RUnlock()

	if !exists {
		return fmt.Errorf("task %s not found", taskID)
	}

	if atomic.LoadInt32(&task.isPaused) == 0 {
		return fmt.Errorf("task %s is not paused", taskID)
	}

	atomic.StoreInt32(&task.isPaused, 0)
	task.Status = "running"
	task.Progress.Status = "running"
	task.Progress.LastUpdate = time.Now()

	select {
	case task.resumeCh <- struct{}{}:
	default:
	}

	c.logger.WithField("task_id", taskID).Info("Collection task resumed")
	return nil
}

// CancelTask 取消任务
func (c *ProxyCollector) CancelTask(taskID string) error {
	c.tasksMu.RLock()
	task, exists := c.tasks[taskID]
	c.tasksMu.RUnlock()

	if !exists {
		return fmt.Errorf("task %s not found", taskID)
	}

	// 如果任务还在队列中，直接从队列移除
	if task.Status == "pending" {
		if c.taskQueue.Remove(taskID) {
			task.Status = "cancelled"
			task.Progress.Status = "cancelled"
			task.Progress.LastUpdate = time.Now()
			c.logger.WithField("task_id", taskID).Info("Task cancelled (removed from queue)")
			return nil
		}
	}

	// 如果任务正在执行，发送取消信号
	atomic.StoreInt32(&task.isCanceled, 1)
	task.Status = "cancelled"
	task.Progress.Status = "cancelled"
	task.Progress.LastUpdate = time.Now()

	select {
	case task.cancelCh <- struct{}{}:
	default:
	}

	c.logger.WithField("task_id", taskID).Info("Collection task cancelled")
	return nil
}

// GetTask 获取任务信息
func (c *ProxyCollector) GetTask(taskID string) (*CollectionTask, error) {
	c.tasksMu.RLock()
	task, exists := c.tasks[taskID]
	c.tasksMu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("task %s not found", taskID)
	}

	return task, nil
}

// ListTasks 获取所有任务
func (c *ProxyCollector) ListTasks() []*CollectionTask {
	c.tasksMu.RLock()
	defer c.tasksMu.RUnlock()

	tasks := make([]*CollectionTask, 0, len(c.tasks))
	for _, task := range c.tasks {
		tasks = append(tasks, task)
	}

	return tasks
}

// GetProgress 获取任务进度
func (c *ProxyCollector) GetProgress(taskID string) (*CollectionProgress, error) {
	c.tasksMu.RLock()
	task, exists := c.tasks[taskID]
	c.tasksMu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("task %s not found", taskID)
	}

	return task.Progress, nil
}

// SubscribeProgress 订阅进度更新
func (c *ProxyCollector) SubscribeProgress(taskID string) (<-chan *CollectionProgress, error) {
	c.tasksMu.RLock()
	_, exists := c.tasks[taskID]
	c.tasksMu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("task %s not found", taskID)
	}

	progressCh := make(chan *CollectionProgress, 10)

	c.progressMu.Lock()
	if c.progressSubscribers == nil {
		c.progressSubscribers = make(map[string][]chan *CollectionProgress)
	}
	c.progressSubscribers[taskID] = append(c.progressSubscribers[taskID], progressCh)
	c.progressMu.Unlock()

	return progressCh, nil
}

// executeTaskSafely 安全执行任务（带错误恢复）
func (c *ProxyCollector) executeTaskSafely(ctx context.Context, task *CollectionTask) {
	defer func() {
		if r := recover(); r != nil {
			task.Status = "error"
			task.Error = fmt.Sprintf("Task panicked: %v", r)
			task.Progress.Status = "error"
			task.Progress.LastUpdate = time.Now()
			c.logger.WithFields(map[string]interface{}{
				"task_id": task.ID,
				"panic":   r,
			}).Error("Task execution panicked")
		}
	}()

	c.executeTask(ctx, task)
}

// executeTask 执行任务
func (c *ProxyCollector) executeTask(ctx context.Context, task *CollectionTask) {
	c.logger.WithFields(map[string]interface{}{
		"task_id":  task.ID,
		"type":     task.Type,
		"priority": task.Priority,
	}).Info("Starting task execution")

	task.Status = "running"
	task.Progress.Status = "running"
	task.Progress.Phase = "starting"
	now := time.Now()
	task.StartedAt = &now
	task.Progress.LastUpdate = now

	// 检查暂停状态
	for atomic.LoadInt32(&task.isPaused) == 1 {
		task.Progress.Status = "paused"
		task.Progress.LastUpdate = time.Now()

		select {
		case <-task.resumeCh:
			atomic.StoreInt32(&task.isPaused, 0)
			task.Progress.Status = "running"
			task.Progress.LastUpdate = time.Now()
			c.logger.WithField("task_id", task.ID).Info("Task resumed")
		case <-task.cancelCh:
			task.Status = "cancelled"
			task.Progress.Status = "cancelled"
			task.Progress.LastUpdate = time.Now()
			c.logger.WithField("task_id", task.ID).Info("Task cancelled during pause")
			return
		case <-ctx.Done():
			task.Status = "cancelled"
			task.Progress.Status = "cancelled"
			task.Progress.LastUpdate = time.Now()
			c.logger.WithField("task_id", task.ID).Info("Task cancelled due to context")
			return
		}
	}

	// 检查取消状态
	if atomic.LoadInt32(&task.isCanceled) == 1 {
		task.Status = "cancelled"
		task.Progress.Status = "cancelled"
		task.Progress.LastUpdate = time.Now()
		c.logger.WithField("task_id", task.ID).Info("Task was cancelled before execution")
		return
	}

	// 执行采集逻辑
	task.Progress.Phase = "collecting"
	task.Progress.LastUpdate = time.Now()

	stats, err := c.CollectOnce(ctx)
	if err != nil {
		task.Status = "error"
		task.Error = err.Error()
		task.Progress.Status = "error"
		c.logger.WithFields(map[string]interface{}{
			"task_id": task.ID,
			"error":   err.Error(),
		}).Error("Task execution failed")
	} else {
		task.Status = "completed"
		task.Progress.Status = "completed"
		task.Progress.Phase = "completed"
		task.Stats = stats
		now := time.Now()
		task.CompletedAt = &now
		c.logger.WithFields(map[string]interface{}{
			"task_id":       task.ID,
			"total_proxies": stats.TotalProxies,
			"valid_proxies": stats.ValidProxies,
			"duration":      time.Since(*task.StartedAt).String(),
		}).Info("Task execution completed successfully")
	}

	task.Progress.LastUpdate = time.Now()

	// 通知进度订阅者
	c.notifyProgressSubscribers(task)
}

// notifyProgressSubscribers 通知进度订阅者
func (c *ProxyCollector) notifyProgressSubscribers(task *CollectionTask) {
	c.progressMu.RLock()
	subscribers, exists := c.progressSubscribers[task.ID]
	c.progressMu.RUnlock()

	if !exists || len(subscribers) == 0 {
		return
	}

	for _, ch := range subscribers {
		select {
		case ch <- task.Progress:
		default:
			// 如果通道已满，跳过这个订阅者
		}
	}
}

// LogSampler 方法实现

// Info 采样日志信息
func (ls *LogSampler) Info(msg string) {
	if ls.shouldLog() {
		ls.logger.Info(msg)
	}
}

// InfoWithFields 采样日志信息（带字段）
func (ls *LogSampler) InfoWithFields(fields map[string]interface{}, msg string) {
	if ls.shouldLog() {
		ls.logger.WithFields(fields).Info(msg)
	}
}

// Error 错误日志（不采样）
func (ls *LogSampler) Error(msg string) {
	ls.logger.Error(msg)
}

// ErrorWithFields 错误日志（带字段，不采样）
func (ls *LogSampler) ErrorWithFields(fields map[string]interface{}, msg string) {
	ls.logger.WithFields(fields).Error(msg)
}

// Warn 警告日志（不采样）
func (ls *LogSampler) Warn(msg string) {
	ls.logger.Warn(msg)
}

// shouldLog 判断是否应该记录日志
func (ls *LogSampler) shouldLog() bool {
	if ls.rate <= 1 {
		return true
	}

	ls.mu.Lock()
	defer ls.mu.Unlock()

	ls.counter++
	return ls.counter%int64(ls.rate) == 0
}

// MemoryMonitor 方法实现

// UpdateUsage 更新内存使用情况
func (mm *MemoryMonitor) UpdateUsage() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	currentMB := int64(m.Alloc / 1024 / 1024)

	mm.mu.Lock()
	mm.currentMB = currentMB
	if currentMB > mm.maxMB {
		mm.maxMB = currentMB
	}
	mm.mu.Unlock()

	// 检查是否超过限制
	if mm.limitMB > 0 && currentMB > mm.limitMB {
		select {
		case mm.alertCh <- struct{}{}:
		default:
		}
	}

	// 定期触发GC
	if time.Since(mm.lastGC) > 5*time.Minute && currentMB > mm.limitMB/2 {
		runtime.GC()
		mm.lastGC = time.Now()
	}
}

// GetUsage 获取内存使用情况
func (mm *MemoryMonitor) GetUsage() (current, max, limit int64) {
	mm.mu.RLock()
	defer mm.mu.RUnlock()
	return mm.currentMB, mm.maxMB, mm.limitMB
}

// IsOverLimit 检查是否超过限制
func (mm *MemoryMonitor) IsOverLimit() bool {
	mm.mu.RLock()
	defer mm.mu.RUnlock()
	return mm.limitMB > 0 && mm.currentMB > mm.limitMB
}

// TaskQueue 方法实现

// NewTaskQueue 创建新的任务队列
func NewTaskQueue() *TaskQueue {
	return &TaskQueue{
		tasks:    make([]*CollectionTask, 0),
		notifyCh: make(chan struct{}, 1),
	}
}

// Enqueue 添加任务到队列
func (tq *TaskQueue) Enqueue(task *CollectionTask) {
	tq.mu.Lock()
	defer tq.mu.Unlock()

	// 按优先级插入任务
	inserted := false
	for i, existingTask := range tq.tasks {
		if task.Priority > existingTask.Priority {
			// 插入到当前位置
			tq.tasks = append(tq.tasks[:i], append([]*CollectionTask{task}, tq.tasks[i:]...)...)
			inserted = true
			break
		}
	}

	if !inserted {
		tq.tasks = append(tq.tasks, task)
	}

	// 通知有新任务
	select {
	case tq.notifyCh <- struct{}{}:
	default:
	}
}

// Dequeue 从队列中取出下一个任务
func (tq *TaskQueue) Dequeue() *CollectionTask {
	tq.mu.Lock()
	defer tq.mu.Unlock()

	if len(tq.tasks) == 0 {
		return nil
	}

	task := tq.tasks[0]
	tq.tasks = tq.tasks[1:]
	return task
}

// Peek 查看下一个任务但不移除
func (tq *TaskQueue) Peek() *CollectionTask {
	tq.mu.RLock()
	defer tq.mu.RUnlock()

	if len(tq.tasks) == 0 {
		return nil
	}

	return tq.tasks[0]
}

// Size 获取队列大小
func (tq *TaskQueue) Size() int {
	tq.mu.RLock()
	defer tq.mu.RUnlock()
	return len(tq.tasks)
}

// Remove 从队列中移除指定任务
func (tq *TaskQueue) Remove(taskID string) bool {
	tq.mu.Lock()
	defer tq.mu.Unlock()

	for i, task := range tq.tasks {
		if task.ID == taskID {
			tq.tasks = append(tq.tasks[:i], tq.tasks[i+1:]...)
			return true
		}
	}
	return false
}

// GetNotifyChannel 获取通知通道
func (tq *TaskQueue) GetNotifyChannel() <-chan struct{} {
	return tq.notifyCh
}

// GetCurrentTask 获取当前正在执行的任务
func (c *ProxyCollector) GetCurrentTask() *CollectionTask {
	c.currentTaskMu.RLock()
	defer c.currentTaskMu.RUnlock()
	return c.currentTask
}

// ForceReset 强制重置采集器状态（清除缓存和进度）
func (c *ProxyCollector) ForceReset() error {
	c.logger.Warn("Performing force reset - clearing all caches and progress")

	// 清除验证进度
	if err := c.clearVerificationProgress(); err != nil {
		c.logger.WithError(err).Error("Failed to clear verification progress")
		return fmt.Errorf("failed to clear verification progress: %w", err)
	}

	// 清除代理缓存
	if c.config.CacheEnabled {
		cachePath := c.config.CacheFilePath
		if cachePath == "" {
			cachePath = "freeProxy/unified_proxy_cache.json"
		}
		cache := NewCache(cachePath, c.config.CacheValidDuration, 24*time.Hour, c.logger)
		cache.Clear()
		if err := cache.Save(); err != nil {
			c.logger.WithError(err).Debug("Failed to save cleared cache")
		}
	}

	// 重置统计信息
	c.statsMu.Lock()
	c.stats = &CollectionStats{
		StartTime: time.Now(),
	}
	c.statsMu.Unlock()

	// 清除所有任务（保留正在运行的任务）
	c.tasksMu.Lock()
	runningTasks := make(map[string]*CollectionTask)
	for id, task := range c.tasks {
		if task.Status == "running" || task.Status == "pending" {
			runningTasks[id] = task
		}
	}
	c.tasks = runningTasks
	c.tasksMu.Unlock()

	c.logger.Info("Force reset completed successfully")
	return nil
}

// GetQueueStatus 获取队列状态
func (c *ProxyCollector) GetQueueStatus() map[string]interface{} {
	queueSize := c.taskQueue.Size()
	currentTask := c.GetCurrentTask()

	status := map[string]interface{}{
		"queue_size":   queueSize,
		"current_task": nil,
	}

	if currentTask != nil {
		status["current_task"] = map[string]interface{}{
			"id":       currentTask.ID,
			"type":     currentTask.Type,
			"priority": currentTask.Priority,
			"status":   currentTask.Status,
			"progress": currentTask.Progress.ProgressPercent,
		}
	}

	return status
}
