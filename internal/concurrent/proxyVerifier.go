package concurrent

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"proxyFlow/internal/utils"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"github.com/sirupsen/logrus"
)

// ProxyCache 代理缓存接口
type ProxyCache interface {
	Set(host string, port int, isValid bool)
	IsValid(host string, port int) bool
	Save() error
}

// ProxyData 代理数据结构
type ProxyData struct {
	Host           string
	Port           int
	Type           string
	Username       string
	Password       string
	TestURL        string
	Timeout        time.Duration
	ConnectTimeout time.Duration
}

// ProxyResult 代理验证结果
type ProxyResult struct {
	Host         string
	Port         int
	Success      bool
	Error        string
	ResponseTime time.Duration
	StatusCode   int
	RemoteIP     string
}

// ProxyVerifierConfig 精简的代理验证器配置
type ProxyVerifierConfig struct {
	// 核心配置
	MaxWorkers          int           // 最大工作协程数
	TestURL             string        // 测试URL
	VerificationTimeout time.Duration // 验证超时
	ConnectTimeout      time.Duration // 连接超时

	// 简化的性能配置
	QueueSize int // 任务队列大小

	// 重试配置
	RetryAttempts int           // 重试次数
	RetryDelay    time.Duration // 重试延迟

	// 进度报告
	ProgressInterval time.Duration // 进度报告间隔
}

// DefaultProxyVerifierConfig 返回精简的默认配置
func DefaultProxyVerifierConfig() *ProxyVerifierConfig {
	cpuCount := runtime.NumCPU()
	// 合理的工作者数量配置
	maxWorkers := cpuCount * 20 // 适中的并发数
	if maxWorkers < 100 {       // 最低保证100个工作者
		maxWorkers = 100
	}
	if maxWorkers > 1000 { // 硬限制最大1000个工作者
		maxWorkers = 1000
	}

	return &ProxyVerifierConfig{
		MaxWorkers:          maxWorkers,
		TestURL:             "http://httpbin.org/ip",
		VerificationTimeout: 10 * time.Second, // 合理的超时时间
		ConnectTimeout:      5 * time.Second,  // 合理的连接超时
		QueueSize:           maxWorkers * 10,  // 队列大小与工作者数量成比例
		RetryAttempts:       1,                // 简单重试
		RetryDelay:          time.Second,      // 重试延迟
		ProgressInterval:    2 * time.Second,  // 合理的进度报告间隔
	}
}

// ProxyVerifier 精简的高并发代理验证器
type ProxyVerifier struct {
	config        *ProxyVerifierConfig
	logger        *logrus.Logger
	httpClient    *http.Client
	baseTransport *http.Transport
	cache         ProxyCache // 缓存接口

	// 统计信息
	totalTasks      int64
	completedTasks  int64
	successfulTasks int64
	failedTasks     int64
	startTime       time.Time

	// 控制
	ctx     context.Context
	cancel  context.CancelFunc
	stopped bool
	mu      sync.RWMutex
}

// NewProxyVerifier 创建新的精简高并发代理验证器
func NewProxyVerifier(config *ProxyVerifierConfig, logger *logrus.Logger) *ProxyVerifier {
	if config == nil {
		config = DefaultProxyVerifierConfig()
	}

	if logger == nil {
		logger = logrus.New()
	}

	// 创建优化的传输层配置
	baseTransport := &http.Transport{
		MaxIdleConns:        100,              // 合理的空闲连接数
		MaxIdleConnsPerHost: 20,               // 每个主机最多20个空闲连接
		IdleConnTimeout:     30 * time.Second, // 合理的空闲超时
		DisableKeepAlives:   false,            // 启用连接复用
		DialContext: (&net.Dialer{
			Timeout:   config.ConnectTimeout,
			KeepAlive: 30 * time.Second, // 保持活跃连接
		}).DialContext,
		TLSHandshakeTimeout:   5 * time.Second, // TLS握手超时
		ResponseHeaderTimeout: config.VerificationTimeout,
		ExpectContinueTimeout: time.Second, // 等待时间
		DisableCompression:    true,        // 禁用压缩减少CPU开销
		MaxConnsPerHost:       50,          // 每主机连接数
	}

	// 创建基础HTTP客户端
	httpClient := &http.Client{
		Transport: baseTransport,
		Timeout:   config.VerificationTimeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse // 不跟随重定向
		},
	}

	ctx, cancel := context.WithCancel(context.Background())

	pv := &ProxyVerifier{
		config:        config,
		logger:        logger,
		httpClient:    httpClient,
		baseTransport: baseTransport,
		ctx:           ctx,
		cancel:        cancel,
		stopped:       false,
	}

	return pv
}

// createProxyClient 创建带代理的HTTP客户端
func (pv *ProxyVerifier) createProxyClient(proxyURL *url.URL) *http.Client {
	// 克隆基础传输层
	transport := pv.baseTransport.Clone()
	transport.Proxy = http.ProxyURL(proxyURL)

	return &http.Client{
		Transport: transport,
		Timeout:   pv.config.VerificationTimeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
	}
}

// Stop 停止验证器并清理资源
func (pv *ProxyVerifier) Stop() {
	pv.mu.Lock()
	defer pv.mu.Unlock()

	if pv.stopped {
		return
	}

	pv.stopped = true

	// 取消上下文
	if pv.cancel != nil {
		pv.cancel()
	}

	// 关闭基础传输层的空闲连接
	if pv.baseTransport != nil {
		pv.baseTransport.CloseIdleConnections()
	}

	pv.logger.Info("ProxyVerifier stopped and resources cleaned")
}

// SetCache 设置缓存接口
func (pv *ProxyVerifier) SetCache(cache ProxyCache) {
	pv.mu.Lock()
	defer pv.mu.Unlock()
	pv.cache = cache
}

// VerifyProxies 精简的高并发验证代理列表
func (pv *ProxyVerifier) VerifyProxies(ctx context.Context, proxies []ProxyData, progressCallback func(completed, total, successful, failed int64)) ([]*ProxyResult, error) {
	if len(proxies) == 0 {
		return nil, nil
	}

	pv.startTime = time.Now()
	pv.totalTasks = int64(len(proxies))
	atomic.StoreInt64(&pv.completedTasks, 0)
	atomic.StoreInt64(&pv.successfulTasks, 0)
	atomic.StoreInt64(&pv.failedTasks, 0)

	pv.logger.WithFields(logrus.Fields{
		"total_proxies": len(proxies),
		"max_workers":   pv.config.MaxWorkers,
		"test_url":      pv.config.TestURL,
		"timeout":       pv.config.VerificationTimeout,
	}).Info("Starting simplified concurrent proxy verification")

	// 创建任务通道和结果通道
	taskCh := make(chan ProxyData, pv.config.QueueSize)
	resultCh := make(chan *ProxyResult, len(proxies))

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < pv.config.MaxWorkers; i++ {
		wg.Add(1)
		go pv.worker(ctx, &wg, taskCh, resultCh)
	}

	// 启动进度报告器
	if progressCallback != nil {
		go pv.progressReporter(ctx, progressCallback)
	}

	// 发送任务
	go func() {
		defer close(taskCh)
		for _, proxy := range proxies {
			// 设置默认值
			if proxy.TestURL == "" {
				proxy.TestURL = pv.config.TestURL
			}
			if proxy.Timeout == 0 {
				proxy.Timeout = pv.config.VerificationTimeout
			}
			if proxy.ConnectTimeout == 0 {
				proxy.ConnectTimeout = pv.config.ConnectTimeout
			}

			select {
			case taskCh <- proxy:
			case <-ctx.Done():
				return
			}
		}
	}()

	// 等待所有工作协程完成
	go func() {
		wg.Wait()
		close(resultCh)
	}()

	// 收集结果
	results := make([]*ProxyResult, 0, len(proxies))
	for result := range resultCh {
		results = append(results, result)
	}

	// 统计结果
	successful := int64(0)
	failed := int64(0)
	for _, result := range results {
		if result.Success {
			successful++
		} else {
			failed++
		}
	}

	pv.logger.WithFields(logrus.Fields{
		"total_verified": len(results),
		"successful":     successful,
		"failed":         failed,
		"success_rate":   fmt.Sprintf("%.2f%%", float64(successful)/float64(len(results))*100),
		"elapsed":        time.Since(pv.startTime),
	}).Info("Simplified concurrent proxy verification completed")

	return results, nil
}

// worker 工作协程
func (pv *ProxyVerifier) worker(ctx context.Context, wg *sync.WaitGroup, taskCh <-chan ProxyData, resultCh chan<- *ProxyResult) {
	defer wg.Done()

	for {
		select {
		case proxy, ok := <-taskCh:
			if !ok {
				return // 通道关闭
			}
			result := pv.verifyProxy(ctx, proxy)

			// 更新统计
			atomic.AddInt64(&pv.completedTasks, 1)
			if result.Success {
				atomic.AddInt64(&pv.successfulTasks, 1)
			} else {
				atomic.AddInt64(&pv.failedTasks, 1)
			}

			select {
			case resultCh <- result:
			case <-ctx.Done():
				return
			}

		case <-ctx.Done():
			return
		}
	}
}

// progressReporter 进度报告器
func (pv *ProxyVerifier) progressReporter(ctx context.Context, callback func(completed, total, successful, failed int64)) {
	ticker := time.NewTicker(pv.config.ProgressInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			completed := atomic.LoadInt64(&pv.completedTasks)
			successful := atomic.LoadInt64(&pv.successfulTasks)
			failed := atomic.LoadInt64(&pv.failedTasks)
			callback(completed, pv.totalTasks, successful, failed)

		case <-ctx.Done():
			return
		}
	}
}

// verifyProxy 验证单个代理
func (pv *ProxyVerifier) verifyProxy(ctx context.Context, proxyData ProxyData) *ProxyResult {
	startTime := time.Now()

	result := &ProxyResult{
		Host: proxyData.Host,
		Port: proxyData.Port,
	}

	// 构建代理URL
	proxyURL, err := pv.buildProxyURL(proxyData)
	if err != nil {
		result.Error = err.Error()
		return result
	}

	// 创建带代理的客户端
	client := pv.createProxyClient(proxyURL)

	// 重试机制
	for attempt := 0; attempt <= pv.config.RetryAttempts; attempt++ {
		if attempt > 0 {
			select {
			case <-time.After(pv.config.RetryDelay):
			case <-ctx.Done():
				result.Error = "context cancelled"
				return result
			}
		}

		// 使用HEAD请求减少数据传输
		req, err := http.NewRequestWithContext(ctx, "HEAD", proxyData.TestURL, nil)
		if err != nil {
			result.Error = err.Error()
			continue
		}

		// 设置请求头
		req.Header.Set("User-Agent", utils.RandUserAgent())

		// 发送请求
		resp, err := client.Do(req)
		if err != nil {
			result.Error = err.Error()
			continue
		}
		resp.Body.Close()

		// 检查响应状态
		if resp.StatusCode >= 200 && resp.StatusCode < 300 {
			result.Success = true
			result.ResponseTime = time.Since(startTime)
			result.StatusCode = resp.StatusCode
			break
		} else {
			result.Error = fmt.Sprintf("HTTP %d", resp.StatusCode)
			result.StatusCode = resp.StatusCode
		}
	}

	result.ResponseTime = time.Since(startTime)

	// 设置缓存结果
	pv.setCacheIfAvailable(proxyData.Host, proxyData.Port, result.Success)

	return result
}

// setCacheIfAvailable 如果缓存可用，则设置缓存
func (pv *ProxyVerifier) setCacheIfAvailable(host string, port int, isValid bool) {
	pv.mu.RLock()
	cache := pv.cache
	pv.mu.RUnlock() // 立即释放锁，避免死锁

	if cache != nil {
		cache.Set(host, port, isValid)
	}
}

// buildProxyURL 构建代理URL
func (pv *ProxyVerifier) buildProxyURL(proxy ProxyData) (*url.URL, error) {
	var scheme string
	switch proxy.Type {
	case "http", "https":
		scheme = "http"
	case "socks4":
		scheme = "socks4"
	case "socks5":
		scheme = "socks5"
	default:
		scheme = "http"
	}

	// 构建URL
	proxyURL := &url.URL{
		Scheme: scheme,
		Host:   fmt.Sprintf("%s:%d", proxy.Host, proxy.Port),
	}

	// 添加认证信息
	if proxy.Username != "" {
		if proxy.Password != "" {
			proxyURL.User = url.UserPassword(proxy.Username, proxy.Password)
		} else {
			proxyURL.User = url.User(proxy.Username)
		}
	}

	return proxyURL, nil
}

// GetStats 获取验证统计信息
func (pv *ProxyVerifier) GetStats() (completed, total, successful, failed int64, elapsed time.Duration) {
	return atomic.LoadInt64(&pv.completedTasks),
		pv.totalTasks,
		atomic.LoadInt64(&pv.successfulTasks),
		atomic.LoadInt64(&pv.failedTasks),
		time.Since(pv.startTime)
}

// StopEngine 停止引擎（兼容性方法）
func (pv *ProxyVerifier) StopEngine() error {
	pv.Stop()
	return nil
}
