package errors

import (
	"errors"
	"fmt"
	"net/http"
)

// ErrorCode 错误代码类型
type ErrorCode string

const (
	// 通用错误
	ErrCodeInternal       ErrorCode = "INTERNAL_ERROR"
	ErrCodeInvalidRequest ErrorCode = "INVALID_REQUEST"
	ErrCodeNotFound       ErrorCode = "NOT_FOUND"
	ErrCodeUnauthorized   ErrorCode = "UNAUTHORIZED"
	ErrCodeForbidden      ErrorCode = "FORBIDDEN"
	ErrCodeConflict       ErrorCode = "CONFLICT"
	ErrCodeTimeout        ErrorCode = "TIMEOUT"

	// 数据库错误
	ErrCodeDBConnection  ErrorCode = "DB_CONNECTION_ERROR"
	ErrCodeDBTransaction ErrorCode = "DB_TRANSACTION_ERROR"
	ErrCodeDBConstraint  ErrorCode = "DB_CONSTRAINT_ERROR"
	ErrCodeDBDeadlock    ErrorCode = "DB_DEADLOCK_ERROR"

	// 代理相关错误
	ErrCodeProxyNotFound    ErrorCode = "PROXY_NOT_FOUND"
	ErrCodeProxyUnavailable ErrorCode = "PROXY_UNAVAILABLE"
	ErrCodeProxyTestFailed  ErrorCode = "PROXY_TEST_FAILED"
	ErrCodeProxyDuplicate   ErrorCode = "PROXY_DUPLICATE"

	// 用户相关错误
	ErrCodeUserNotFound       ErrorCode = "USER_NOT_FOUND"
	ErrCodeUserExists         ErrorCode = "USER_EXISTS"
	ErrCodeInvalidCredentials ErrorCode = "INVALID_CREDENTIALS"
	ErrCodeTokenExpired       ErrorCode = "TOKEN_EXPIRED"
	ErrCodeTokenInvalid       ErrorCode = "TOKEN_INVALID"

	// 任务相关错误
	ErrCodeTaskNotFound       ErrorCode = "TASK_NOT_FOUND"
	ErrCodeTaskAlreadyRunning ErrorCode = "TASK_ALREADY_RUNNING"
	ErrCodeTaskFailed         ErrorCode = "TASK_FAILED"

	// 资源相关错误
	ErrCodeResourceExhausted ErrorCode = "RESOURCE_EXHAUSTED"
	ErrCodeRateLimitExceeded ErrorCode = "RATE_LIMIT_EXCEEDED"
)

// AppError 应用程序错误
type AppError struct {
	Code       ErrorCode `json:"code"`
	Message    string    `json:"message"`
	Details    string    `json:"details,omitempty"`
	HTTPStatus int       `json:"-"`
	Cause      error     `json:"-"`
}

// Error 实现 error 接口
func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap 实现 errors.Unwrap 接口
func (e *AppError) Unwrap() error {
	return e.Cause
}

// Is 实现 errors.Is 接口
func (e *AppError) Is(target error) bool {
	if t, ok := target.(*AppError); ok {
		return e.Code == t.Code
	}
	return false
}

// NewAppError 创建应用程序错误
func NewAppError(code ErrorCode, message string, cause error) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: getHTTPStatus(code),
		Cause:      cause,
	}
}

// NewAppErrorWithDetails 创建带详细信息的应用程序错误
func NewAppErrorWithDetails(code ErrorCode, message, details string, cause error) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		Details:    details,
		HTTPStatus: getHTTPStatus(code),
		Cause:      cause,
	}
}

// getHTTPStatus 根据错误代码获取HTTP状态码
func getHTTPStatus(code ErrorCode) int {
	switch code {
	case ErrCodeInvalidRequest, ErrCodeDBConstraint:
		return http.StatusBadRequest
	case ErrCodeUnauthorized, ErrCodeInvalidCredentials, ErrCodeTokenExpired, ErrCodeTokenInvalid:
		return http.StatusUnauthorized
	case ErrCodeForbidden:
		return http.StatusForbidden
	case ErrCodeNotFound, ErrCodeProxyNotFound, ErrCodeUserNotFound, ErrCodeTaskNotFound:
		return http.StatusNotFound
	case ErrCodeConflict, ErrCodeProxyDuplicate, ErrCodeUserExists, ErrCodeTaskAlreadyRunning:
		return http.StatusConflict
	case ErrCodeTimeout:
		return http.StatusRequestTimeout
	case ErrCodeResourceExhausted, ErrCodeRateLimitExceeded:
		return http.StatusTooManyRequests
	case ErrCodeProxyUnavailable:
		return http.StatusServiceUnavailable
	default:
		return http.StatusInternalServerError
	}
}

// 预定义的常用错误
var (
	ErrInternalServer = NewAppError(ErrCodeInternal, "Internal server error", nil)
	ErrInvalidRequest = NewAppError(ErrCodeInvalidRequest, "Invalid request", nil)
	ErrNotFound       = NewAppError(ErrCodeNotFound, "Resource not found", nil)
	ErrUnauthorized   = NewAppError(ErrCodeUnauthorized, "Unauthorized", nil)
	ErrForbidden      = NewAppError(ErrCodeForbidden, "Forbidden", nil)
	ErrTimeout        = NewAppError(ErrCodeTimeout, "Request timeout", nil)

	ErrDBConnection  = NewAppError(ErrCodeDBConnection, "Database connection error", nil)
	ErrDBTransaction = NewAppError(ErrCodeDBTransaction, "Database transaction error", nil)
	ErrDBDeadlock    = NewAppError(ErrCodeDBDeadlock, "Database deadlock detected", nil)

	ErrProxyNotFound    = NewAppError(ErrCodeProxyNotFound, "Proxy not found", nil)
	ErrProxyUnavailable = NewAppError(ErrCodeProxyUnavailable, "Proxy unavailable", nil)
	ErrProxyTestFailed  = NewAppError(ErrCodeProxyTestFailed, "Proxy test failed", nil)

	ErrUserNotFound       = NewAppError(ErrCodeUserNotFound, "User not found", nil)
	ErrUserExists         = NewAppError(ErrCodeUserExists, "User already exists", nil)
	ErrInvalidCredentials = NewAppError(ErrCodeInvalidCredentials, "Invalid credentials", nil)
	ErrTokenExpired       = NewAppError(ErrCodeTokenExpired, "Token expired", nil)
	ErrTokenInvalid       = NewAppError(ErrCodeTokenInvalid, "Invalid token", nil)

	ErrTaskNotFound       = NewAppError(ErrCodeTaskNotFound, "Task not found", nil)
	ErrTaskAlreadyRunning = NewAppError(ErrCodeTaskAlreadyRunning, "Task already running", nil)
	ErrTaskFailed         = NewAppError(ErrCodeTaskFailed, "Task execution failed", nil)
)

// WrapError 包装错误
func WrapError(code ErrorCode, message string, cause error) *AppError {
	return NewAppError(code, message, cause)
}

// WrapDBError 包装数据库错误
func WrapDBError(cause error) *AppError {
	if cause == nil {
		return nil
	}

	// 检查是否是连接错误
	if isConnectionError(cause) {
		return NewAppError(ErrCodeDBConnection, "Database connection failed", cause)
	}

	// 检查是否是约束错误
	if isConstraintError(cause) {
		return NewAppError(ErrCodeDBConstraint, "Database constraint violation", cause)
	}

	// 检查是否是死锁错误
	if isDeadlockError(cause) {
		return NewAppError(ErrCodeDBDeadlock, "Database deadlock detected", cause)
	}

	// 默认为事务错误
	return NewAppError(ErrCodeDBTransaction, "Database transaction failed", cause)
}

// isConnectionError 检查是否是连接错误
func isConnectionError(err error) bool {
	errStr := err.Error()
	return contains(errStr, "connection refused") ||
		contains(errStr, "connection reset") ||
		contains(errStr, "connection timeout") ||
		contains(errStr, "no such host")
}

// isConstraintError 检查是否是约束错误
func isConstraintError(err error) bool {
	errStr := err.Error()
	return contains(errStr, "duplicate key") ||
		contains(errStr, "unique constraint") ||
		contains(errStr, "foreign key constraint") ||
		contains(errStr, "check constraint")
}

// isDeadlockError 检查是否是死锁错误
func isDeadlockError(err error) bool {
	errStr := err.Error()
	return contains(errStr, "deadlock") ||
		contains(errStr, "lock timeout")
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					containsSubstring(s, substr)))
}

// containsSubstring 简单的子字符串检查
func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// IsAppError 检查是否是应用程序错误
func IsAppError(err error) (*AppError, bool) {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr, true
	}
	return nil, false
}

// GetErrorCode 获取错误代码
func GetErrorCode(err error) ErrorCode {
	if appErr, ok := IsAppError(err); ok {
		return appErr.Code
	}
	return ErrCodeInternal
}

// GetHTTPStatus 获取HTTP状态码
func GetHTTPStatus(err error) int {
	if appErr, ok := IsAppError(err); ok {
		return appErr.HTTPStatus
	}
	return http.StatusInternalServerError
}

// ErrorResponse HTTP错误响应
type ErrorResponse struct {
	Error   string `json:"error"`
	Code    string `json:"code,omitempty"`
	Details string `json:"details,omitempty"`
}

// ToErrorResponse 转换为错误响应
func ToErrorResponse(err error) ErrorResponse {
	if appErr, ok := IsAppError(err); ok {
		return ErrorResponse{
			Error:   appErr.Message,
			Code:    string(appErr.Code),
			Details: appErr.Details,
		}
	}

	return ErrorResponse{
		Error: "Internal server error",
		Code:  string(ErrCodeInternal),
	}
}
