package resource

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// Resource 资源接口
type Resource interface {
	Close() error
	String() string
}

// ResourceManager 资源管理器
type ResourceManager struct {
	resources map[string]Resource
	mutex     sync.RWMutex
	logger    *logrus.Logger
	closed    bool
}

// NewResourceManager 创建资源管理器
func NewResourceManager(logger *logrus.Logger) *ResourceManager {
	return &ResourceManager{
		resources: make(map[string]Resource),
		logger:    logger,
	}
}

// Register 注册资源
func (rm *ResourceManager) Register(name string, resource Resource) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	if rm.closed {
		rm.logger.WithField("resource", name).Warn("Attempting to register resource after manager is closed")
		return
	}

	rm.resources[name] = resource
	rm.logger.WithField("resource", name).Debug("Resource registered")
}

// Unregister 注销资源
func (rm *ResourceManager) Unregister(name string) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	resource, exists := rm.resources[name]
	if !exists {
		return fmt.Errorf("resource %s not found", name)
	}

	delete(rm.resources, name)
	rm.logger.WithField("resource", name).Debug("Resource unregistered")

	return resource.Close()
}

// CloseAll 关闭所有资源
func (rm *ResourceManager) CloseAll() error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	if rm.closed {
		return nil
	}

	var errors []error
	for name, resource := range rm.resources {
		if err := resource.Close(); err != nil {
			rm.logger.WithError(err).WithField("resource", name).Error("Failed to close resource")
			errors = append(errors, fmt.Errorf("failed to close %s: %w", name, err))
		} else {
			rm.logger.WithField("resource", name).Debug("Resource closed")
		}
	}

	rm.resources = make(map[string]Resource)
	rm.closed = true

	if len(errors) > 0 {
		return fmt.Errorf("failed to close %d resources: %v", len(errors), errors)
	}

	return nil
}

// GetResourceCount 获取资源数量
func (rm *ResourceManager) GetResourceCount() int {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	return len(rm.resources)
}

// ListResources 列出所有资源
func (rm *ResourceManager) ListResources() []string {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	names := make([]string, 0, len(rm.resources))
	for name := range rm.resources {
		names = append(names, name)
	}
	return names
}

// ConnectionTracker 连接跟踪器
type ConnectionTracker struct {
	connections map[string]*ConnectionInfo
	mutex       sync.RWMutex
	logger      *logrus.Logger
}

// ConnectionInfo 连接信息
type ConnectionInfo struct {
	ID        string
	Type      string
	CreatedAt time.Time
	LastUsed  time.Time
	StackTrace string
}

// NewConnectionTracker 创建连接跟踪器
func NewConnectionTracker(logger *logrus.Logger) *ConnectionTracker {
	return &ConnectionTracker{
		connections: make(map[string]*ConnectionInfo),
		logger:      logger,
	}
}

// TrackConnection 跟踪连接
func (ct *ConnectionTracker) TrackConnection(id, connType string) {
	ct.mutex.Lock()
	defer ct.mutex.Unlock()

	// 获取调用栈
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	stackTrace := string(buf[:n])

	ct.connections[id] = &ConnectionInfo{
		ID:         id,
		Type:       connType,
		CreatedAt:  time.Now(),
		LastUsed:   time.Now(),
		StackTrace: stackTrace,
	}

	ct.logger.WithFields(logrus.Fields{
		"connection_id":   id,
		"connection_type": connType,
	}).Debug("Connection tracked")
}

// UntrackConnection 取消跟踪连接
func (ct *ConnectionTracker) UntrackConnection(id string) {
	ct.mutex.Lock()
	defer ct.mutex.Unlock()

	if info, exists := ct.connections[id]; exists {
		delete(ct.connections, id)
		ct.logger.WithFields(logrus.Fields{
			"connection_id":   id,
			"connection_type": info.Type,
			"duration":        time.Since(info.CreatedAt),
		}).Debug("Connection untracked")
	}
}

// UpdateLastUsed 更新最后使用时间
func (ct *ConnectionTracker) UpdateLastUsed(id string) {
	ct.mutex.Lock()
	defer ct.mutex.Unlock()

	if info, exists := ct.connections[id]; exists {
		info.LastUsed = time.Now()
	}
}

// GetActiveConnections 获取活跃连接
func (ct *ConnectionTracker) GetActiveConnections() []*ConnectionInfo {
	ct.mutex.RLock()
	defer ct.mutex.RUnlock()

	connections := make([]*ConnectionInfo, 0, len(ct.connections))
	for _, info := range ct.connections {
		connections = append(connections, info)
	}
	return connections
}

// DetectLeaks 检测连接泄漏
func (ct *ConnectionTracker) DetectLeaks(maxAge time.Duration) []*ConnectionInfo {
	ct.mutex.RLock()
	defer ct.mutex.RUnlock()

	var leaks []*ConnectionInfo
	now := time.Now()

	for _, info := range ct.connections {
		if now.Sub(info.LastUsed) > maxAge {
			leaks = append(leaks, info)
		}
	}

	if len(leaks) > 0 {
		ct.logger.WithField("leak_count", len(leaks)).Warn("Connection leaks detected")
	}

	return leaks
}

// LogConnectionStats 记录连接统计
func (ct *ConnectionTracker) LogConnectionStats() {
	ct.mutex.RLock()
	defer ct.mutex.RUnlock()

	typeCount := make(map[string]int)
	for _, info := range ct.connections {
		typeCount[info.Type]++
	}

	ct.logger.WithFields(logrus.Fields{
		"total_connections": len(ct.connections),
		"by_type":          typeCount,
	}).Info("Connection statistics")
}

// ResourceMonitor 资源监控器
type ResourceMonitor struct {
	resourceManager    *ResourceManager
	connectionTracker  *ConnectionTracker
	logger            *logrus.Logger
	stopChan          chan struct{}
	wg                sync.WaitGroup
}

// NewResourceMonitor 创建资源监控器
func NewResourceMonitor(rm *ResourceManager, ct *ConnectionTracker, logger *logrus.Logger) *ResourceMonitor {
	return &ResourceMonitor{
		resourceManager:   rm,
		connectionTracker: ct,
		logger:           logger,
		stopChan:         make(chan struct{}),
	}
}

// Start 启动监控
func (rm *ResourceMonitor) Start(ctx context.Context, interval time.Duration) {
	rm.wg.Add(1)
	go rm.monitorLoop(ctx, interval)
}

// Stop 停止监控
func (rm *ResourceMonitor) Stop() {
	close(rm.stopChan)
	rm.wg.Wait()
}

// monitorLoop 监控循环
func (rm *ResourceMonitor) monitorLoop(ctx context.Context, interval time.Duration) {
	defer rm.wg.Done()

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-rm.stopChan:
			return
		case <-ticker.C:
			rm.performCheck()
		}
	}
}

// performCheck 执行检查
func (rm *ResourceMonitor) performCheck() {
	// 记录资源统计
	resourceCount := rm.resourceManager.GetResourceCount()
	rm.logger.WithField("resource_count", resourceCount).Debug("Resource count")

	// 记录连接统计
	rm.connectionTracker.LogConnectionStats()

	// 检测连接泄漏
	leaks := rm.connectionTracker.DetectLeaks(5 * time.Minute)
	if len(leaks) > 0 {
		for _, leak := range leaks {
			rm.logger.WithFields(logrus.Fields{
				"connection_id":   leak.ID,
				"connection_type": leak.Type,
				"age":            time.Since(leak.CreatedAt),
				"last_used":      time.Since(leak.LastUsed),
				"stack_trace":    leak.StackTrace,
			}).Warn("Connection leak detected")
		}
	}

	// 记录内存统计
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	rm.logger.WithFields(logrus.Fields{
		"alloc_mb":      bToMb(m.Alloc),
		"total_alloc_mb": bToMb(m.TotalAlloc),
		"sys_mb":        bToMb(m.Sys),
		"num_gc":        m.NumGC,
		"goroutines":    runtime.NumGoroutine(),
	}).Debug("Memory statistics")
}

// bToMb 字节转MB
func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}

// SafeClose 安全关闭资源
func SafeClose(resource Resource, logger *logrus.Logger) {
	if resource == nil {
		return
	}

	if err := resource.Close(); err != nil {
		logger.WithError(err).WithField("resource", resource.String()).Error("Failed to close resource")
	}
}

// WithTimeout 带超时的操作
func WithTimeout(ctx context.Context, timeout time.Duration, operation func(context.Context) error) error {
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	done := make(chan error, 1)
	go func() {
		done <- operation(ctx)
	}()

	select {
	case err := <-done:
		return err
	case <-ctx.Done():
		return ctx.Err()
	}
}

// RetryWithBackoff 带退避的重试
func RetryWithBackoff(ctx context.Context, maxRetries int, initialDelay time.Duration, operation func() error) error {
	var lastErr error
	delay := initialDelay

	for i := 0; i < maxRetries; i++ {
		if err := operation(); err == nil {
			return nil
		} else {
			lastErr = err
		}

		if i < maxRetries-1 {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(delay):
				delay *= 2 // 指数退避
			}
		}
	}

	return fmt.Errorf("operation failed after %d retries: %w", maxRetries, lastErr)
}
