package sync

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"proxyFlow/internal/models"
	"proxyFlow/internal/repository"
	"proxyFlow/pkg/database"
)

// DualStorageSync 双存储同步器
type DualStorageSync struct {
	repoManager repository.RepositoryManager
	redisClient *database.RedisClient
	logger      *logrus.Logger
	config      *DataSyncConfig
	cacheTTL    time.Duration
}

// DataSyncConfig 数据同步配置
type DataSyncConfig struct {
	SyncInterval   time.Duration
	BatchSize      int
	EnableRealtime bool
	ConflictPolicy string
}

// NewDualStorageSync 创建双存储同步器
func NewDualStorageSync(repoManager repository.RepositoryManager, redisClient *database.RedisClient, logger *logrus.Logger, config *DataSyncConfig) *DualStorageSync {
	if config == nil {
		config = &DataSyncConfig{
			SyncInterval:   30 * time.Second,
			BatchSize:      100,
			EnableRealtime: true,
			ConflictPolicy: "latest_wins",
		}
	}

	return &DualStorageSync{
		repoManager: repoManager,
		redisClient: redisClient,
		logger:      logger,
		config:      config,
		cacheTTL:    24 * time.Hour, // 默认缓存24小时
	}
}

// Start 启动同步服务
func (s *DualStorageSync) Start(ctx context.Context) {
	s.logger.Info("Starting dual storage sync service")

	// 启动定期同步
	go s.startPeriodicSync(ctx)

	// 如果启用实时同步，启动实时同步监听
	if s.config.EnableRealtime {
		go s.startRealtimeSync(ctx)
	}
}

// startPeriodicSync 启动定期同步
func (s *DualStorageSync) startPeriodicSync(ctx context.Context) {
	ticker := time.NewTicker(s.config.SyncInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("Stopping periodic sync")
			return
		case <-ticker.C:
			if err := s.syncAll(ctx); err != nil {
				s.logger.WithError(err).Error("Periodic sync failed")
			}
		}
	}
}

// startRealtimeSync 启动实时同步监听
func (s *DualStorageSync) startRealtimeSync(ctx context.Context) {
	// 注意：当前Redis客户端不支持Subscribe，这里使用定期检查作为替代
	// 在生产环境中，建议使用支持pub/sub的Redis客户端
	s.logger.Info("Realtime sync not fully implemented, using periodic sync instead")

	// 可以在这里实现基于定期检查的"准实时"同步
	ticker := time.NewTicker(5 * time.Second) // 每5秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("Stopping realtime sync")
			return
		case <-ticker.C:
			// 执行快速同步检查
			if err := s.syncAll(ctx); err != nil {
				s.logger.WithError(err).Error("Realtime sync check failed")
			}
		}
	}
}

// handleRealtimeEvent 处理实时事件
func (s *DualStorageSync) handleRealtimeEvent(ctx context.Context, channel, payload string) {
	s.logger.WithFields(logrus.Fields{
		"channel": channel,
		"payload": payload,
	}).Debug("Received realtime sync event")

	// 根据事件类型处理
	switch payload {
	case "set", "hset":
		// 数据更新事件，同步到PostgreSQL
		go s.syncKeyToPostgres(ctx, channel)
	case "del":
		// 数据删除事件
		go s.handleDeleteEvent(ctx, channel)
	}
}

// syncAll 同步所有数据
func (s *DualStorageSync) syncAll(ctx context.Context) error {
	s.logger.Info("Starting full sync")

	// 同步用户数据
	if err := s.syncUsers(ctx); err != nil {
		return fmt.Errorf("failed to sync users: %w", err)
	}

	// 同步代理数据
	if err := s.syncProxies(ctx); err != nil {
		return fmt.Errorf("failed to sync proxies: %w", err)
	}

	s.logger.Info("Full sync completed")
	return nil
}

// syncUsers 同步用户数据
func (s *DualStorageSync) syncUsers(ctx context.Context) error {
	// 从PostgreSQL获取所有用户
	users, err := s.repoManager.GetUser().List(ctx, repository.UserFilters{})
	if err != nil {
		return fmt.Errorf("failed to get users from postgres: %w", err)
	}

	// 同步到Redis缓存
	for _, user := range users {
		key := "user:" + user.ID
		if err := s.redisClient.Set(ctx, key, user, s.cacheTTL); err != nil {
			s.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to cache user")
			continue
		}

		// 缓存索引
		usernameKey := "user:username:" + user.Username
		emailKey := "user:email:" + user.Email
		s.redisClient.Set(ctx, usernameKey, user.ID, s.cacheTTL)
		s.redisClient.Set(ctx, emailKey, user.ID, s.cacheTTL)
	}

	s.logger.WithField("count", len(users)).Info("Synced users to cache")
	return nil
}

// syncProxies 同步代理数据
func (s *DualStorageSync) syncProxies(ctx context.Context) error {
	// 从PostgreSQL获取所有代理
	proxies, err := s.repoManager.GetProxy().List(ctx, repository.ProxyFilters{})
	if err != nil {
		return fmt.Errorf("failed to get proxies from postgres: %w", err)
	}

	// 同步到Redis缓存
	for _, proxy := range proxies {
		key := "proxy:" + proxy.ID
		if err := s.redisClient.Set(ctx, key, proxy, s.cacheTTL); err != nil {
			s.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to cache proxy")
			continue
		}
	}

	s.logger.WithField("count", len(proxies)).Info("Synced proxies to cache")
	return nil
}

// syncKeyToPostgres 将Redis中的键同步到PostgreSQL
func (s *DualStorageSync) syncKeyToPostgres(ctx context.Context, keyPattern string) {
	// 解析键类型
	if contains(keyPattern, "user:") && !contains(keyPattern, "username:") && !contains(keyPattern, "email:") {
		s.syncUserToPostgres(ctx, keyPattern)
	} else if contains(keyPattern, "proxy:") {
		s.syncProxyToPostgres(ctx, keyPattern)
	}
}

// syncUserToPostgres 同步用户到PostgreSQL
func (s *DualStorageSync) syncUserToPostgres(ctx context.Context, keyPattern string) {
	// 从键模式中提取实际的键
	key := extractKeyFromPattern(keyPattern)

	var user models.User
	if err := s.redisClient.Get(ctx, key, &user); err != nil {
		s.logger.WithError(err).WithField("key", key).Error("Failed to get user from redis")
		return
	}

	// 检查PostgreSQL中是否存在
	existingUser, err := s.repoManager.GetUser().GetByID(ctx, user.ID)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to check user in postgres")
		return
	}

	if existingUser == nil {
		// 创建新用户
		if err := s.repoManager.GetUser().Create(ctx, &user); err != nil {
			s.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to create user in postgres")
		}
	} else {
		// 根据冲突策略决定是否更新
		if s.shouldUpdate(existingUser.UpdatedAt, user.UpdatedAt) {
			if err := s.repoManager.GetUser().Update(ctx, &user); err != nil {
				s.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to update user in postgres")
			}
		}
	}
}

// syncProxyToPostgres 同步代理到PostgreSQL
func (s *DualStorageSync) syncProxyToPostgres(ctx context.Context, keyPattern string) {
	// 从键模式中提取实际的键
	key := extractKeyFromPattern(keyPattern)

	var proxy models.Proxy
	if err := s.redisClient.Get(ctx, key, &proxy); err != nil {
		s.logger.WithError(err).WithField("key", key).Error("Failed to get proxy from redis")
		return
	}

	// 检查PostgreSQL中是否存在
	existingProxy, err := s.repoManager.GetProxy().GetByID(ctx, proxy.ID)
	if err != nil {
		s.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to check proxy in postgres")
		return
	}

	if existingProxy == nil {
		// 创建新代理
		if err := s.repoManager.GetProxy().Create(ctx, &proxy); err != nil {
			s.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to create proxy in postgres")
		}
	} else {
		// 根据冲突策略决定是否更新
		if s.shouldUpdate(existingProxy.UpdatedAt, proxy.UpdatedAt) {
			if err := s.repoManager.GetProxy().Update(ctx, &proxy); err != nil {
				s.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to update proxy in postgres")
			}
		}
	}
}

// handleDeleteEvent 处理删除事件
func (s *DualStorageSync) handleDeleteEvent(ctx context.Context, keyPattern string) {
	// 解析键类型并执行相应的删除操作
	if contains(keyPattern, "user:") && !contains(keyPattern, "username:") && !contains(keyPattern, "email:") {
		userID := extractIDFromKey(extractKeyFromPattern(keyPattern))
		if err := s.repoManager.GetUser().Delete(ctx, userID); err != nil {
			s.logger.WithError(err).WithField("user_id", userID).Error("Failed to delete user from postgres")
		}
	} else if contains(keyPattern, "proxy:") {
		proxyID := extractIDFromKey(extractKeyFromPattern(keyPattern))
		if err := s.repoManager.GetProxy().Delete(ctx, proxyID); err != nil {
			s.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to delete proxy from postgres")
		}
	}
}

// shouldUpdate 根据冲突策略决定是否应该更新
func (s *DualStorageSync) shouldUpdate(existingTime, newTime time.Time) bool {
	switch s.config.ConflictPolicy {
	case "latest_wins":
		return newTime.After(existingTime)
	case "postgres_wins":
		return false
	case "redis_wins":
		return true
	default:
		return newTime.After(existingTime)
	}
}

// 辅助函数
func contains(s, substr string) bool {
	return strings.Contains(s, substr)
}

func extractKeyFromPattern(keyPattern string) string {
	// 从 "__keyspace@0__:user:123" 提取 "user:123"
	if idx := len("__keyspace@0__:"); idx < len(keyPattern) {
		return keyPattern[idx:]
	}
	return keyPattern
}

func extractIDFromKey(key string) string {
	// 从 "user:123" 提取 "123"
	if idx := len("user:"); idx < len(key) && key[:idx] == "user:" {
		return key[idx:]
	}
	if idx := len("proxy:"); idx < len(key) && key[:idx] == "proxy:" {
		return key[idx:]
	}
	return ""
}
