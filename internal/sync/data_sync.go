package sync

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"

	"proxyFlow/internal/models"
	"proxyFlow/internal/repository"
)

// DataSyncService 数据同步服务
type DataSyncService struct {
	redisClient *redis.Client
	repository  *repository.Repository
	logger      *logrus.Logger
	config      *SyncConfig
}

// SyncConfig 同步配置
type SyncConfig struct {
	SyncInterval   time.Duration `mapstructure:"sync_interval"`
	BatchSize      int           `mapstructure:"batch_size"`
	EnableRealtime bool          `mapstructure:"enable_realtime"`
	ConflictPolicy string        `mapstructure:"conflict_policy"` // "redis_wins", "postgres_wins", "latest_wins"
}

// NewDataSyncService 创建数据同步服务
func NewDataSyncService(redisClient *redis.Client, repository *repository.Repository, logger *logrus.Logger, config *SyncConfig) *DataSyncService {
	if config == nil {
		config = &SyncConfig{
			SyncInterval:   30 * time.Second,
			BatchSize:      100,
			EnableRealtime: true,
			ConflictPolicy: "latest_wins",
		}
	}

	return &DataSyncService{
		redisClient: redisClient,
		repository:  repository,
		logger:      logger,
		config:      config,
	}
}

// Start 启动数据同步服务
func (s *DataSyncService) Start(ctx context.Context) error {
	s.logger.Info("Starting data sync service")

	// 启动定时同步
	go s.startPeriodicSync(ctx)

	// 如果启用实时同步，启动Redis监听
	if s.config.EnableRealtime {
		go s.startRealtimeSync(ctx)
	}

	return nil
}

// startPeriodicSync 启动定时同步
func (s *DataSyncService) startPeriodicSync(ctx context.Context) {
	ticker := time.NewTicker(s.config.SyncInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("Stopping periodic sync")
			return
		case <-ticker.C:
			if err := s.syncAll(ctx); err != nil {
				s.logger.WithError(err).Error("Periodic sync failed")
			}
		}
	}
}

// startRealtimeSync 启动实时同步（Redis键空间通知）
func (s *DataSyncService) startRealtimeSync(ctx context.Context) {
	// 订阅Redis键空间通知
	pubsub := s.redisClient.PSubscribe(ctx, "__keyspace@0__:proxy:*")
	defer pubsub.Close()

	ch := pubsub.Channel()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("Stopping realtime sync")
			return
		case msg := <-ch:
			if err := s.handleKeyspaceNotification(ctx, msg); err != nil {
				s.logger.WithError(err).Error("Failed to handle keyspace notification")
			}
		}
	}
}

// handleKeyspaceNotification 处理键空间通知
func (s *DataSyncService) handleKeyspaceNotification(ctx context.Context, msg *redis.Message) error {
	// 解析键名获取代理ID
	keyParts := strings.Split(msg.Channel, ":")
	if len(keyParts) < 3 {
		return nil
	}

	proxyID := keyParts[2]
	operation := msg.Payload

	switch operation {
	case "set", "hset":
		return s.syncProxyFromRedis(ctx, proxyID)
	case "del":
		return s.deleteProxyFromPostgres(ctx, proxyID)
	}

	return nil
}

// syncAll 同步所有数据
func (s *DataSyncService) syncAll(ctx context.Context) error {
	s.logger.Info("Starting full data sync")

	// 同步代理数据
	if err := s.syncProxies(ctx); err != nil {
		return fmt.Errorf("failed to sync proxies: %w", err)
	}

	// 同步用户数据
	if err := s.syncUsers(ctx); err != nil {
		return fmt.Errorf("failed to sync users: %w", err)
	}

	s.logger.Info("Full data sync completed")
	return nil
}

// syncProxies 同步代理数据
func (s *DataSyncService) syncProxies(ctx context.Context) error {
	// 获取Redis中的所有代理键
	keys, err := s.redisClient.Keys(ctx, "proxy:*").Result()
	if err != nil {
		return fmt.Errorf("failed to get proxy keys from Redis: %w", err)
	}

	// 批量处理
	for i := 0; i < len(keys); i += s.config.BatchSize {
		end := i + s.config.BatchSize
		if end > len(keys) {
			end = len(keys)
		}

		batch := keys[i:end]
		if err := s.syncProxyBatch(ctx, batch); err != nil {
			s.logger.WithError(err).WithField("batch_size", len(batch)).Error("Failed to sync proxy batch")
			continue
		}
	}

	return nil
}

// syncProxyBatch 批量同步代理
func (s *DataSyncService) syncProxyBatch(ctx context.Context, keys []string) error {
	var proxies []*models.Proxy

	for _, key := range keys {
		proxyID := strings.TrimPrefix(key, "proxy:")

		// 从Redis获取代理数据
		data, err := s.redisClient.Get(ctx, key).Result()
		if err != nil {
			if err == redis.Nil {
				continue
			}
			return fmt.Errorf("failed to get proxy %s from Redis: %w", proxyID, err)
		}

		var proxy models.Proxy
		if err := json.Unmarshal([]byte(data), &proxy); err != nil {
			s.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to unmarshal proxy data")
			continue
		}

		proxies = append(proxies, &proxy)
	}

	if len(proxies) == 0 {
		return nil
	}

	// 检查PostgreSQL中是否存在这些代理
	for _, proxy := range proxies {
		existingProxy, err := s.repository.Proxy.GetByID(ctx, proxy.ID)
		if err != nil {
			return fmt.Errorf("failed to check proxy existence: %w", err)
		}

		if existingProxy == nil {
			// 代理不存在，创建新的
			if err := s.repository.Proxy.Create(ctx, proxy); err != nil {
				s.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to create proxy in PostgreSQL")
				continue
			}
		} else {
			// 代理存在，根据冲突策略处理
			if err := s.resolveProxyConflict(ctx, proxy, existingProxy); err != nil {
				s.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to resolve proxy conflict")
				continue
			}
		}
	}

	return nil
}

// resolveProxyConflict 解决代理冲突
func (s *DataSyncService) resolveProxyConflict(ctx context.Context, redisProxy, pgProxy *models.Proxy) error {
	var targetProxy *models.Proxy

	switch s.config.ConflictPolicy {
	case "redis_wins":
		targetProxy = redisProxy
	case "postgres_wins":
		targetProxy = pgProxy
	case "latest_wins":
		if redisProxy.UpdatedAt.After(pgProxy.UpdatedAt) {
			targetProxy = redisProxy
		} else {
			targetProxy = pgProxy
		}
	default:
		targetProxy = redisProxy
	}

	// 更新目标数据源
	if targetProxy == redisProxy {
		// Redis数据更新，更新PostgreSQL
		return s.repository.Proxy.Update(ctx, redisProxy)
	} else {
		// PostgreSQL数据更新，更新Redis
		return s.syncProxyToRedis(ctx, pgProxy)
	}
}

// syncProxyFromRedis 从Redis同步单个代理到PostgreSQL
func (s *DataSyncService) syncProxyFromRedis(ctx context.Context, proxyID string) error {
	// 从Redis获取代理数据
	data, err := s.redisClient.Get(ctx, "proxy:"+proxyID).Result()
	if err != nil {
		if err == redis.Nil {
			return nil
		}
		return fmt.Errorf("failed to get proxy from Redis: %w", err)
	}

	var proxy models.Proxy
	if err := json.Unmarshal([]byte(data), &proxy); err != nil {
		return fmt.Errorf("failed to unmarshal proxy data: %w", err)
	}

	// 检查PostgreSQL中是否存在
	existingProxy, err := s.repository.Proxy.GetByID(ctx, proxyID)
	if err != nil {
		return fmt.Errorf("failed to check proxy existence: %w", err)
	}

	if existingProxy == nil {
		return s.repository.Proxy.Create(ctx, &proxy)
	} else {
		return s.resolveProxyConflict(ctx, &proxy, existingProxy)
	}
}

// syncProxyToRedis 同步代理到Redis
func (s *DataSyncService) syncProxyToRedis(ctx context.Context, proxy *models.Proxy) error {
	data, err := json.Marshal(proxy)
	if err != nil {
		return fmt.Errorf("failed to marshal proxy data: %w", err)
	}

	return s.redisClient.Set(ctx, "proxy:"+proxy.ID, data, 0).Err()
}

// deleteProxyFromPostgres 从PostgreSQL删除代理
func (s *DataSyncService) deleteProxyFromPostgres(ctx context.Context, proxyID string) error {
	return s.repository.Proxy.Delete(ctx, proxyID)
}

// syncUsers 同步用户数据
func (s *DataSyncService) syncUsers(ctx context.Context) error {
	// 获取Redis中的所有用户键
	keys, err := s.redisClient.Keys(ctx, "user:*").Result()
	if err != nil {
		return fmt.Errorf("failed to get user keys from Redis: %w", err)
	}

	for _, key := range keys {
		userID := strings.TrimPrefix(key, "user:")

		// 从Redis获取用户数据
		data, err := s.redisClient.Get(ctx, key).Result()
		if err != nil {
			if err == redis.Nil {
				continue
			}
			s.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user from Redis")
			continue
		}

		var user models.User
		if err := json.Unmarshal([]byte(data), &user); err != nil {
			s.logger.WithError(err).WithField("user_id", userID).Error("Failed to unmarshal user data")
			continue
		}

		// 检查PostgreSQL中是否存在
		existingUser, err := s.repository.User.GetByID(ctx, userID)
		if err != nil {
			s.logger.WithError(err).WithField("user_id", userID).Error("Failed to check user existence")
			continue
		}

		if existingUser == nil {
			if err := s.repository.User.Create(ctx, &user); err != nil {
				s.logger.WithError(err).WithField("user_id", userID).Error("Failed to create user in PostgreSQL")
			}
		} else {
			if err := s.repository.User.Update(ctx, &user); err != nil {
				s.logger.WithError(err).WithField("user_id", userID).Error("Failed to update user in PostgreSQL")
			}
		}
	}

	return nil
}

// GetSyncStatus 获取同步状态
func (s *DataSyncService) GetSyncStatus(ctx context.Context) (*SyncStatus, error) {
	// 统计Redis和PostgreSQL中的数据量
	redisProxyCount, err := s.redisClient.Keys(ctx, "proxy:*").Result()
	if err != nil {
		return nil, fmt.Errorf("failed to count Redis proxies: %w", err)
	}

	pgProxyCount, err := s.repository.Proxy.Count(ctx, repository.ProxyFilters{})
	if err != nil {
		return nil, fmt.Errorf("failed to count PostgreSQL proxies: %w", err)
	}

	return &SyncStatus{
		RedisProxyCount:     len(redisProxyCount),
		PostgresProxyCount:  int(pgProxyCount),
		LastSyncTime:        time.Now(),
		SyncInterval:        s.config.SyncInterval,
		RealtimeSyncEnabled: s.config.EnableRealtime,
	}, nil
}

// SyncStatus 同步状态
type SyncStatus struct {
	RedisProxyCount     int           `json:"redis_proxy_count"`
	PostgresProxyCount  int           `json:"postgres_proxy_count"`
	LastSyncTime        time.Time     `json:"last_sync_time"`
	SyncInterval        time.Duration `json:"sync_interval"`
	RealtimeSyncEnabled bool          `json:"realtime_sync_enabled"`
}
