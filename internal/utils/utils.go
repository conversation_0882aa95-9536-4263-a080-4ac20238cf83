package utils

import (
	"math/rand"
	"time"
)

var TEST_URLS = []string{
	"http://httpbin.org/ip",                       // 返回JSON格式的IP
	"https://api.ipify.org?format=json",           // 简单JSON IP API
	"https://api.myip.com",                        // JSON格式的IP和地理信息
	"https://wtfismyip.com/json",                  // JSON格式的IP
	"https://api.iplocation.net/?cmd=get-ip",      // JSON格式的IP
	"https://ip-api.com/json",                     // JSON格式的IP和地理信息
	"https://api64.ipify.org?format=json",         // 支持IPv6的JSON IP API
	"https://ipwho.is",                            // JSON格式的IP和地理信息
	"https://get.geojs.io/v1/ip.json",             // JSON格式的IP
	"https://ipapi.co/json",                       // JSON格式的IP和地理信息
	"https://api.bigdatacloud.net/data/client-ip", // JSON格式的IP
	"https://jsonip.com",                          // JSON 格式，同时支持 JSONP
	"https://freeipapi.com/api/json",              // JSON 格式的地理位置信息
	"https://api.db-ip.com/v2/free/self",          // JSON 格式，包含地理位置和ASN信息

	// --- 纯文本格式 ---
	"https://ifconfig.me/ip",        // 纯文本IP
	"http://icanhazip.com",          // 纯文本IP
	"https://ipinfo.io/ip",          // 纯文本IP
	"https://checkip.amazonaws.com", // AWS提供的纯文本IP
	"http://ip4only.me/api/",        // 纯文本IPv4
	"https://myip.dnsomatic.com",    // 纯文本IP
	"https://ipecho.net/plain",      // 纯文本IP
	"https://ident.me",              // 纯文本IP
	"https://ip2c.org/self",         // 纯文本IP
	"https://api.ip.sb/ip",          // 纯文本IP
	"https://api.ip.sb/geoip",       // 纯文本IP
	"https://myexternalip.com/raw",  // 纯文本IP
	"https://ifconfig.co/ip",        // 纯文本IP
	"https://ip.seeip.org",          // 纯文本IP
	"https://ipget.net",             // 纯文本IP
	"https://api.ipify.org",         // 纯文本 IP (最常用之一)
	"https://ident.me",              // 纯文本 IP
	"https://l2.io/ip",              // 纯文本 IP

	// --- IPv6 查询 (如果网络支持) ---
	"https://v6.ident.me", // 纯文本 IPv6
	// --- 其他备用 ---
	"https://ipecho.net/plain", // 纯文本 IP
}

func RandUserAgent() string {
	// List of common User-Agent strings
	userAgents := []string{
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59",
		"Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
		"Mozilla/5.0 (Android 11; Mobile; rv:89.0) Gecko/89.0 Firefox/89.0",
		"Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
	}

	rand.New(rand.NewSource(time.Now().UnixNano()))

	return userAgents[rand.Intn(len(userAgents))]
}
