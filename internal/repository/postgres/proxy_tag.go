package postgres

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"

	"proxyFlow/internal/models"
	"proxyFlow/internal/repository"
)

// ProxyTagRepository PostgreSQL代理标签仓储实现
type ProxyTagRepository struct {
	db     *sqlx.DB
	logger *logrus.Logger
}

// NewProxyTagRepository 创建代理标签仓储
func NewProxyTagRepository(db *sqlx.DB, logger *logrus.Logger) repository.ProxyTagRepository {
	return &ProxyTagRepository{
		db:     db,
		logger: logger,
	}
}

// Create 创建标签
func (r *ProxyTagRepository) Create(ctx context.Context, tag *models.ProxyTag) error {
	var query string
	var err error

	if tag.ID == "" {
		// 让数据库自动生成 UUID
		query = `
			INSERT INTO proxy_tags (name, description, color, created_by)
			VALUES (:name, :description, :color, :created_by)
			RETURNING id`
		err = r.db.GetContext(ctx, &tag.ID, query, tag)
	} else {
		// 使用提供的 ID
		query = `
			INSERT INTO proxy_tags (id, name, description, color, created_by)
			VALUES (:id, :name, :description, :color, :created_by)`
		_, err = r.db.NamedExecContext(ctx, query, tag)
	}

	if err != nil {
		r.logger.WithError(err).Error("Failed to create proxy tag")
		return fmt.Errorf("failed to create proxy tag: %w", err)
	}

	return nil
}

// GetByID 根据ID获取标签
func (r *ProxyTagRepository) GetByID(ctx context.Context, id string) (*models.ProxyTag, error) {
	var tag models.ProxyTag
	query := `SELECT * FROM proxy_tags WHERE id = $1`

	err := r.db.GetContext(ctx, &tag, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("tag_id", id).Error("Failed to get proxy tag by ID")
		return nil, fmt.Errorf("failed to get proxy tag by ID: %w", err)
	}

	return &tag, nil
}

// GetByName 根据名称获取标签
func (r *ProxyTagRepository) GetByName(ctx context.Context, name string) (*models.ProxyTag, error) {
	var tag models.ProxyTag
	query := `SELECT * FROM proxy_tags WHERE name = $1`

	err := r.db.GetContext(ctx, &tag, query, name)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("tag_name", name).Error("Failed to get proxy tag by name")
		return nil, fmt.Errorf("failed to get proxy tag by name: %w", err)
	}

	return &tag, nil
}

// Update 更新标签
func (r *ProxyTagRepository) Update(ctx context.Context, tag *models.ProxyTag) error {
	tag.UpdatedAt = time.Now()

	query := `
		UPDATE proxy_tags SET
			name = :name, description = :description, color = :color,
			updated_at = :updated_at
		WHERE id = :id`

	result, err := r.db.NamedExecContext(ctx, query, tag)
	if err != nil {
		r.logger.WithError(err).WithField("tag_id", tag.ID).Error("Failed to update proxy tag")
		return fmt.Errorf("failed to update proxy tag: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("proxy tag not found")
	}

	return nil
}

// Delete 删除标签
func (r *ProxyTagRepository) Delete(ctx context.Context, id string) error {
	// 先删除关联关系
	_, err := r.db.ExecContext(ctx, "DELETE FROM proxy_tag_assignments WHERE tag_id = $1", id)
	if err != nil {
		r.logger.WithError(err).WithField("tag_id", id).Error("Failed to delete proxy tag assignments")
		return fmt.Errorf("failed to delete proxy tag assignments: %w", err)
	}

	// 再删除标签
	result, err := r.db.ExecContext(ctx, "DELETE FROM proxy_tags WHERE id = $1", id)
	if err != nil {
		r.logger.WithError(err).WithField("tag_id", id).Error("Failed to delete proxy tag")
		return fmt.Errorf("failed to delete proxy tag: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("proxy tag not found")
	}

	return nil
}

// List 获取所有标签
func (r *ProxyTagRepository) List(ctx context.Context) ([]*models.ProxyTag, error) {
	query := `SELECT * FROM proxy_tags ORDER BY name`

	var tags []*models.ProxyTag
	err := r.db.SelectContext(ctx, &tags, query)
	if err != nil {
		r.logger.WithError(err).Error("Failed to list proxy tags")
		return nil, fmt.Errorf("failed to list proxy tags: %w", err)
	}

	return tags, nil
}

// GetByProxyID 根据代理ID获取标签
func (r *ProxyTagRepository) GetByProxyID(ctx context.Context, proxyID string) ([]*models.ProxyTag, error) {
	query := `
		SELECT pt.id, pt.name, pt.description, pt.color, pt.created_at, pt.updated_at, pt.created_by
		FROM proxy_tags pt
		INNER JOIN proxy_tag_assignments pta ON pt.id = pta.tag_id
		WHERE pta.proxy_id = $1
		ORDER BY pt.name`

	var tags []*models.ProxyTag
	err := r.db.SelectContext(ctx, &tags, query, proxyID)
	if err != nil {
		r.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to get proxy tags")
		return nil, fmt.Errorf("failed to get proxy tags: %w", err)
	}

	return tags, nil
}
