package postgres

import (
	"context"
	"fmt"

	"proxyFlow/internal/models"

	"github.com/sirupsen/logrus"
)

// AssignPermissionToUser 为用户分配权限
func (r *RBACRepository) AssignPermissionToUser(ctx context.Context, userID string, permissionID string, granted bool, assignedBy string) error {
	query := `
		INSERT INTO user_permissions (user_id, permission_id, granted, created_by)
		VALUES ($1, $2, $3, $4)
		ON CONFLICT (user_id, permission_id) 
		DO UPDATE SET granted = $3, created_by = $4, created_at = CURRENT_TIMESTAMP`

	_, err := r.db.ExecContext(ctx, query, userID, permissionID, granted, assignedBy)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":       userID,
			"permission_id": permissionID,
			"granted":       granted,
		}).Error("Failed to assign permission to user")
		return fmt.Errorf("failed to assign permission to user: %w", err)
	}

	return nil
}

// RemovePermissionFromUser 移除用户的权限
func (r *RBACRepository) RemovePermissionFromUser(ctx context.Context, userID string, permissionID string) error {
	query := `DELETE FROM user_permissions WHERE user_id = $1 AND permission_id = $2`

	_, err := r.db.ExecContext(ctx, query, userID, permissionID)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":       userID,
			"permission_id": permissionID,
		}).Error("Failed to remove permission from user")
		return fmt.Errorf("failed to remove permission from user: %w", err)
	}

	return nil
}

// GetUserPermissions 获取用户的直接权限
func (r *RBACRepository) GetUserPermissions(ctx context.Context, userID string) ([]*models.Permission, error) {
	query := `
		SELECT p.id, p.name, p.resource, p.action, p.description, p.created_at, p.updated_at
		FROM permissions p
		INNER JOIN user_permissions up ON p.id = up.permission_id
		WHERE up.user_id = $1 AND up.granted = TRUE 
		AND (up.expires_at IS NULL OR up.expires_at > NOW())
		ORDER BY p.resource, p.action`

	var permissions []*models.Permission
	err := r.db.SelectContext(ctx, &permissions, query, userID)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user permissions")
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}

	return permissions, nil
}

// GetUserEffectivePermissions 获取用户的有效权限（角色权限+直接权限）
func (r *RBACRepository) GetUserEffectivePermissions(ctx context.Context, userID string) ([]*models.Permission, error) {
	query := `
		WITH role_permissions AS (
			SELECT DISTINCT p.id, p.name, p.resource, p.action, p.description, p.created_at, p.updated_at
			FROM permissions p
			INNER JOIN role_permissions rp ON p.id = rp.permission_id
			INNER JOIN user_role_assignments ura ON rp.role_id = ura.role_id
			WHERE ura.user_id = $1 AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
		),
		user_permissions AS (
			SELECT p.id, p.name, p.resource, p.action, p.description, p.created_at, p.updated_at,
				   up.granted
			FROM permissions p
			INNER JOIN user_permissions up ON p.id = up.permission_id
			WHERE up.user_id = $1 AND (up.expires_at IS NULL OR up.expires_at > NOW())
		)
		SELECT DISTINCT rp.id, rp.name, rp.resource, rp.action, rp.description, rp.created_at, rp.updated_at
		FROM role_permissions rp
		WHERE NOT EXISTS (
			SELECT 1 FROM user_permissions up 
			WHERE up.id = rp.id AND up.granted = FALSE
		)
		UNION
		SELECT up.id, up.name, up.resource, up.action, up.description, up.created_at, up.updated_at
		FROM user_permissions up
		WHERE up.granted = TRUE
		ORDER BY resource, action`

	var permissions []*models.Permission
	err := r.db.SelectContext(ctx, &permissions, query, userID)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user effective permissions")
		return nil, fmt.Errorf("failed to get user effective permissions: %w", err)
	}

	return permissions, nil
}

// CreateTeam 创建团队
func (r *RBACRepository) CreateTeam(ctx context.Context, team *models.Team) error {
	var query string
	var err error

	if team.ID == "" {
		query = `
			INSERT INTO teams (name, description, created_by)
			VALUES ($1, $2, $3)
			RETURNING id`
		err = r.db.QueryRowContext(ctx, query,
			team.Name, team.Description, team.CreatedBy).Scan(&team.ID)
	} else {
		query = `
			INSERT INTO teams (id, name, description, created_by)
			VALUES ($1, $2, $3, $4)`
		_, err = r.db.ExecContext(ctx, query,
			team.ID, team.Name, team.Description, team.CreatedBy)
	}

	if err != nil {
		r.logger.WithError(err).Error("Failed to create team")
		return fmt.Errorf("failed to create team: %w", err)
	}

	return nil
}

// GetTeamByID 根据ID获取团队
func (r *RBACRepository) GetTeamByID(ctx context.Context, id string) (*models.Team, error) {
	var team models.Team
	query := `SELECT * FROM teams WHERE id = $1`

	err := r.db.GetContext(ctx, &team, query, id)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return nil, nil
		}
		r.logger.WithError(err).WithField("team_id", id).Error("Failed to get team by ID")
		return nil, fmt.Errorf("failed to get team by ID: %w", err)
	}

	return &team, nil
}

// ListTeams 获取所有团队
func (r *RBACRepository) ListTeams(ctx context.Context) ([]*models.Team, error) {
	query := `SELECT * FROM teams ORDER BY name`

	var teams []*models.Team
	err := r.db.SelectContext(ctx, &teams, query)
	if err != nil {
		r.logger.WithError(err).Error("Failed to list teams")
		return nil, fmt.Errorf("failed to list teams: %w", err)
	}

	return teams, nil
}

// UpdateTeam 更新团队
func (r *RBACRepository) UpdateTeam(ctx context.Context, team *models.Team) error {
	query := `
		UPDATE teams SET
			name = $2, description = $3, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query, team.ID, team.Name, team.Description)
	if err != nil {
		r.logger.WithError(err).WithField("team_id", team.ID).Error("Failed to update team")
		return fmt.Errorf("failed to update team: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("team not found")
	}

	return nil
}

// DeleteTeam 删除团队
func (r *RBACRepository) DeleteTeam(ctx context.Context, id string) error {
	// 先删除团队成员
	_, err := r.db.ExecContext(ctx, "DELETE FROM team_members WHERE team_id = $1", id)
	if err != nil {
		r.logger.WithError(err).WithField("team_id", id).Error("Failed to delete team members")
		return fmt.Errorf("failed to delete team members: %w", err)
	}

	// 删除团队
	result, err := r.db.ExecContext(ctx, "DELETE FROM teams WHERE id = $1", id)
	if err != nil {
		r.logger.WithError(err).WithField("team_id", id).Error("Failed to delete team")
		return fmt.Errorf("failed to delete team: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("team not found")
	}

	return nil
}

// AddTeamMember 添加团队成员
func (r *RBACRepository) AddTeamMember(ctx context.Context, teamID, userID, roleID string, addedBy string) error {
	query := `
		INSERT INTO team_members (team_id, user_id, role_id, added_by)
		VALUES ($1, $2, $3, $4)
		ON CONFLICT (team_id, user_id) 
		DO UPDATE SET role_id = $3, added_by = $4, joined_at = CURRENT_TIMESTAMP`

	_, err := r.db.ExecContext(ctx, query, teamID, userID, roleID, addedBy)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"team_id": teamID,
			"user_id": userID,
			"role_id": roleID,
		}).Error("Failed to add team member")
		return fmt.Errorf("failed to add team member: %w", err)
	}

	return nil
}

// RemoveTeamMember 移除团队成员
func (r *RBACRepository) RemoveTeamMember(ctx context.Context, teamID, userID string) error {
	query := `DELETE FROM team_members WHERE team_id = $1 AND user_id = $2`

	_, err := r.db.ExecContext(ctx, query, teamID, userID)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"team_id": teamID,
			"user_id": userID,
		}).Error("Failed to remove team member")
		return fmt.Errorf("failed to remove team member: %w", err)
	}

	return nil
}

// GetTeamMembers 获取团队成员
func (r *RBACRepository) GetTeamMembers(ctx context.Context, teamID string) ([]*models.UserWithRoles, error) {
	query := `
		SELECT u.id, u.username, u.email, u.role as user_role, u.is_active, 
			   u.created_at, u.updated_at, u.last_login,
			   r.id as team_role_id, r.name as team_role_name, r.display_name as team_role_display_name
		FROM users u
		INNER JOIN team_members tm ON u.id = tm.user_id
		INNER JOIN roles r ON tm.role_id = r.id
		WHERE tm.team_id = $1
		ORDER BY u.username`

	rows, err := r.db.QueryContext(ctx, query, teamID)
	if err != nil {
		r.logger.WithError(err).WithField("team_id", teamID).Error("Failed to get team members")
		return nil, fmt.Errorf("failed to get team members: %w", err)
	}
	defer rows.Close()

	var members []*models.UserWithRoles
	for rows.Next() {
		var member models.UserWithRoles
		var teamRole models.Role

		err := rows.Scan(
			&member.ID, &member.Username, &member.Email, &member.Role, &member.IsActive,
			&member.CreatedAt, &member.UpdatedAt, &member.LastLogin,
			&teamRole.ID, &teamRole.Name, &teamRole.DisplayName,
		)
		if err != nil {
			r.logger.WithError(err).Error("Failed to scan team member")
			continue
		}

		member.Roles = []*models.Role{&teamRole}
		members = append(members, &member)
	}

	return members, nil
}

// GetUserTeams 获取用户所在的团队
func (r *RBACRepository) GetUserTeams(ctx context.Context, userID string) ([]*models.Team, error) {
	query := `
		SELECT t.id, t.name, t.description, t.created_at, t.updated_at, t.created_by
		FROM teams t
		INNER JOIN team_members tm ON t.id = tm.team_id
		WHERE tm.user_id = $1
		ORDER BY t.name`

	var teams []*models.Team
	err := r.db.SelectContext(ctx, &teams, query, userID)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user teams")
		return nil, fmt.Errorf("failed to get user teams: %w", err)
	}

	return teams, nil
}

// HasPermission 检查用户是否有指定权限
func (r *RBACRepository) HasPermission(ctx context.Context, userID, permission string) (bool, error) {
	query := `
		WITH user_effective_permissions AS (
			-- 从角色获得的权限
			SELECT DISTINCT p.name
			FROM permissions p
			INNER JOIN role_permissions rp ON p.id = rp.permission_id
			INNER JOIN user_role_assignments ura ON rp.role_id = ura.role_id
			WHERE ura.user_id = $1 AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
			
			UNION
			
			-- 直接分配的权限（只包括granted=true的）
			SELECT p.name
			FROM permissions p
			INNER JOIN user_permissions up ON p.id = up.permission_id
			WHERE up.user_id = $1 AND up.granted = TRUE 
			AND (up.expires_at IS NULL OR up.expires_at > NOW())
		),
		user_denied_permissions AS (
			-- 直接拒绝的权限
			SELECT p.name
			FROM permissions p
			INNER JOIN user_permissions up ON p.id = up.permission_id
			WHERE up.user_id = $1 AND up.granted = FALSE 
			AND (up.expires_at IS NULL OR up.expires_at > NOW())
		)
		SELECT EXISTS(
			SELECT 1 FROM user_effective_permissions uep 
			WHERE uep.name = $2
			AND NOT EXISTS (
				SELECT 1 FROM user_denied_permissions udp 
				WHERE udp.name = $2
			)
		)`

	var hasPermission bool
	err := r.db.QueryRowContext(ctx, query, userID, permission).Scan(&hasPermission)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":    userID,
			"permission": permission,
		}).Error("Failed to check user permission")
		return false, fmt.Errorf("failed to check user permission: %w", err)
	}

	return hasPermission, nil
}

// HasResourceAccess 检查用户是否有资源访问权限
func (r *RBACRepository) HasResourceAccess(ctx context.Context, userID, resourceID, resourceType, permission string) (bool, error) {
	query := `
		SELECT EXISTS(
			SELECT 1 FROM resource_access ra
			WHERE ra.resource_id = $2 AND ra.resource_type = $3 AND ra.permission = $4
			AND (
				(ra.subject_type = 'user' AND ra.subject_id = $1)
				OR 
				(ra.subject_type = 'team' AND ra.subject_id IN (
					SELECT tm.team_id FROM team_members tm WHERE tm.user_id = $1
				))
			)
		)`

	var hasAccess bool
	err := r.db.QueryRowContext(ctx, query, userID, resourceID, resourceType, permission).Scan(&hasAccess)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":       userID,
			"resource_id":   resourceID,
			"resource_type": resourceType,
			"permission":    permission,
		}).Error("Failed to check resource access")
		return false, fmt.Errorf("failed to check resource access: %w", err)
	}

	return hasAccess, nil
}
