package postgres

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"proxyFlow/internal/repository"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"
)

// UserSettingsRepository 用户设置仓储实现
type UserSettingsRepository struct {
	db     *sqlx.DB
	logger *logrus.Logger
}

// NewUserSettingsRepository 创建用户设置仓储
func NewUserSettingsRepository(db *sqlx.DB, logger *logrus.Logger) repository.UserSettingsRepository {
	return &UserSettingsRepository{
		db:     db,
		logger: logger,
	}
}

// Get 获取用户设置
func (r *UserSettingsRepository) Get(ctx context.Context, userID, key string) (interface{}, error) {
	query := `SELECT setting_value FROM user_settings WHERE user_id = $1 AND setting_key = $2`

	var valueJSON []byte
	err := r.db.QueryRowContext(ctx, query, userID, key).Scan(&valueJSON)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithFields(logrus.Fields{
			"user_id": userID,
			"key":     key,
		}).Error("Failed to get user setting")
		return nil, fmt.Errorf("failed to get user setting: %w", err)
	}

	var value interface{}
	if err := json.Unmarshal(valueJSON, &value); err != nil {
		r.logger.WithError(err).Error("Failed to unmarshal setting value")
		return nil, fmt.Errorf("failed to unmarshal setting value: %w", err)
	}

	return value, nil
}

// Set 设置用户设置
func (r *UserSettingsRepository) Set(ctx context.Context, userID, key string, value interface{}) error {
	valueJSON, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal setting value: %w", err)
	}

	query := `
		INSERT INTO user_settings (user_id, setting_key, setting_value, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (user_id, setting_key)
		DO UPDATE SET setting_value = EXCLUDED.setting_value, updated_at = EXCLUDED.updated_at`

	now := time.Now()
	_, err = r.db.ExecContext(ctx, query, userID, key, valueJSON, now, now)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"user_id": userID,
			"key":     key,
		}).Error("Failed to set user setting")
		return fmt.Errorf("failed to set user setting: %w", err)
	}

	return nil
}

// GetAll 获取用户所有设置
func (r *UserSettingsRepository) GetAll(ctx context.Context, userID string) (map[string]interface{}, error) {
	query := `SELECT setting_key, setting_value FROM user_settings WHERE user_id = $1`

	rows, err := r.db.QueryContext(ctx, query, userID)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).Error("Failed to get all user settings")
		return nil, fmt.Errorf("failed to get all user settings: %w", err)
	}
	defer rows.Close()

	settings := make(map[string]interface{})
	for rows.Next() {
		var key string
		var valueJSON []byte

		err := rows.Scan(&key, &valueJSON)
		if err != nil {
			r.logger.WithError(err).Error("Failed to scan setting row")
			continue
		}

		var value interface{}
		if err := json.Unmarshal(valueJSON, &value); err != nil {
			r.logger.WithError(err).WithField("key", key).Error("Failed to unmarshal setting value")
			continue
		}

		settings[key] = value
	}

	return settings, nil
}

// SetMultiple 批量设置用户设置
func (r *UserSettingsRepository) SetMultiple(ctx context.Context, userID string, settings map[string]interface{}) error {
	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	query := `
		INSERT INTO user_settings (user_id, setting_key, setting_value, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (user_id, setting_key)
		DO UPDATE SET setting_value = EXCLUDED.setting_value, updated_at = EXCLUDED.updated_at`

	now := time.Now()
	for key, value := range settings {
		valueJSON, err := json.Marshal(value)
		if err != nil {
			r.logger.WithError(err).WithField("key", key).Error("Failed to marshal setting value")
			continue
		}

		_, err = tx.ExecContext(ctx, query, userID, key, valueJSON, now, now)
		if err != nil {
			r.logger.WithError(err).WithFields(logrus.Fields{
				"user_id": userID,
				"key":     key,
			}).Error("Failed to set user setting in transaction")
			return fmt.Errorf("failed to set user setting: %w", err)
		}
	}

	if err := tx.Commit(); err != nil {
		r.logger.WithError(err).Error("Failed to commit settings transaction")
		return fmt.Errorf("failed to commit settings transaction: %w", err)
	}

	return nil
}

// Delete 删除用户设置
func (r *UserSettingsRepository) Delete(ctx context.Context, userID, key string) error {
	query := `DELETE FROM user_settings WHERE user_id = $1 AND setting_key = $2`

	_, err := r.db.ExecContext(ctx, query, userID, key)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"user_id": userID,
			"key":     key,
		}).Error("Failed to delete user setting")
		return fmt.Errorf("failed to delete user setting: %w", err)
	}

	return nil
}

// DeleteAll 删除用户所有设置
func (r *UserSettingsRepository) DeleteAll(ctx context.Context, userID string) error {
	query := `DELETE FROM user_settings WHERE user_id = $1`

	_, err := r.db.ExecContext(ctx, query, userID)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).Error("Failed to delete all user settings")
		return fmt.Errorf("failed to delete all user settings: %w", err)
	}

	return nil
}
