package postgres

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"

	"proxyFlow/internal/models"
	"proxyFlow/internal/repository"
)

// UserRepository PostgreSQL用户仓储实现
type UserRepository struct {
	db        *sqlx.DB
	logger    *logrus.Logger
	validator *SecurityValidator
}

// NewUserRepository 创建用户仓储
func NewUserRepository(db *sqlx.DB, logger *logrus.Logger) repository.UserRepository {
	return &UserRepository{
		db:        db,
		logger:    logger,
		validator: NewSecurityValidator(),
	}
}

// Create 创建用户
func (r *UserRepository) Create(ctx context.Context, user *models.User) error {
	var query string
	var err error

	// 确保 IsActive 字段与 Status 字段同步
	user.SetStatus(user.Status)

	if user.ID == "" {
		// 让数据库自动生成 UUID
		query = `
			INSERT INTO users (
				username, email, password_hash, role, is_active,
				created_at, updated_at, last_login
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8
			) RETURNING id`

		err = r.db.QueryRowContext(ctx, query,
			user.Username, user.Email, user.Password, user.Role,
			user.IsActive, user.CreatedAt, user.UpdatedAt, user.LastLogin).Scan(&user.ID)
	} else {
		// 使用提供的 ID
		query = `
			INSERT INTO users (
				id, username, email, password_hash, role, is_active,
				created_at, updated_at, last_login
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9
			)`

		_, err = r.db.ExecContext(ctx, query,
			user.ID, user.Username, user.Email, user.Password, user.Role,
			user.IsActive, user.CreatedAt, user.UpdatedAt, user.LastLogin)
	}

	if err != nil {
		r.logger.WithError(err).WithField("username", user.Username).Error("Failed to create user")
		return fmt.Errorf("failed to create user: %w", err)
	}

	return nil
}

// GetByID 根据ID获取用户
func (r *UserRepository) GetByID(ctx context.Context, id string) (*models.User, error) {
	var user models.User
	var isActive bool

	query := `
		SELECT id, username, email, password_hash, role, is_active, 
			   created_at, updated_at, last_login 
		FROM users WHERE id = $1`

	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&user.ID, &user.Username, &user.Email, &user.Password, &user.Role,
		&isActive, &user.CreatedAt, &user.UpdatedAt, &user.LastLogin)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("user_id", id).Error("Failed to get user by ID")
		return nil, fmt.Errorf("failed to get user by ID: %w", err)
	}

	// 设置 IsActive 字段并转换到用户状态
	user.IsActive = isActive
	user.Status = user.GetStatus()

	return &user, nil
}

// GetByUsername 根据用户名获取用户
func (r *UserRepository) GetByUsername(ctx context.Context, username string) (*models.User, error) {
	var user models.User
	var isActive bool

	query := `
		SELECT id, username, email, password_hash, role, is_active, 
			   created_at, updated_at, last_login 
		FROM users WHERE username = $1`

	err := r.db.QueryRowContext(ctx, query, username).Scan(
		&user.ID, &user.Username, &user.Email, &user.Password, &user.Role,
		&isActive, &user.CreatedAt, &user.UpdatedAt, &user.LastLogin)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("username", username).Error("Failed to get user by username")
		return nil, fmt.Errorf("failed to get user by username: %w", err)
	}

	// 设置 IsActive 字段并转换到用户状态
	user.IsActive = isActive
	user.Status = user.GetStatus()

	return &user, nil
}

// GetByEmail 根据邮箱获取用户
func (r *UserRepository) GetByEmail(ctx context.Context, email string) (*models.User, error) {
	var user models.User
	var isActive bool

	query := `
		SELECT id, username, email, password_hash, role, is_active, 
			   created_at, updated_at, last_login 
		FROM users WHERE email = $1`

	err := r.db.QueryRowContext(ctx, query, email).Scan(
		&user.ID, &user.Username, &user.Email, &user.Password, &user.Role,
		&isActive, &user.CreatedAt, &user.UpdatedAt, &user.LastLogin)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("email", email).Error("Failed to get user by email")
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}

	// 设置 IsActive 字段并转换到用户状态
	user.IsActive = isActive
	user.Status = user.GetStatus()

	return &user, nil
}

// Update 更新用户
func (r *UserRepository) Update(ctx context.Context, user *models.User) error {
	query := `
		UPDATE users SET 
			username = $2, email = $3, password_hash = $4, role = $5, 
			is_active = $6, updated_at = $7, last_login = $8
		WHERE id = $1`

	// 确保 IsActive 字段与 Status 字段同步
	user.SetStatus(user.Status)

	result, err := r.db.ExecContext(ctx, query,
		user.ID, user.Username, user.Email, user.Password, user.Role,
		user.IsActive, time.Now(), user.LastLogin)

	if err != nil {
		r.logger.WithError(err).WithField("user_id", user.ID).Error("Failed to update user")
		return fmt.Errorf("failed to update user: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("user not found")
	}

	return nil
}

// Delete 删除用户
func (r *UserRepository) Delete(ctx context.Context, id string) error {
	query := `DELETE FROM users WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", id).Error("Failed to delete user")
		return fmt.Errorf("failed to delete user: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("user not found")
	}

	return nil
}

// List 获取用户列表
func (r *UserRepository) List(ctx context.Context, filters repository.UserFilters) ([]*models.User, error) {
	query := `
		SELECT id, username, email, password_hash, role, is_active,
			   created_at, updated_at, last_login
		FROM users WHERE 1=1`

	args := []interface{}{}
	argIndex := 1

	// 添加过滤条件
	if filters.Role != nil {
		query += fmt.Sprintf(" AND role = $%d", argIndex)
		args = append(args, *filters.Role)
		argIndex++
	}

	if filters.IsActive != nil {
		query += fmt.Sprintf(" AND is_active = $%d", argIndex)
		args = append(args, *filters.IsActive)
		argIndex++
	}

	if filters.Search != nil && *filters.Search != "" {
		query += fmt.Sprintf(" AND (username ILIKE $%d OR email ILIKE $%d)", argIndex, argIndex+1)
		searchPattern := "%" + *filters.Search + "%"
		args = append(args, searchPattern, searchPattern)
		argIndex += 2
	}

	// 添加排序
	query += " ORDER BY created_at DESC"

	// 添加分页
	if filters.Limit != nil && *filters.Limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, *filters.Limit)
		argIndex++

		if filters.Offset != nil && *filters.Offset > 0 {
			query += fmt.Sprintf(" OFFSET $%d", argIndex)
			args = append(args, *filters.Offset)
		}
	}

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("Failed to list users")
		return nil, fmt.Errorf("failed to list users: %w", err)
	}
	defer rows.Close()

	var users []*models.User
	for rows.Next() {
		var user models.User
		var isActive bool

		err := rows.Scan(
			&user.ID, &user.Username, &user.Email, &user.Password, &user.Role,
			&isActive, &user.CreatedAt, &user.UpdatedAt, &user.LastLogin)

		if err != nil {
			r.logger.WithError(err).Error("Failed to scan user row")
			return nil, fmt.Errorf("failed to scan user row: %w", err)
		}

		// 设置 IsActive 字段并转换到用户状态
		user.IsActive = isActive
		user.Status = user.GetStatus()

		users = append(users, &user)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("rows iteration error: %w", err)
	}

	return users, nil
}

// UpdateLastLogin 更新最后登录时间
func (r *UserRepository) UpdateLastLogin(ctx context.Context, id string, lastLogin time.Time) error {
	query := `UPDATE users SET last_login = $2, updated_at = $3 WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query, id, lastLogin, time.Now())
	if err != nil {
		r.logger.WithError(err).WithField("user_id", id).Error("Failed to update last login")
		return fmt.Errorf("failed to update last login: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("user not found")
	}

	return nil
}
