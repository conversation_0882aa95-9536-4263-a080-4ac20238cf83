package postgres

import (
	"fmt"
	"regexp"
	"strings"
)

// SecurityValidator 安全验证器
type SecurityValidator struct{}

// NewSecurityValidator 创建安全验证器
func NewSecurityValidator() *SecurityValidator {
	return &SecurityValidator{}
}

// ValidateOrderBy 验证排序字段，防止 SQL 注入
func (v *SecurityValidator) ValidateOrderBy(field string, allowedFields []string) (string, error) {
	if field == "" {
		return "", fmt.Errorf("order by field cannot be empty")
	}

	// 检查是否在允许的字段列表中
	for _, allowed := range allowedFields {
		if field == allowed {
			return field, nil
		}
	}

	return "", fmt.Errorf("invalid order by field: %s", field)
}

// ValidateOrderDirection 验证排序方向
func (v *SecurityValidator) ValidateOrderDirection(direction string) (string, error) {
	direction = strings.ToUpper(strings.TrimSpace(direction))
	
	if direction == "" {
		return "DESC", nil // 默认降序
	}

	if direction == "ASC" || direction == "DESC" {
		return direction, nil
	}

	return "", fmt.Errorf("invalid order direction: %s, must be ASC or DESC", direction)
}

// ValidateSearchTerm 验证搜索词，防止恶意输入
func (v *SecurityValidator) ValidateSearchTerm(term string) (string, error) {
	if term == "" {
		return "", nil
	}

	// 限制搜索词长度
	if len(term) > 100 {
		return "", fmt.Errorf("search term too long, maximum 100 characters")
	}

	// 移除潜在的 SQL 注入字符
	// 允许字母、数字、空格、点、连字符、下划线
	validPattern := regexp.MustCompile(`^[a-zA-Z0-9\s\.\-_]+$`)
	if !validPattern.MatchString(term) {
		return "", fmt.Errorf("search term contains invalid characters")
	}

	return strings.TrimSpace(term), nil
}

// ValidateLimit 验证分页限制
func (v *SecurityValidator) ValidateLimit(limit int) (int, error) {
	if limit < 0 {
		return 0, fmt.Errorf("limit cannot be negative")
	}

	if limit == 0 {
		return 50, nil // 默认限制
	}

	if limit > 1000 {
		return 1000, nil // 最大限制
	}

	return limit, nil
}

// ValidateOffset 验证分页偏移
func (v *SecurityValidator) ValidateOffset(offset int) (int, error) {
	if offset < 0 {
		return 0, fmt.Errorf("offset cannot be negative")
	}

	return offset, nil
}

// ValidateTableName 验证表名，防止 SQL 注入
func (v *SecurityValidator) ValidateTableName(tableName string, allowedTables []string) (string, error) {
	if tableName == "" {
		return "", fmt.Errorf("table name cannot be empty")
	}

	// 检查是否在允许的表列表中
	for _, allowed := range allowedTables {
		if tableName == allowed {
			return tableName, nil
		}
	}

	return "", fmt.Errorf("invalid table name: %s", tableName)
}

// ValidateColumnName 验证列名，防止 SQL 注入
func (v *SecurityValidator) ValidateColumnName(columnName string, allowedColumns []string) (string, error) {
	if columnName == "" {
		return "", fmt.Errorf("column name cannot be empty")
	}

	// 检查是否在允许的列列表中
	for _, allowed := range allowedColumns {
		if columnName == allowed {
			return columnName, nil
		}
	}

	return "", fmt.Errorf("invalid column name: %s", columnName)
}

// SanitizeString 清理字符串，移除潜在的恶意字符
func (v *SecurityValidator) SanitizeString(input string) string {
	// 移除 SQL 关键字和特殊字符
	dangerous := []string{
		"'", "\"", ";", "--", "/*", "*/", "xp_", "sp_",
		"SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "CREATE",
		"ALTER", "EXEC", "EXECUTE", "UNION", "SCRIPT",
	}

	result := input
	for _, danger := range dangerous {
		result = strings.ReplaceAll(result, danger, "")
		result = strings.ReplaceAll(result, strings.ToLower(danger), "")
		result = strings.ReplaceAll(result, strings.ToUpper(danger), "")
	}

	return strings.TrimSpace(result)
}

// ValidateUUID 验证 UUID 格式
func (v *SecurityValidator) ValidateUUID(uuid string) (string, error) {
	if uuid == "" {
		return "", fmt.Errorf("UUID cannot be empty")
	}

	// UUID 格式验证
	uuidPattern := regexp.MustCompile(`^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$`)
	if !uuidPattern.MatchString(uuid) {
		return "", fmt.Errorf("invalid UUID format: %s", uuid)
	}

	return uuid, nil
}

// ValidateEmail 验证邮箱格式
func (v *SecurityValidator) ValidateEmail(email string) (string, error) {
	if email == "" {
		return "", fmt.Errorf("email cannot be empty")
	}

	// 简单的邮箱格式验证
	emailPattern := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailPattern.MatchString(email) {
		return "", fmt.Errorf("invalid email format: %s", email)
	}

	return strings.ToLower(strings.TrimSpace(email)), nil
}

// ValidateUsername 验证用户名格式
func (v *SecurityValidator) ValidateUsername(username string) (string, error) {
	if username == "" {
		return "", fmt.Errorf("username cannot be empty")
	}

	// 用户名只能包含字母、数字、下划线和连字符
	usernamePattern := regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)
	if !usernamePattern.MatchString(username) {
		return "", fmt.Errorf("username can only contain letters, numbers, underscores and hyphens")
	}

	if len(username) < 3 || len(username) > 50 {
		return "", fmt.Errorf("username must be between 3 and 50 characters")
	}

	return strings.TrimSpace(username), nil
}

// ValidateHost 验证主机名或 IP 地址
func (v *SecurityValidator) ValidateHost(host string) (string, error) {
	if host == "" {
		return "", fmt.Errorf("host cannot be empty")
	}

	// 简单的主机名/IP 验证
	hostPattern := regexp.MustCompile(`^[a-zA-Z0-9.-]+$`)
	if !hostPattern.MatchString(host) {
		return "", fmt.Errorf("invalid host format: %s", host)
	}

	if len(host) > 255 {
		return "", fmt.Errorf("host name too long")
	}

	return strings.TrimSpace(host), nil
}

// ValidatePort 验证端口号
func (v *SecurityValidator) ValidatePort(port int) (int, error) {
	if port < 1 || port > 65535 {
		return 0, fmt.Errorf("port must be between 1 and 65535")
	}

	return port, nil
}

// GetAllowedProxyOrderByFields 获取代理排序允许的字段
func (v *SecurityValidator) GetAllowedProxyOrderByFields() []string {
	return []string{
		"created_at", "updated_at", "host", "port", "status",
		"use_count", "response_time", "quality_score", 
		"country_code", "city_name", "failures",
	}
}

// GetAllowedUserOrderByFields 获取用户排序允许的字段
func (v *SecurityValidator) GetAllowedUserOrderByFields() []string {
	return []string{
		"created_at", "updated_at", "username", "email", "role", "last_login",
	}
}

// GetAllowedTaskOrderByFields 获取任务排序允许的字段
func (v *SecurityValidator) GetAllowedTaskOrderByFields() []string {
	return []string{
		"created_at", "updated_at", "started_at", "completed_at", 
		"name", "type", "status", "progress",
	}
}
