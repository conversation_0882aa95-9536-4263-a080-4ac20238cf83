package task

import (
	"encoding/json"

	"proxyFlow/internal/models"
)

// TaskConfig 任务配置结构（用于存储在数据库的 config 字段中）
type TaskConfig struct {
	URL           string               `json:"url"`
	Method        string               `json:"method"`
	Headers       map[string]string    `json:"headers,omitempty"`
	Body          string               `json:"body,omitempty"`
	ProxyStrategy models.ProxyStrategy `json:"proxy_strategy"`
	Priority      models.TaskPriority  `json:"priority"`
	MaxRetries    int                  `json:"max_retries"`
	Timeout       int                  `json:"timeout"`
}

// TaskResult 任务结果结构（用于存储在数据库的 result 字段中）
type TaskResult struct {
	StatusCode      int               `json:"status_code"`
	ResponseBody    string            `json:"response_body"`
	ResponseHeaders map[string]string `json:"response_headers,omitempty"`
	ProxyID         string            `json:"proxy_id"`
	Duration        int64             `json:"duration"`
	Error           string            `json:"error,omitempty"`
}

// ConvertLegacyTaskToDBTask 将旧的 Task 模型转换为数据库兼容的 Task 模型
func ConvertLegacyTaskToDBTask(legacyTask *models.LegacyTask) (*models.Task, error) {
	// 构建配置
	config := TaskConfig{
		URL:           legacyTask.URL,
		Method:        legacyTask.Method,
		Headers:       legacyTask.Headers,
		Body:          legacyTask.Body,
		ProxyStrategy: legacyTask.ProxyStrategy,
		Priority:      legacyTask.Priority,
		MaxRetries:    legacyTask.MaxRetries,
		Timeout:       legacyTask.Timeout,
	}

	configJSON, err := json.Marshal(config)
	if err != nil {
		return nil, err
	}

	// 构建结果
	var resultJSON []byte
	if legacyTask.Result != "" || legacyTask.Error != "" {
		result := TaskResult{
			ResponseBody: legacyTask.Result,
			Error:        legacyTask.Error,
		}
		resultJSON, err = json.Marshal(result)
		if err != nil {
			return nil, err
		}
	}

	// 转换状态
	var status models.TaskStatus
	switch legacyTask.Status {
	case models.TaskStatusPending:
		status = "pending"
	case models.TaskStatusRunning:
		status = "running"
	case models.TaskStatusCompleted:
		status = "completed"
	case models.TaskStatusFailed:
		status = "failed"
	case models.TaskStatusCancelled:
		status = "cancelled"
	default:
		status = "pending"
	}

	// 计算进度
	progress := 0
	if status == "completed" {
		progress = 100
	} else if status == "running" {
		progress = 50
	}

	dbTask := &models.Task{
		ID:           legacyTask.ID,
		UserID:       legacyTask.UserID,
		Name:         legacyTask.Name,
		Description:  "", // 旧模型没有描述字段
		Type:         "http_request",
		Status:       status,
		Config:       string(configJSON),
		Result:       string(resultJSON),
		Progress:     progress,
		CreatedAt:    legacyTask.CreatedAt,
		UpdatedAt:    legacyTask.UpdatedAt,
		ErrorMessage: legacyTask.Error,
	}

	// 处理可选的时间字段
	if !legacyTask.StartedAt.IsZero() {
		dbTask.StartedAt = &legacyTask.StartedAt
	}
	if !legacyTask.CompletedAt.IsZero() {
		dbTask.CompletedAt = &legacyTask.CompletedAt
	}

	return dbTask, nil
}

// ConvertDBTaskToLegacyTask 将数据库 Task 模型转换为旧的 Task 模型
func ConvertDBTaskToLegacyTask(dbTask *models.Task) (*models.LegacyTask, error) {
	// 解析配置
	var config TaskConfig
	if dbTask.Config != "" {
		if err := json.Unmarshal([]byte(dbTask.Config), &config); err != nil {
			return nil, err
		}
	}

	// 解析结果
	var result TaskResult
	if dbTask.Result != "" {
		if err := json.Unmarshal([]byte(dbTask.Result), &result); err != nil {
			return nil, err
		}
	}

	// 转换状态
	var status models.TaskStatus
	switch dbTask.Status {
	case "pending":
		status = models.TaskStatusPending
	case "running":
		status = models.TaskStatusRunning
	case "completed":
		status = models.TaskStatusCompleted
	case "failed":
		status = models.TaskStatusFailed
	case "cancelled":
		status = models.TaskStatusCancelled
	default:
		status = models.TaskStatusPending
	}

	legacyTask := &models.LegacyTask{
		ID:            dbTask.ID,
		Name:          dbTask.Name,
		URL:           config.URL,
		Method:        config.Method,
		Headers:       config.Headers,
		Body:          config.Body,
		ProxyID:       result.ProxyID,
		ProxyStrategy: config.ProxyStrategy,
		Status:        status,
		Priority:      config.Priority,
		RetryCount:    0, // 这个信息在新模型中没有直接对应
		MaxRetries:    config.MaxRetries,
		Result:        result.ResponseBody,
		Error:         dbTask.ErrorMessage,
		CreatedAt:     dbTask.CreatedAt,
		UpdatedAt:     dbTask.UpdatedAt,
		Timeout:       config.Timeout,
		UserID:        dbTask.UserID,
	}

	// 处理可选的时间字段
	if dbTask.StartedAt != nil {
		legacyTask.StartedAt = *dbTask.StartedAt
	}
	if dbTask.CompletedAt != nil {
		legacyTask.CompletedAt = *dbTask.CompletedAt
	}

	return legacyTask, nil
}

// CreateTaskConfigFromRequest 从请求创建任务配置
func CreateTaskConfigFromRequest(req *models.TaskRequest) TaskConfig {
	config := TaskConfig{
		URL:           req.URL,
		Method:        req.Method,
		Headers:       req.Headers,
		Body:          req.Body,
		ProxyStrategy: req.ProxyStrategy,
		Priority:      req.Priority,
		MaxRetries:    req.MaxRetries,
		Timeout:       req.Timeout,
	}

	// 设置默认值
	if config.Priority == 0 {
		config.Priority = models.TaskPriorityNormal
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}
	if config.Timeout == 0 {
		config.Timeout = 30
	}
	if config.Method == "" {
		config.Method = "GET"
	}

	return config
}

// CreateTaskResult 创建任务结果
func CreateTaskResult(taskID, proxyID string, statusCode int, responseBody string, responseHeaders map[string]string, duration int64, err error) TaskResult {
	result := TaskResult{
		StatusCode:      statusCode,
		ResponseBody:    responseBody,
		ResponseHeaders: responseHeaders,
		ProxyID:         proxyID,
		Duration:        duration,
	}

	if err != nil {
		result.Error = err.Error()
	}

	return result
}
