package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"proxyFlow/internal/collector"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// CollectorHandler 采集器处理器
type CollectorHandler struct {
	collector collector.Collector
	logger    *logrus.Logger
}

// NewCollectorHandler 创建采集器处理器
func NewCollectorHandler(collector collector.Collector, logger *logrus.Logger) *CollectorHandler {
	return &CollectorHandler{
		collector: collector,
		logger:    logger,
	}
}

// GetStatus 获取采集器状态
// @Summary 获取采集器状态
// @Description 获取代理采集器的运行状态和统计信息
// @Tags collector
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "采集器状态"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/status [get]
func (h *CollectorHandler) GetStatus(c *gin.Context) {
	stats := h.collector.GetStats()

	status := map[string]interface{}{
		"running":             h.collector.IsRunning(),
		"last_collection":     stats.LastCollection,
		"total_sources":       stats.TotalSources,
		"success_sources":     stats.SuccessSources,
		"total_proxies":       stats.TotalProxies,
		"valid_proxies":       stats.ValidProxies,
		"duplicate_count":     stats.DuplicateCount,
		"success_rate":        0.0,
		"collection_duration": stats.Duration,
		"start_time":          stats.StartTime,
		"end_time":            stats.EndTime,
	}

	if stats.TotalProxies > 0 {
		status["success_rate"] = float64(stats.ValidProxies) / float64(stats.TotalProxies) * 100
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    status,
	})
}

// TriggerCollection 手动触发采集
// @Summary 手动触发代理采集
// @Description 立即执行一次代理采集任务
// @Tags collector
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "采集结果"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/collect [post]
func (h *CollectorHandler) TriggerCollection(c *gin.Context) {
	h.logger.Info("Manual collection triggered")

	// 执行采集
	stats, err := h.collector.CollectOnce(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Manual collection failed")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Collection failed: " + err.Error(),
		})
		return
	}

	successRate := 0.0
	if stats.TotalProxies > 0 {
		successRate = float64(stats.ValidProxies) / float64(stats.TotalProxies) * 100
	}

	result := map[string]interface{}{
		"total_sources":   stats.TotalSources,
		"success_sources": stats.SuccessSources,
		"total_proxies":   stats.TotalProxies,
		"valid_proxies":   stats.ValidProxies,
		"duplicate_count": stats.DuplicateCount,
		"success_rate":    successRate,
		"duration":        stats.Duration,
		"start_time":      stats.StartTime,
		"end_time":        stats.EndTime,
	}

	h.logger.WithFields(logrus.Fields{
		"total_proxies": stats.TotalProxies,
		"valid_proxies": stats.ValidProxies,
		"success_rate":  successRate,
		"duration":      stats.Duration,
	}).Info("Manual collection completed")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Collection completed successfully",
		"data":    result,
	})
}

// GetStats 获取采集统计
// @Summary 获取采集统计信息
// @Description 获取详细的采集统计信息
// @Tags collector
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "统计信息"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/stats [get]
func (h *CollectorHandler) GetStats(c *gin.Context) {
	stats := h.collector.GetStats()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// GetHistory 获取采集历史
// @Summary 获取采集历史记录
// @Description 获取最近的采集历史记录
// @Tags collector
// @Accept json
// @Produce json
// @Param limit query int false "限制数量" default(10)
// @Success 200 {object} map[string]interface{} "历史记录"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/history [get]
func (h *CollectorHandler) GetHistory(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid limit parameter",
		})
		return
	}

	if limit > 100 {
		limit = 100 // 最大限制100条记录
	}

	// 这里需要实现历史记录获取逻辑
	// 暂时返回当前统计作为示例
	stats := h.collector.GetStats()
	history := []*collector.CollectionStats{stats}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": map[string]interface{}{
			"history": history,
			"total":   len(history),
		},
	})
}

// StartCollector 启动采集器
// @Summary 启动采集器
// @Description 启动代理采集器服务
// @Tags collector
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "启动结果"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/start [post]
func (h *CollectorHandler) StartCollector(c *gin.Context) {
	if h.collector.IsRunning() {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Collector is already running",
		})
		return
	}

	if err := h.collector.Start(c.Request.Context()); err != nil {
		h.logger.WithError(err).Error("Failed to start collector")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to start collector: " + err.Error(),
		})
		return
	}

	h.logger.Info("Collector started via API")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Collector started successfully",
	})
}

// StopCollector 停止采集器
// @Summary 停止采集器
// @Description 停止代理采集器服务
// @Tags collector
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "停止结果"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/stop [post]
func (h *CollectorHandler) StopCollector(c *gin.Context) {
	if !h.collector.IsRunning() {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Collector is not running",
		})
		return
	}

	if err := h.collector.Stop(); err != nil {
		h.logger.WithError(err).Error("Failed to stop collector")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to stop collector: " + err.Error(),
		})
		return
	}

	h.logger.Info("Collector stopped via API")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Collector stopped successfully",
	})
}

// GetMetrics 获取采集器指标
// @Summary 获取采集器指标
// @Description 获取采集器的详细指标信息
// @Tags collector
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "指标信息"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/metrics [get]
func (h *CollectorHandler) GetMetrics(c *gin.Context) {
	// 这里需要实现指标获取逻辑
	// 暂时返回基本信息
	stats := h.collector.GetStats()

	metrics := map[string]interface{}{
		"is_running":              h.collector.IsRunning(),
		"last_collection":         stats.LastCollection,
		"total_collections":       1, // 这里需要从持久化存储获取
		"avg_success_rate":        0.0,
		"avg_duration":            stats.Duration,
		"total_proxies_collected": stats.TotalProxies,
		"total_valid_proxies":     stats.ValidProxies,
	}

	if stats.TotalProxies > 0 {
		metrics["avg_success_rate"] = float64(stats.ValidProxies) / float64(stats.TotalProxies) * 100
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

// StartTask 启动采集任务
// @Summary 启动新的采集任务
// @Description 创建并启动一个新的代理采集任务
// @Tags collector
// @Accept json
// @Produce json
// @Param config body map[string]interface{} true "任务配置"
// @Success 200 {object} collector.CollectionTask
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/collector/tasks [post]
func (h *CollectorHandler) StartTask(c *gin.Context) {
	var config map[string]interface{}
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request body: " + err.Error(),
		})
		return
	}

	task, err := h.collector.StartTask(c.Request.Context(), config)
	if err != nil {
		h.logger.WithError(err).Error("Failed to start collection task")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to start task: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    task,
	})
}

// GetTask 获取任务信息
// @Summary 获取采集任务信息
// @Description 根据任务ID获取采集任务的详细信息
// @Tags collector
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID"
// @Success 200 {object} collector.CollectionTask
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/collector/tasks/{taskId} [get]
func (h *CollectorHandler) GetTask(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Task ID is required",
		})
		return
	}

	task, err := h.collector.GetTask(taskID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    task,
	})
}

// ListTasks 获取任务列表
// @Summary 获取所有采集任务列表
// @Description 获取所有采集任务的列表信息
// @Tags collector
// @Accept json
// @Produce json
// @Param status query string false "任务状态过滤"
// @Param limit query int false "限制数量" default(50)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {array} collector.CollectionTask
// @Failure 500 {object} gin.H
// @Router /api/v1/collector/tasks [get]
func (h *CollectorHandler) ListTasks(c *gin.Context) {
	tasks := h.collector.ListTasks()

	// 应用过滤器
	statusFilter := c.Query("status")
	if statusFilter != "" {
		filteredTasks := make([]*collector.CollectionTask, 0)
		for _, task := range tasks {
			if task.Status == statusFilter {
				filteredTasks = append(filteredTasks, task)
			}
		}
		tasks = filteredTasks
	}

	// 应用分页
	limit := 50
	offset := 0

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	if offsetStr := c.Query("offset"); offsetStr != "" {
		if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
			offset = o
		}
	}

	// 计算分页
	total := len(tasks)
	start := offset
	end := offset + limit

	if start > total {
		start = total
	}
	if end > total {
		end = total
	}

	paginatedTasks := tasks[start:end]

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"tasks":  paginatedTasks,
			"total":  total,
			"limit":  limit,
			"offset": offset,
		},
	})
}

// PauseTask 暂停任务
// @Summary 暂停采集任务
// @Description 暂停指定的采集任务
// @Tags collector
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/collector/tasks/{taskId}/pause [post]
func (h *CollectorHandler) PauseTask(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Task ID is required",
		})
		return
	}

	if err := h.collector.PauseTask(taskID); err != nil {
		h.logger.WithError(err).WithField("task_id", taskID).Error("Failed to pause task")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Task paused successfully",
	})
}

// ResumeTask 恢复任务
// @Summary 恢复采集任务
// @Description 恢复指定的采集任务
// @Tags collector
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/collector/tasks/{taskId}/resume [post]
func (h *CollectorHandler) ResumeTask(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Task ID is required",
		})
		return
	}

	if err := h.collector.ResumeTask(taskID); err != nil {
		h.logger.WithError(err).WithField("task_id", taskID).Error("Failed to resume task")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Task resumed successfully",
	})
}

// CancelTask 取消任务
// @Summary 取消采集任务
// @Description 取消指定的采集任务
// @Tags collector
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/collector/tasks/{taskId}/cancel [post]
func (h *CollectorHandler) CancelTask(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Task ID is required",
		})
		return
	}

	if err := h.collector.CancelTask(taskID); err != nil {
		h.logger.WithError(err).WithField("task_id", taskID).Error("Failed to cancel task")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Task cancelled successfully",
	})
}

// GetProgress 获取任务进度
// @Summary 获取采集任务进度
// @Description 获取指定任务的实时进度信息
// @Tags collector
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID"
// @Success 200 {object} collector.CollectionProgress
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/collector/tasks/{taskId}/progress [get]
func (h *CollectorHandler) GetProgress(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Task ID is required",
		})
		return
	}

	progress, err := h.collector.GetProgress(taskID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    progress,
	})
}

// StartStandaloneCollection 启动独立采集任务（推荐用于大规模采集）
// @Summary 启动独立的代理采集任务
// @Description 启动一个独立的代理采集任务，适用于大规模代理验证，不影响主服务性能
// @Tags collector
// @Accept json
// @Produce json
// @Param config body map[string]interface{} true "采集配置"
// @Success 200 {object} gin.H "任务启动结果"
// @Failure 400 {object} gin.H "请求错误"
// @Failure 500 {object} gin.H "内部错误"
// @Router /api/v1/collector/standalone [post]
func (h *CollectorHandler) StartStandaloneCollection(c *gin.Context) {
	var config map[string]interface{}
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request body: " + err.Error(),
		})
		return
	}

	// 检查是否需要强制重置
	if force, ok := config["force"].(bool); ok && force {
		h.logger.Info("Force mode requested via API - clearing all caches and progress")
		
		// 类型断言以访问ForceReset方法
		if resetCollector, ok := h.collector.(interface {
			ForceReset() error
		}); ok {
			if err := resetCollector.ForceReset(); err != nil {
				h.logger.WithError(err).Error("Failed to perform force reset")
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"error":   "Failed to perform force reset: " + err.Error(),
				})
				return
			}
			h.logger.Info("Force reset completed via API")
		} else {
			h.logger.Warn("Force reset not supported by current collector implementation")
		}
	}

	// 设置独立任务配置
	config["standalone"] = true
	config["high_performance"] = true
	if config["priority"] == nil {
		config["priority"] = collector.PriorityHigh
	}

	task, err := h.collector.StartTask(c.Request.Context(), config)
	if err != nil {
		h.logger.WithError(err).Error("Failed to start standalone collection task")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to start standalone collection: " + err.Error(),
		})
		return
	}

	// 确保采集器服务已启动
	if !h.collector.IsRunning() {
		if err := h.collector.Start(c.Request.Context()); err != nil {
			h.logger.WithError(err).Error("Failed to start collector service")
			// 尝试取消任务
			h.collector.CancelTask(task.ID)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "Failed to start collector service: " + err.Error(),
			})
			return
		}
	}

	forceMode := false
	if force, ok := config["force"].(bool); ok {
		forceMode = force
	}

	h.logger.WithFields(logrus.Fields{
		"task_id":    task.ID,
		"task_type":  task.Type,
		"priority":   task.Priority,
		"standalone": true,
		"force_mode": forceMode,
	}).Info("Standalone collection task started")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Standalone collection task started successfully",
		"data": gin.H{
			"task_id":    task.ID,
			"task_type":  task.Type,
			"priority":   task.Priority,
			"force_mode": forceMode,
			"created_at": task.CreatedAt,
			"status":     task.Status,
			"progress_endpoint": fmt.Sprintf("/api/v1/collector/tasks/%s/progress", task.ID),
			"cancel_endpoint":   fmt.Sprintf("/api/v1/collector/tasks/%s/cancel", task.ID),
		},
	})
}

// @Summary 获取任务队列状态
// @Description 获取采集器任务队列的状态信息
// @Tags collector
// @Accept json
// @Produce json
// @Success 200 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/collector/queue/status [get]
func (h *CollectorHandler) GetQueueStatus(c *gin.Context) {
	// 类型断言以访问新方法
	if enhancedCollector, ok := h.collector.(interface {
		GetQueueStatus() map[string]interface{}
	}); ok {
		status := enhancedCollector.GetQueueStatus()
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    status,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": map[string]interface{}{
				"queue_size":   0,
				"current_task": nil,
				"message":      "Queue status not available in this collector version",
			},
		})
	}
}
