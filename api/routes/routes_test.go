package routes

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestHandleAPIDocumentation(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	// 设置测试路由
	router.GET("/api/v1/", handleAPIDocumentation)
	router.GET("/api/v1/docs", handleAPIDocumentation)

	tests := []struct {
		name           string
		path           string
		acceptHeader   string
		expectedStatus int
		expectedType   string
	}{
		{
			name:           "API概览 - HTML格式",
			path:           "/api/v1/",
			acceptHeader:   "text/html",
			expectedStatus: http.StatusOK,
			expectedType:   "text/html",
		},
		{
			name:           "API概览 - JSON格式",
			path:           "/api/v1/",
			acceptHeader:   "application/json",
			expectedStatus: http.StatusOK,
			expectedType:   "application/json",
		},
		{
			name:           "详细文档 - HTML格式",
			path:           "/api/v1/docs",
			acceptHeader:   "text/html",
			expectedStatus: http.StatusOK,
			expectedType:   "text/html",
		},
		{
			name:           "详细文档 - JSON格式",
			path:           "/api/v1/docs",
			acceptHeader:   "application/json",
			expectedStatus: http.StatusOK,
			expectedType:   "application/json",
		},
		{
			name:           "默认HTML格式",
			path:           "/api/v1/",
			acceptHeader:   "",
			expectedStatus: http.StatusOK,
			expectedType:   "text/html",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", tt.path, nil)
			if tt.acceptHeader != "" {
				req.Header.Set("Accept", tt.acceptHeader)
			}
			
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			
			contentType := w.Header().Get("Content-Type")
			if tt.expectedType == "application/json" {
				assert.Contains(t, contentType, "application/json")
			} else {
				assert.Contains(t, contentType, "text/html")
			}
			
			// 检查响应内容不为空
			assert.NotEmpty(t, w.Body.String())
			
			// 检查响应内容包含预期的关键字
			body := w.Body.String()
			assert.Contains(t, body, "Proxy Manager API")
			
			if tt.acceptHeader == "application/json" {
				assert.Contains(t, body, "endpoints")
			} else {
				assert.Contains(t, body, "<!DOCTYPE html>")
			}
		})
	}
}

func TestGetAPIOverviewJSON(t *testing.T) {
	result := getAPIOverviewJSON()
	
	// 检查基本字段
	assert.Equal(t, "Proxy Manager API", result["name"])
	assert.Equal(t, "v1", result["version"])
	assert.Equal(t, "/api/v1", result["base_url"])
	
	// 检查endpoints字段存在
	endpoints, exists := result["endpoints"]
	assert.True(t, exists)
	assert.NotNil(t, endpoints)
	
	// 检查documentation字段存在
	documentation, exists := result["documentation"]
	assert.True(t, exists)
	assert.NotNil(t, documentation)
}

func TestGetDetailedAPIDocsJSON(t *testing.T) {
	result := getDetailedAPIDocsJSON()
	
	// 检查基本字段
	assert.Equal(t, "Proxy Manager API", result["name"])
	assert.Equal(t, "v1", result["version"])
	
	// 检查auth字段存在
	auth, exists := result["auth"]
	assert.True(t, exists)
	assert.NotNil(t, auth)
	
	// 检查status_codes字段存在
	statusCodes, exists := result["status_codes"]
	assert.True(t, exists)
	assert.NotNil(t, statusCodes)
	
	// 检查endpoints字段存在
	endpoints, exists := result["endpoints"]
	assert.True(t, exists)
	assert.NotNil(t, endpoints)
}

func TestGetAPIOverviewHTML(t *testing.T) {
	result := getAPIOverviewHTML()
	
	// 检查HTML结构
	assert.Contains(t, result, "<!DOCTYPE html>")
	assert.Contains(t, result, "<html lang=\"zh-CN\">")
	assert.Contains(t, result, "</html>")
	
	// 检查关键内容
	assert.Contains(t, result, "Proxy Manager API v1")
	assert.Contains(t, result, "认证接口")
	assert.Contains(t, result, "代理管理")
	assert.Contains(t, result, "标签管理")
	assert.Contains(t, result, "任务管理")
	assert.Contains(t, result, "采集器管理")
}

func TestGetDetailedAPIDocsHTML(t *testing.T) {
	result := getDetailedAPIDocsHTML()
	
	// 检查HTML结构
	assert.Contains(t, result, "<!DOCTYPE html>")
	assert.Contains(t, result, "<html lang=\"zh-CN\">")
	assert.Contains(t, result, "</html>")
	
	// 检查关键内容
	assert.Contains(t, result, "详细接口文档")
	assert.Contains(t, result, "认证说明")
	assert.Contains(t, result, "HTTP状态码")
	assert.Contains(t, result, "/auth/register")
	assert.Contains(t, result, "/auth/login")
	assert.Contains(t, result, "/auth/profile")
	assert.Contains(t, result, "请求参数")
	assert.Contains(t, result, "响应示例")
	assert.Contains(t, result, "curl")
}
