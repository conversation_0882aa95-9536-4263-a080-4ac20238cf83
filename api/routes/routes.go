package routes

import (
	"net/http"
	"proxyFlow/api/handlers"
	"proxyFlow/internal/middleware"
	"proxyFlow/internal/models"
	"proxyFlow/internal/proxy"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/sirupsen/logrus"
)

// SetupRoutes 设置路由
func SetupRoutes(
	router *gin.Engine,
	authHandler *handlers.AuthHandler,
	proxyHandler *handlers.ProxyHandler,
	taskHandler *handlers.TaskHandler,
	apiKeyHandler *handlers.APIKeyHandler,
	settingsHandler *handlers.SettingsHandler,
	collectorHandler *handlers.CollectorHandler,
	authMiddleware *middleware.AuthMiddleware,
	proxyManager *proxy.Manager,
	logger *logrus.Logger,
) {
	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// Prometheus指标
	router.GET("/metrics", gin.WrapH(promhttp.Handler()))

	// 创建标签处理器
	proxyTagHandler := handlers.NewProxyTagHandler(proxyManager, logger)

	// API v1
	v1 := router.Group("/api/v1")
	v1.GET("/", handleAPIDocumentation)
	v1.GET("/docs", handleAPIDocumentation)
	{
		// 认证路由
		auth := v1.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.GET("/profile", authMiddleware.Authenticate(), authHandler.GetProfile)
		}

		// 用户管理路由
		user := v1.Group("/user")
		{
			user.Use(authMiddleware.Authenticate())

			// 新版本多API Key 管理
			apikeys := user.Group("/apikeys")
			{
				apikeys.GET("", apiKeyHandler.GetAPIKeys)          // 获取API密钥列表
				apikeys.POST("", apiKeyHandler.CreateAPIKey)       // 创建新的API密钥
				apikeys.PUT("/:id", apiKeyHandler.UpdateAPIKey)    // 更新API密钥
				apikeys.DELETE("/:id", apiKeyHandler.DeleteAPIKey) // 删除API密钥
			}

			// 用户设置管理
			user.GET("/settings", settingsHandler.GetUserSettings)      // 获取用户设置
			user.PUT("/settings", settingsHandler.UpdateUserSettings)   // 更新用户设置
			user.POST("/settings/reset", settingsHandler.ResetSettings) // 重置设置

			// 代理设置
			user.GET("/proxy-settings", settingsHandler.GetProxySettings)    // 获取代理设置
			user.PUT("/proxy-settings", settingsHandler.UpdateProxySettings) // 更新代理设置
		}

		// 代理路由
		proxies := v1.Group("/proxies")
		{
			// 地理位置相关路由 - 放在前面避免与/:id冲突
			proxies.GET("/location/stats", authMiddleware.Authenticate(), proxyHandler.GetProxyLocationStats)
			proxies.GET("/location/groups", authMiddleware.Authenticate(), proxyHandler.GroupProxiesByLocation)
			proxies.GET("/location", authMiddleware.Authenticate(), proxyHandler.GetProxiesByLocation)
			// 质量评估相关路由
			proxies.GET("/quality/top", authMiddleware.Authenticate(), proxyHandler.GetTopQualityProxies)
			proxies.GET("/quality", authMiddleware.Authenticate(), proxyHandler.GetProxiesByQuality)
			proxies.POST("/quality/assess", authMiddleware.Authenticate(), authMiddleware.RequireRole(models.UserRoleAdmin), proxyHandler.BatchAssessQuality)
			// 标签和智能路由相关路由
			proxies.GET("/tags", authMiddleware.Authenticate(), proxyHandler.GetProxiesByTags)
			proxies.GET("/scenario", authMiddleware.Authenticate(), proxyHandler.GetProxiesByScenario)
			proxies.GET("/smart-routing", authMiddleware.Authenticate(), proxyHandler.GetProxyWithSmartRouting)
			// 其他具体路径
			proxies.GET("/available", authMiddleware.Authenticate(), proxyHandler.GetAvailableProxies)
			proxies.GET("/stats", authMiddleware.Authenticate(), proxyHandler.GetProxyStats)
			proxies.GET("/strategy", authMiddleware.Authenticate(), proxyHandler.GetProxyByStrategy)
			// 基本CRUD操作
			proxies.GET("", authMiddleware.Authenticate(), proxyHandler.GetProxies)
			proxies.POST("", authMiddleware.Authenticate(), authMiddleware.RequireRole(models.UserRoleAdmin), proxyHandler.AddProxy)
			// 批量操作
			proxies.POST("/batch/health-check", authMiddleware.Authenticate(), proxyHandler.BatchHealthCheck)
			proxies.POST("/health-check-all", authMiddleware.Authenticate(), proxyHandler.HealthCheckAll)
			// super admin 批量导入代理
			proxies.POST("/batch-import", authMiddleware.Authenticate(), authMiddleware.RequireRole(models.UserRoleAdmin), proxyHandler.BatchImportProxies)
			// 带ID的路由放在最后
			proxies.GET("/:id", authMiddleware.Authenticate(), proxyHandler.GetProxy)
			proxies.DELETE("/:id", authMiddleware.Authenticate(), authMiddleware.RequireRole(models.UserRoleAdmin), proxyHandler.DeleteProxy)
			proxies.POST("/:id/health-check", authMiddleware.Authenticate(), proxyHandler.HealthCheck)
			proxies.POST("/:id/quality/assess", authMiddleware.Authenticate(), authMiddleware.RequireRole(models.UserRoleAdmin), proxyHandler.AssessProxyQuality)
			// 代理标签管理
			// proxies.GET("/:proxy_id/tags", authMiddleware.Authenticate(), proxyTagHandler.GetProxyTags)
			// proxies.DELETE("/:proxy_id/tags", authMiddleware.Authenticate(), authMiddleware.RequireRole(models.UserRoleAdmin), proxyTagHandler.RemoveTags)
		}

		// 标签管理路由
		tags := v1.Group("/tags")
		{
			tags.Use(authMiddleware.Authenticate())
			tags.GET("", proxyTagHandler.GetTags)
			tags.POST("", authMiddleware.RequireRole(models.UserRoleAdmin), proxyTagHandler.CreateTag)
			tags.GET("/:id", proxyTagHandler.GetTag)
			tags.PUT("/:id", authMiddleware.RequireRole(models.UserRoleAdmin), proxyTagHandler.UpdateTag)
			tags.DELETE("/:id", authMiddleware.RequireRole(models.UserRoleAdmin), proxyTagHandler.DeleteTag)
			tags.POST("/assign", authMiddleware.RequireRole(models.UserRoleAdmin), proxyTagHandler.AssignTags)
		}

		// 任务路由
		tasks := v1.Group("/tasks")
		{
			tasks.Use(authMiddleware.Authenticate())
			tasks.GET("", taskHandler.GetUserTasks)
			tasks.GET("/stats", taskHandler.GetTaskStats)
			tasks.POST("", taskHandler.CreateTask)
			tasks.GET("/:id", taskHandler.GetTask)
			tasks.PUT("/:id", taskHandler.UpdateTask)
			tasks.DELETE("/:id", taskHandler.DeleteTask)
			tasks.PATCH("/:id/cancel", taskHandler.CancelTask)
			// 批量操作
			tasks.POST("/batch-delete", taskHandler.BatchDeleteTasks)
			tasks.POST("/batch-cancel", taskHandler.BatchCancelTasks)
		}

		// 采集器路由
		collector := v1.Group("/collector")
		{
			collector.Use(authMiddleware.Authenticate())
			collector.GET("/status", collectorHandler.GetStatus)
			collector.GET("/stats", collectorHandler.GetStats)
			collector.GET("/metrics", collectorHandler.GetMetrics)
			collector.GET("/history", collectorHandler.GetHistory)

			// 任务管理路由
			tasks := collector.Group("/tasks")
			{
				tasks.GET("", collectorHandler.ListTasks)
				tasks.POST("", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.StartTask)
				tasks.GET("/:taskId", collectorHandler.GetTask)
				tasks.POST("/:taskId/pause", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.PauseTask)
				tasks.POST("/:taskId/resume", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.ResumeTask)
				tasks.POST("/:taskId/cancel", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.CancelTask)
				tasks.GET("/:taskId/progress", collectorHandler.GetProgress)
			}

			// 队列管理路由
			queue := collector.Group("/queue")
			{
				queue.GET("/status", collectorHandler.GetQueueStatus)
			}

			// 管理员操作
			collector.POST("/collect", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.TriggerCollection)
			collector.POST("/start", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.StartCollector)
			collector.POST("/stop", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.StopCollector)
			// 独立采集任务（推荐用于大规模采集）
			collector.POST("/standalone", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.StartStandaloneCollection)
		}
	}

	// 静态文件服务 - 为前端提供服务
	router.Static("/static", "./web/build")
	router.StaticFile("/favicon.ico", "./web/build/favicon.ico")
	router.StaticFile("/manifest.json", "./web/build/manifest.json")

	// SPA路由处理 - 所有未匹配的路由都返回index.html
	router.NoRoute(func(c *gin.Context) {
		// 如果是API请求，返回404
		if len(c.Request.URL.Path) >= 4 && c.Request.URL.Path[:4] == "/api" {
			c.JSON(http.StatusNotFound, gin.H{"error": "API endpoint not found"})
			return
		}
		// js css 等资源正常不用管，Static 已经处理了
		_p := c.Request.URL.Path
		if strings.Contains(_p, "js") || strings.Contains(_p, "css") || strings.Contains(_p, "ico") || strings.Contains(_p, "json") {
			return
		}
		// 否则返回前端应用
		c.File("./web/build/index.html")
	})
}

// handleAPIDocumentation 处理API文档请求
func handleAPIDocumentation(c *gin.Context) {
	// 检查Accept头部，决定返回格式
	acceptHeader := c.GetHeader("Accept")
	isJSONRequest := strings.Contains(acceptHeader, "application/json")

	// 根据路径决定返回概览还是详细文档
	isDetailedDocs := strings.HasSuffix(c.Request.URL.Path, "/docs")

	if isJSONRequest {
		// 返回JSON格式的API文档
		if isDetailedDocs {
			c.JSON(http.StatusOK, getDetailedAPIDocsJSON())
		} else {
			c.JSON(http.StatusOK, getAPIOverviewJSON())
		}
	} else {
		// 返回HTML格式的API文档
		if isDetailedDocs {
			c.Header("Content-Type", "text/html; charset=utf-8")
			c.String(http.StatusOK, getDetailedAPIDocsHTML())
		} else {
			c.Header("Content-Type", "text/html; charset=utf-8")
			c.String(http.StatusOK, getAPIOverviewHTML())
		}
	}
}

// getAPIOverviewJSON 返回API概览的JSON格式
func getAPIOverviewJSON() gin.H {
	return gin.H{
		"name":        "Proxy Manager API",
		"version":     "v1",
		"description": "高性能代理管理系统API",
		"base_url":    "/api/v1",
		"endpoints": gin.H{
			"auth": gin.H{
				"description": "用户认证相关接口",
				"endpoints": []gin.H{
					{"method": "POST", "path": "/auth/register", "description": "用户注册"},
					{"method": "POST", "path": "/auth/login", "description": "用户登录"},
					{"method": "GET", "path": "/auth/profile", "description": "获取用户信息", "auth": true},
				},
			},
			"user": gin.H{
				"description": "用户管理相关接口",
				"endpoints": []gin.H{
					{"method": "GET", "path": "/user/apikeys", "description": "获取API密钥列表", "auth": true},
					{"method": "POST", "path": "/user/apikeys", "description": "创建新的API密钥", "auth": true},
					{"method": "PUT", "path": "/user/apikeys/:id", "description": "更新API密钥", "auth": true},
					{"method": "DELETE", "path": "/user/apikeys/:id", "description": "删除API密钥", "auth": true},
					{"method": "GET", "path": "/user/settings", "description": "获取用户设置", "auth": true},
					{"method": "PUT", "path": "/user/settings", "description": "更新用户设置", "auth": true},
					{"method": "POST", "path": "/user/settings/reset", "description": "重置设置", "auth": true},
					{"method": "GET", "path": "/user/proxy-settings", "description": "获取代理设置", "auth": true},
					{"method": "PUT", "path": "/user/proxy-settings", "description": "更新代理设置", "auth": true},
				},
			},
			"proxies": gin.H{
				"description": "代理管理相关接口",
				"endpoints": []gin.H{
					{"method": "GET", "path": "/proxies", "description": "获取代理列表", "auth": true},
					{"method": "POST", "path": "/proxies", "description": "添加代理", "auth": true, "role": "admin"},
					{"method": "GET", "path": "/proxies/:id", "description": "获取单个代理", "auth": true},
					{"method": "DELETE", "path": "/proxies/:id", "description": "删除代理", "auth": true, "role": "admin"},
					{"method": "GET", "path": "/proxies/available", "description": "获取可用代理", "auth": true},
					{"method": "GET", "path": "/proxies/stats", "description": "获取代理统计", "auth": true},
					{"method": "POST", "path": "/proxies/batch/health-check", "description": "批量健康检查", "auth": true},
					{"method": "POST", "path": "/proxies/health-check-all", "description": "检查所有代理", "auth": true},
					{"method": "POST", "path": "/proxies/batch-import", "description": "批量导入代理", "auth": true, "role": "admin"},
				},
			},
			"tags": gin.H{
				"description": "标签管理相关接口",
				"endpoints": []gin.H{
					{"method": "GET", "path": "/tags", "description": "获取标签列表", "auth": true},
					{"method": "POST", "path": "/tags", "description": "创建标签", "auth": true, "role": "admin"},
					{"method": "GET", "path": "/tags/:id", "description": "获取单个标签", "auth": true},
					{"method": "PUT", "path": "/tags/:id", "description": "更新标签", "auth": true, "role": "admin"},
					{"method": "DELETE", "path": "/tags/:id", "description": "删除标签", "auth": true, "role": "admin"},
					{"method": "POST", "path": "/tags/assign", "description": "分配标签", "auth": true, "role": "admin"},
				},
			},
			"tasks": gin.H{
				"description": "任务管理相关接口",
				"endpoints": []gin.H{
					{"method": "GET", "path": "/tasks", "description": "获取用户任务", "auth": true},
					{"method": "GET", "path": "/tasks/stats", "description": "获取任务统计", "auth": true},
					{"method": "POST", "path": "/tasks", "description": "创建任务", "auth": true},
					{"method": "GET", "path": "/tasks/:id", "description": "获取单个任务", "auth": true},
					{"method": "PUT", "path": "/tasks/:id", "description": "更新任务", "auth": true},
					{"method": "DELETE", "path": "/tasks/:id", "description": "删除任务", "auth": true},
					{"method": "PATCH", "path": "/tasks/:id/cancel", "description": "取消任务", "auth": true},
				},
			},
			"collector": gin.H{
				"description": "采集器相关接口",
				"endpoints": []gin.H{
					{"method": "GET", "path": "/collector/status", "description": "获取采集器状态", "auth": true},
					{"method": "GET", "path": "/collector/stats", "description": "获取采集器统计", "auth": true},
					{"method": "GET", "path": "/collector/metrics", "description": "获取采集器指标", "auth": true},
					{"method": "GET", "path": "/collector/history", "description": "获取采集历史", "auth": true},
					{"method": "POST", "path": "/collector/collect", "description": "触发采集", "auth": true, "role": "admin"},
					{"method": "POST", "path": "/collector/start", "description": "启动采集器", "auth": true, "role": "admin"},
					{"method": "POST", "path": "/collector/stop", "description": "停止采集器", "auth": true, "role": "admin"},
				},
			},
		},
		"documentation": gin.H{
			"detailed_docs": "/api/v1/docs",
			"json_format":   "添加 Accept: application/json 头部获取JSON格式",
			"html_format":   "默认返回HTML格式，便于浏览器查看",
		},
	}
}

// getDetailedAPIDocsJSON 返回详细API文档的JSON格式
func getDetailedAPIDocsJSON() gin.H {
	return gin.H{
		"name":        "Proxy Manager API",
		"version":     "v1",
		"description": "高性能代理管理系统API - 详细文档",
		"base_url":    "/api/v1",
		"auth": gin.H{
			"description": "认证方式",
			"methods": []gin.H{
				{
					"type":        "Bearer Token",
					"header":      "Authorization: Bearer <token>",
					"description": "通过登录接口获取token，然后在请求头中携带",
				},
			},
		},
		"status_codes": gin.H{
			"200": "请求成功",
			"201": "创建成功",
			"400": "请求参数错误",
			"401": "未授权，需要登录",
			"403": "权限不足",
			"404": "资源不存在",
			"500": "服务器内部错误",
		},
		"endpoints": gin.H{
			"auth": []gin.H{
				{
					"method":      "POST",
					"path":        "/auth/register",
					"description": "用户注册",
					"parameters": gin.H{
						"body": gin.H{
							"username": gin.H{"type": "string", "required": true, "description": "用户名"},
							"email":    gin.H{"type": "string", "required": true, "description": "邮箱地址"},
							"password": gin.H{"type": "string", "required": true, "description": "密码"},
						},
					},
					"responses": gin.H{
						"201": gin.H{"description": "注册成功", "example": gin.H{"message": "User registered successfully"}},
						"400": gin.H{"description": "参数错误", "example": gin.H{"error": "Invalid input"}},
					},
					"example": gin.H{
						"curl": `curl -X POST /api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}'`,
					},
				},
				{
					"method":      "POST",
					"path":        "/auth/login",
					"description": "用户登录",
					"parameters": gin.H{
						"body": gin.H{
							"username": gin.H{"type": "string", "required": true, "description": "用户名或邮箱"},
							"password": gin.H{"type": "string", "required": true, "description": "密码"},
						},
					},
					"responses": gin.H{
						"200": gin.H{"description": "登录成功", "example": gin.H{"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}},
						"401": gin.H{"description": "认证失败", "example": gin.H{"error": "Invalid credentials"}},
					},
					"example": gin.H{
						"curl": `curl -X POST /api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"password123"}'`,
					},
				},
				{
					"method":      "GET",
					"path":        "/auth/profile",
					"description": "获取用户信息",
					"auth":        true,
					"responses": gin.H{
						"200": gin.H{"description": "获取成功", "example": gin.H{"id": 1, "username": "testuser", "email": "<EMAIL>"}},
						"401": gin.H{"description": "未授权", "example": gin.H{"error": "Unauthorized"}},
					},
					"example": gin.H{
						"curl": `curl -X GET /api/v1/auth/profile \
  -H "Authorization: Bearer <your-token>"`,
					},
				},
			},
		},
	}
}

// getAPIOverviewHTML 返回API概览的HTML格式
func getAPIOverviewHTML() string {
	return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proxy Manager API v1 - 概览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .section {
            background: white;
            margin: 20px 0;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .endpoint-group {
            margin: 20px 0;
        }
        .endpoint-group h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        .endpoint {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        .method {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            margin-right: 10px;
            min-width: 60px;
            text-align: center;
            font-size: 0.8em;
        }
        .method.GET { background: #28a745; color: white; }
        .method.POST { background: #007bff; color: white; }
        .method.PUT { background: #ffc107; color: black; }
        .method.DELETE { background: #dc3545; color: white; }
        .method.PATCH { background: #6f42c1; color: white; }
        .path {
            font-family: 'Monaco', 'Menlo', monospace;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            margin-right: 10px;
            min-width: 200px;
        }
        .description {
            flex: 1;
            color: #6c757d;
        }
        .auth-badge {
            background: #fd7e14;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.7em;
            margin-left: 10px;
        }
        .admin-badge {
            background: #dc3545;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.7em;
            margin-left: 5px;
        }
        .nav-links {
            text-align: center;
            margin: 30px 0;
        }
        .nav-links a {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: #5a6fd8;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .info-box h4 {
            color: #1976d2;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Proxy Manager API v1</h1>
        <p>高性能代理管理系统API接口概览</p>
    </div>

    <div class="nav-links">
        <a href="/api/v1/">API 概览</a>
        <a href="/api/v1/docs">详细文档</a>
        <a href="/api/v1/docs" onclick="this.href+='?format=json'">JSON 格式</a>
    </div>

    <div class="section">
        <h2>📋 接口概览</h2>
        <div class="info-box">
            <h4>🔗 基础URL</h4>
            <code>/api/v1</code>
        </div>
        <div class="info-box">
            <h4>🔐 认证方式</h4>
            <p>大部分接口需要在请求头中携带 <code>Authorization: Bearer &lt;token&gt;</code></p>
            <p>通过 <code>POST /api/v1/auth/login</code> 获取token</p>
        </div>
    </div>

    <div class="section">
        <h2>🔐 认证接口</h2>
        <div class="endpoint-group">
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/auth/register</span>
                <span class="description">用户注册</span>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/auth/login</span>
                <span class="description">用户登录</span>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/auth/profile</span>
                <span class="description">获取用户信息</span>
                <span class="auth-badge">需要认证</span>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>👤 用户管理</h2>
        <div class="endpoint-group">
            <h3>API密钥管理</h3>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/user/apikeys</span>
                <span class="description">获取API密钥列表</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/user/apikeys</span>
                <span class="description">创建新的API密钥</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method PUT">PUT</span>
                <span class="path">/user/apikeys/:id</span>
                <span class="description">更新API密钥</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method DELETE">DELETE</span>
                <span class="path">/user/apikeys/:id</span>
                <span class="description">删除API密钥</span>
                <span class="auth-badge">需要认证</span>
            </div>
        </div>
        <div class="endpoint-group">
            <h3>用户设置</h3>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/user/settings</span>
                <span class="description">获取用户设置</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method PUT">PUT</span>
                <span class="path">/user/settings</span>
                <span class="description">更新用户设置</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/user/settings/reset</span>
                <span class="description">重置设置</span>
                <span class="auth-badge">需要认证</span>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🌐 代理管理</h2>
        <div class="endpoint-group">
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/proxies</span>
                <span class="description">获取代理列表</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/proxies</span>
                <span class="description">添加代理</span>
                <span class="auth-badge">需要认证</span>
                <span class="admin-badge">管理员</span>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/proxies/:id</span>
                <span class="description">获取单个代理</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method DELETE">DELETE</span>
                <span class="path">/proxies/:id</span>
                <span class="description">删除代理</span>
                <span class="auth-badge">需要认证</span>
                <span class="admin-badge">管理员</span>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/proxies/available</span>
                <span class="description">获取可用代理</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/proxies/stats</span>
                <span class="description">获取代理统计</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/proxies/batch/health-check</span>
                <span class="description">批量健康检查</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/proxies/batch-import</span>
                <span class="description">批量导入代理</span>
                <span class="auth-badge">需要认证</span>
                <span class="admin-badge">管理员</span>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🏷️ 标签管理</h2>
        <div class="endpoint-group">
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/tags</span>
                <span class="description">获取标签列表</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/tags</span>
                <span class="description">创建标签</span>
                <span class="auth-badge">需要认证</span>
                <span class="admin-badge">管理员</span>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/tags/:id</span>
                <span class="description">获取单个标签</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method PUT">PUT</span>
                <span class="path">/tags/:id</span>
                <span class="description">更新标签</span>
                <span class="auth-badge">需要认证</span>
                <span class="admin-badge">管理员</span>
            </div>
            <div class="endpoint">
                <span class="method DELETE">DELETE</span>
                <span class="path">/tags/:id</span>
                <span class="description">删除标签</span>
                <span class="auth-badge">需要认证</span>
                <span class="admin-badge">管理员</span>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>📋 任务管理</h2>
        <div class="endpoint-group">
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/tasks</span>
                <span class="description">获取用户任务</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/tasks</span>
                <span class="description">创建任务</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/tasks/:id</span>
                <span class="description">获取单个任务</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method PUT">PUT</span>
                <span class="path">/tasks/:id</span>
                <span class="description">更新任务</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method DELETE">DELETE</span>
                <span class="path">/tasks/:id</span>
                <span class="description">删除任务</span>
                <span class="auth-badge">需要认证</span>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🔄 采集器管理</h2>
        <div class="endpoint-group">
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/collector/status</span>
                <span class="description">获取采集器状态</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method GET">GET</span>
                <span class="path">/collector/stats</span>
                <span class="description">获取采集器统计</span>
                <span class="auth-badge">需要认证</span>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/collector/collect</span>
                <span class="description">触发采集</span>
                <span class="auth-badge">需要认证</span>
                <span class="admin-badge">管理员</span>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/collector/start</span>
                <span class="description">启动采集器</span>
                <span class="auth-badge">需要认证</span>
                <span class="admin-badge">管理员</span>
            </div>
            <div class="endpoint">
                <span class="method POST">POST</span>
                <span class="path">/collector/stop</span>
                <span class="description">停止采集器</span>
                <span class="auth-badge">需要认证</span>
                <span class="admin-badge">管理员</span>
            </div>
        </div>
    </div>

    <div class="nav-links">
        <a href="/api/v1/docs">查看详细文档</a>
        <a href="/">返回首页</a>
    </div>

    <footer style="text-align: center; margin-top: 50px; color: #6c757d;">
        <p>© 2024 Proxy Manager API v1 | <a href="/api/v1/docs" style="color: #667eea;">详细文档</a></p>
    </footer>
</body>
</html>`
}

// getDetailedAPIDocsHTML 返回详细API文档的HTML格式
func getDetailedAPIDocsHTML() string {
	return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proxy Manager API v1 - 详细文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .section {
            background: white;
            margin: 20px 0;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .endpoint {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin: 20px 0;
            overflow: hidden;
        }
        .endpoint-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        .endpoint-method {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            margin-right: 10px;
            font-size: 0.9em;
        }
        .endpoint-method.POST { background: #007bff; color: white; }
        .endpoint-method.GET { background: #28a745; color: white; }
        .endpoint-path {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 1.1em;
            font-weight: bold;
        }
        .endpoint-body {
            padding: 20px;
        }
        .params-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .params-table th,
        .params-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        .params-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }
        .response-example {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
        }
        .error-example {
            background: #f8e8e8;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
        }
        .nav-links {
            text-align: center;
            margin: 30px 0;
        }
        .nav-links a {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: #5a6fd8;
        }
        .status-codes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-code {
            padding: 10px;
            border-radius: 6px;
            text-align: center;
        }
        .status-200 { background: #d4edda; border: 1px solid #c3e6cb; }
        .status-400 { background: #f8d7da; border: 1px solid #f5c6cb; }
        .status-401 { background: #fff3cd; border: 1px solid #ffeaa7; }
        .status-500 { background: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📚 Proxy Manager API v1</h1>
        <p>详细接口文档</p>
    </div>

    <div class="nav-links">
        <a href="/api/v1/">返回概览</a>
        <a href="/api/v1/docs">详细文档</a>
        <a href="#" onclick="window.location.href='/api/v1/docs?format=json'">JSON 格式</a>
    </div>

    <div class="section">
        <h2>🔐 认证说明</h2>
        <p>大部分API接口需要认证。认证方式：</p>
        <div class="code-block">Authorization: Bearer &lt;your-jwt-token&gt;</div>
        <p>通过登录接口获取JWT token，然后在后续请求的Header中携带。</p>
    </div>

    <div class="section">
        <h2>📊 HTTP状态码</h2>
        <div class="status-codes">
            <div class="status-code status-200"><strong>200</strong><br>请求成功</div>
            <div class="status-code status-200"><strong>201</strong><br>创建成功</div>
            <div class="status-code status-400"><strong>400</strong><br>请求参数错误</div>
            <div class="status-code status-401"><strong>401</strong><br>未授权，需要登录</div>
            <div class="status-code status-400"><strong>403</strong><br>权限不足</div>
            <div class="status-code status-400"><strong>404</strong><br>资源不存在</div>
            <div class="status-code status-500"><strong>500</strong><br>服务器内部错误</div>
        </div>
    </div>

    <div class="section">
        <h2>🔐 认证接口</h2>

        <div class="endpoint">
            <div class="endpoint-header">
                <span class="endpoint-method POST">POST</span>
                <span class="endpoint-path">/api/v1/auth/register</span>
                <span style="margin-left: 20px; color: #6c757d;">用户注册</span>
            </div>
            <div class="endpoint-body">
                <h4>请求参数</h4>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必填</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>username</td>
                            <td>string</td>
                            <td>是</td>
                            <td>用户名</td>
                        </tr>
                        <tr>
                            <td>email</td>
                            <td>string</td>
                            <td>是</td>
                            <td>邮箱地址</td>
                        </tr>
                        <tr>
                            <td>password</td>
                            <td>string</td>
                            <td>是</td>
                            <td>密码</td>
                        </tr>
                    </tbody>
                </table>

                <h4>请求示例</h4>
                <div class="code-block">curl -X POST /api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123"
  }'</div>

                <h4>响应示例</h4>
                <div class="response-example">
                    <strong>201 Created</strong>
                    <div class="code-block">{"message": "User registered successfully"}</div>
                </div>
                <div class="error-example">
                    <strong>400 Bad Request</strong>
                    <div class="code-block">{"error": "Invalid input"}</div>
                </div>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <span class="endpoint-method POST">POST</span>
                <span class="endpoint-path">/api/v1/auth/login</span>
                <span style="margin-left: 20px; color: #6c757d;">用户登录</span>
            </div>
            <div class="endpoint-body">
                <h4>请求参数</h4>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必填</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>username</td>
                            <td>string</td>
                            <td>是</td>
                            <td>用户名或邮箱</td>
                        </tr>
                        <tr>
                            <td>password</td>
                            <td>string</td>
                            <td>是</td>
                            <td>密码</td>
                        </tr>
                    </tbody>
                </table>

                <h4>请求示例</h4>
                <div class="code-block">curl -X POST /api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'</div>

                <h4>响应示例</h4>
                <div class="response-example">
                    <strong>200 OK</strong>
                    <div class="code-block">{"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}</div>
                </div>
                <div class="error-example">
                    <strong>401 Unauthorized</strong>
                    <div class="code-block">{"error": "Invalid credentials"}</div>
                </div>
            </div>
        </div>

        <div class="endpoint">
            <div class="endpoint-header">
                <span class="endpoint-method GET">GET</span>
                <span class="endpoint-path">/api/v1/auth/profile</span>
                <span style="margin-left: 20px; color: #6c757d;">获取用户信息</span>
                <span style="margin-left: 10px; background: #fd7e14; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">需要认证</span>
            </div>
            <div class="endpoint-body">
                <h4>请求头</h4>
                <div class="code-block">Authorization: Bearer &lt;your-token&gt;</div>

                <h4>请求示例</h4>
                <div class="code-block">curl -X GET /api/v1/auth/profile \
  -H "Authorization: Bearer &lt;your-token&gt;"</div>

                <h4>响应示例</h4>
                <div class="response-example">
                    <strong>200 OK</strong>
                    <div class="code-block">{
  "id": 1,
  "username": "testuser",
  "email": "<EMAIL>",
  "role": "user",
  "created_at": "2024-01-01T00:00:00Z"
}</div>
                </div>
                <div class="error-example">
                    <strong>401 Unauthorized</strong>
                    <div class="code-block">{"error": "Unauthorized"}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🌐 代理管理接口示例</h2>

        <div class="endpoint">
            <div class="endpoint-header">
                <span class="endpoint-method GET">GET</span>
                <span class="endpoint-path">/api/v1/proxies</span>
                <span style="margin-left: 20px; color: #6c757d;">获取代理列表</span>
                <span style="margin-left: 10px; background: #fd7e14; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">需要认证</span>
            </div>
            <div class="endpoint-body">
                <h4>查询参数</h4>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必填</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>page</td>
                            <td>int</td>
                            <td>否</td>
                            <td>页码，默认1</td>
                        </tr>
                        <tr>
                            <td>limit</td>
                            <td>int</td>
                            <td>否</td>
                            <td>每页数量，默认20</td>
                        </tr>
                        <tr>
                            <td>status</td>
                            <td>string</td>
                            <td>否</td>
                            <td>代理状态：active, inactive</td>
                        </tr>
                    </tbody>
                </table>

                <h4>请求示例</h4>
                <div class="code-block">curl -X GET "/api/v1/proxies?page=1&limit=10&status=active" \
  -H "Authorization: Bearer &lt;your-token&gt;"</div>

                <h4>响应示例</h4>
                <div class="response-example">
                    <strong>200 OK</strong>
                    <div class="code-block">{
  "data": [
    {
      "id": 1,
      "host": "*************",
      "port": 8080,
      "type": "http",
      "status": "active",
      "response_time": 150,
      "last_checked": "2024-01-01T12:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10
  }
}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>💡 使用提示</h2>
        <ul>
            <li><strong>认证</strong>：大部分接口需要在请求头中携带 <code>Authorization: Bearer &lt;token&gt;</code></li>
            <li><strong>内容类型</strong>：POST/PUT请求需要设置 <code>Content-Type: application/json</code></li>
            <li><strong>分页</strong>：列表接口支持 <code>page</code> 和 <code>limit</code> 参数进行分页</li>
            <li><strong>错误处理</strong>：所有错误响应都包含 <code>error</code> 字段说明错误原因</li>
            <li><strong>权限</strong>：部分接口需要管理员权限，会返回403错误</li>
        </ul>
    </div>

    <div class="nav-links">
        <a href="/api/v1/">返回API概览</a>
        <a href="/">返回首页</a>
    </div>

    <footer style="text-align: center; margin-top: 50px; color: #6c757d;">
        <p>© 2024 Proxy Manager API v1 | 完整的RESTful API文档</p>
    </footer>
</body>
</html>`
}
