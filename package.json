{"name": "proxy-manager", "version": "1.0.0", "description": "A high-performance proxy management system", "scripts": {"dev": "./scripts/dev.sh", "dev:backend": "./scripts/dev.sh --backend", "dev:frontend": "./scripts/dev.sh --frontend", "dev:full": "./scripts/dev.sh --full", "install:all": "./scripts/dev.sh --install", "check": "./scripts/dev.sh --check", "setup": "make setup-dev", "build": "make build", "test": "make test", "clean": "make clean", "docker:up": "make docker-run", "docker:down": "make docker-stop", "docker:restart": "make docker-restart"}, "keywords": ["proxy", "management", "golang", "react", "typescript"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}