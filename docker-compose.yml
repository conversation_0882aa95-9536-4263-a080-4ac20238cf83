version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: proxy-manager-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=${PROXY_MANAGER_POSTGRES_DATABASE:-proxy_manager}
      - POSTGRES_USER=${PROXY_MANAGER_POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${PROXY_MANAGER_POSTGRES_PASSWORD:-proxy_manager_2025}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${PROXY_MANAGER_POSTGRES_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - proxy-manager-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: proxy-manager-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - proxy-manager-network
    restart: unless-stopped

  # ProxyFlow 主应用
  proxy-manager:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: proxy-manager-app
    ports:
      - "8080:8080"
    environment:
      # 数据库配置
      - PROXY_MANAGER_POSTGRES_HOST=postgres
      - PROXY_MANAGER_POSTGRES_PORT=5432
      - PROXY_MANAGER_POSTGRES_USER=${PROXY_MANAGER_POSTGRES_USER:-postgres}
      - PROXY_MANAGER_POSTGRES_PASSWORD=${PROXY_MANAGER_POSTGRES_PASSWORD:-proxy_manager_2025}
      - PROXY_MANAGER_POSTGRES_DATABASE=${PROXY_MANAGER_POSTGRES_DATABASE:-proxy_manager}
      - PROXY_MANAGER_POSTGRES_SSL_MODE=disable
      # Redis 配置
      - PROXY_MANAGER_REDIS_ADDR=redis:6379
      - PROXY_MANAGER_REDIS_PASSWORD=
      - PROXY_MANAGER_REDIS_DB=0
      # 应用配置
      - PROXY_MANAGER_SERVER_PORT=8080
      - PROXY_MANAGER_SERVER_MODE=${PROXY_MANAGER_SERVER_MODE:-release}
      - PROXY_MANAGER_LOG_LEVEL=${PROXY_MANAGER_LOG_LEVEL:-info}
      # 认证配置 (必须通过 .env 文件设置)
      - PROXY_MANAGER_JWT_SECRET=${PROXY_MANAGER_JWT_SECRET}
      - PROXY_MANAGER_SUPER_API_KEY=${PROXY_MANAGER_SUPER_API_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    networks:
      - proxy-manager-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis Exporter for Prometheus
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: proxy-manager-redis-exporter
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis:6379
    depends_on:
      - redis
    networks:
      - proxy-manager-network
    restart: unless-stopped

  # 可选：Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: proxy-manager-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    depends_on:
      - proxy-manager
      - redis-exporter
    networks:
      - proxy-manager-network
    restart: unless-stopped

  # 可选：Grafana监控面板
  grafana:
    image: grafana/grafana:latest
    container_name: proxy-manager-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - proxy-manager-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  proxy-manager-network:
    driver: bridge 