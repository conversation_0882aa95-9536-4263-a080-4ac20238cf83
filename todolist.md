请帮我优化web页面：
1. 内容有个最大合理宽度。
2. 添加代理支持批量添加，比如每行按什么格式添加。
3. 个人资料、设置、退出登录目前都不能点击。左下角个人信息也不能点击。
4. 设计一个美观的favicon和logo。
5. 帮我项目取一个有科技感、简洁的名字，然后应用到页面上去。
6. 合理的地方增加github 源码仓库地址的链接（可配置）。
7. 系统监控路由提示请求的资源不存在。


优化后端
1. proxy 可能是这样的信息（但也可能某些字段缺失）。提供的API可能会需要根据相同的 city_name 或者 country_code 匹配，请何优化后端以及前端展示。请跟我确认了再修改。
{
    "id": "d-16844894371",
    "username": "nmbcimpu",
    "password": "rifegq1h0tva",
    "proxy_address": "**************",
    "port": 6540,
    "valid": true,
    "last_verification": "2025-06-11T17:48:14.191112-07:00",
    "country_code": "US",
    "city_name": "Buffalo",
    "asn_name": "As-Colocrossing",
    "asn_number": 36352,
    "high_country_confidence": true,
    "created_at": "2025-04-30T06:20:46.914965-07:00"
  }

