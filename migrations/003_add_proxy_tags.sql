-- 添加代理标签功能
-- 创建时间: 2025-06-28

-- 代理标签表
CREATE TABLE IF NOT EXISTS proxy_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(7) DEFAULT '#3B82F6', -- 十六进制颜色代码
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL
);

-- 代理标签关联表（多对多关系）
CREATE TABLE IF NOT EXISTS proxy_tag_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    proxy_id UUID NOT NULL REFERENCES proxies(id) ON DELETE CASCADE,
    tag_id UUID NOT NULL REFERENCES proxy_tags(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE(proxy_id, tag_id)
);

-- 为代理表添加场景化字段
ALTER TABLE proxies ADD COLUMN IF NOT EXISTS scenario VARCHAR(100); -- 使用场景
ALTER TABLE proxies ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 10); -- 优先级 1-10

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_proxy_tags_name ON proxy_tags(name);
CREATE INDEX IF NOT EXISTS idx_proxy_tag_assignments_proxy_id ON proxy_tag_assignments(proxy_id);
CREATE INDEX IF NOT EXISTS idx_proxy_tag_assignments_tag_id ON proxy_tag_assignments(tag_id);
CREATE INDEX IF NOT EXISTS idx_proxies_scenario ON proxies(scenario);
CREATE INDEX IF NOT EXISTS idx_proxies_priority ON proxies(priority);

-- 插入一些预定义的标签
INSERT INTO proxy_tags (name, description, color) VALUES 
    ('google-search', '谷歌搜索专用', '#4285F4'),
    ('ecommerce-scraping', '电商数据采集', '#FF6B35'),
    ('social-media', '社交媒体', '#1DA1F2'),
    ('high-speed', '高速代理', '#22C55E'),
    ('stable', '稳定代理', '#8B5CF6'),
    ('anonymous', '高匿代理', '#F59E0B'),
    ('testing', '测试专用', '#EF4444'),
    ('production', '生产环境', '#10B981')
ON CONFLICT (name) DO NOTHING;

-- 创建触发器自动更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_proxy_tags_updated_at 
    BEFORE UPDATE ON proxy_tags 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 添加注释
COMMENT ON TABLE proxy_tags IS '代理标签表，用于对代理进行分类和标记';
COMMENT ON TABLE proxy_tag_assignments IS '代理标签关联表，建立代理和标签的多对多关系';
COMMENT ON COLUMN proxies.scenario IS '代理使用场景，如web-scraping、api-testing等';
COMMENT ON COLUMN proxies.priority IS '代理优先级，1-10，数字越大优先级越高';
