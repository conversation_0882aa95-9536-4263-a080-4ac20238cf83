-- RBAC权限管理系统
-- 创建时间: 2025-06-28

-- 权限表
CREATE TABLE IF NOT EXISTS permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 角色表（扩展版）
CREATE TABLE IF NOT EXISTS roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL
);

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE(role_id, permission_id)
);

-- 用户角色关联表（支持多角色）
CREATE TABLE IF NOT EXISTS user_role_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID REFERENCES users(id) ON DELETE SET NULL,
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, role_id)
);

-- 用户直接权限表
CREATE TABLE IF NOT EXISTS user_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    granted BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, permission_id)
);

-- 团队表
CREATE TABLE IF NOT EXISTS teams (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL
);

-- 团队成员表
CREATE TABLE IF NOT EXISTS team_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    added_by UUID REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE(team_id, user_id)
);

-- 资源访问控制表
CREATE TABLE IF NOT EXISTS resource_access (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    resource_id VARCHAR(255) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    subject_id UUID NOT NULL,
    subject_type VARCHAR(20) NOT NULL CHECK (subject_type IN ('user', 'team')),
    permission VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE(resource_id, resource_type, subject_id, subject_type, permission)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_permissions_resource_action ON permissions(resource, action);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_user_role_assignments_user_id ON user_role_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_user_role_assignments_role_id ON user_role_assignments(role_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_permission_id ON user_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_team_members_team_id ON team_members(team_id);
CREATE INDEX IF NOT EXISTS idx_team_members_user_id ON team_members(user_id);
CREATE INDEX IF NOT EXISTS idx_resource_access_resource ON resource_access(resource_id, resource_type);
CREATE INDEX IF NOT EXISTS idx_resource_access_subject ON resource_access(subject_id, subject_type);

-- 插入预定义权限
INSERT INTO permissions (name, resource, action, description) VALUES 
    ('proxy:read', 'proxy', 'read', '查看代理信息'),
    ('proxy:write', 'proxy', 'write', '创建和修改代理'),
    ('proxy:delete', 'proxy', 'delete', '删除代理'),
    ('proxy:manage', 'proxy', 'manage', '管理代理（包括批量操作）'),
    ('user:read', 'user', 'read', '查看用户信息'),
    ('user:write', 'user', 'write', '创建和修改用户'),
    ('user:delete', 'user', 'delete', '删除用户'),
    ('user:manage', 'user', 'manage', '管理用户权限和角色'),
    ('task:read', 'task', 'read', '查看任务信息'),
    ('task:write', 'task', 'write', '创建和修改任务'),
    ('task:delete', 'task', 'delete', '删除任务'),
    ('task:execute', 'task', 'execute', '执行任务'),
    ('tag:read', 'tag', 'read', '查看标签信息'),
    ('tag:write', 'tag', 'write', '创建和修改标签'),
    ('tag:delete', 'tag', 'delete', '删除标签'),
    ('team:read', 'team', 'read', '查看团队信息'),
    ('team:write', 'team', 'write', '创建和修改团队'),
    ('team:delete', 'team', 'delete', '删除团队'),
    ('team:manage', 'team', 'manage', '管理团队成员'),
    ('system:read', 'system', 'read', '查看系统信息'),
    ('system:write', 'system', 'write', '修改系统配置'),
    ('system:manage', 'system', 'manage', '系统管理')
ON CONFLICT (name) DO NOTHING;

-- 插入预定义角色
INSERT INTO roles (name, display_name, description, is_system) VALUES 
    ('super_admin', '超级管理员', '拥有所有权限的超级管理员', TRUE),
    ('admin', '管理员', '拥有大部分管理权限的管理员', TRUE),
    ('user', '普通用户', '普通用户，可以查看和使用代理', TRUE),
    ('guest', '访客', '只读访问权限', TRUE),
    ('team_lead', '团队负责人', '团队负责人，可以管理团队成员', TRUE),
    ('team_member', '团队成员', '团队成员，可以访问团队资源', TRUE)
ON CONFLICT (name) DO NOTHING;

-- 为角色分配权限
-- 超级管理员：所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'super_admin'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 管理员：除系统管理外的所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'admin' AND p.name NOT IN ('system:manage')
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 普通用户：基本读写权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'user' AND p.name IN (
    'proxy:read', 'proxy:write', 'task:read', 'task:write', 'task:execute', 'tag:read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 访客：只读权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'guest' AND p.action = 'read'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 团队负责人：团队管理权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'team_lead' AND p.name IN (
    'proxy:read', 'proxy:write', 'task:read', 'task:write', 'task:execute', 
    'tag:read', 'team:read', 'team:write', 'team:manage', 'user:read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 团队成员：基本权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'team_member' AND p.name IN (
    'proxy:read', 'task:read', 'task:write', 'task:execute', 'tag:read', 'team:read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 创建触发器自动更新 updated_at 字段
CREATE TRIGGER update_roles_updated_at 
    BEFORE UPDATE ON roles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teams_updated_at 
    BEFORE UPDATE ON teams 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 添加注释
COMMENT ON TABLE permissions IS '权限表，定义系统中的所有权限';
COMMENT ON TABLE roles IS '角色表，定义系统中的角色';
COMMENT ON TABLE role_permissions IS '角色权限关联表';
COMMENT ON TABLE user_role_assignments IS '用户角色关联表，支持多角色';
COMMENT ON TABLE user_permissions IS '用户直接权限表，可以覆盖角色权限';
COMMENT ON TABLE teams IS '团队表';
COMMENT ON TABLE team_members IS '团队成员表';
COMMENT ON TABLE resource_access IS '资源访问控制表';
