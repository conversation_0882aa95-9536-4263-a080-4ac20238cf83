package database

import (
	"context"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"
)

// MigrationManager 数据库迁移管理器
type MigrationManager struct {
	db     *sqlx.DB
	logger *logrus.Logger
}

// NewMigrationManager 创建迁移管理器
func NewMigrationManager(db *sqlx.DB, logger *logrus.Logger) *MigrationManager {
	return &MigrationManager{
		db:     db,
		logger: logger,
	}
}

// Migration 迁移信息
type Migration struct {
	Version string
	Name    string
	SQL     string
}

// RunMigrations 运行数据库迁移
func (m *MigrationManager) RunMigrations(ctx context.Context, migrationsDir string) error {
	// 创建迁移记录表
	if err := m.createMigrationTable(ctx); err != nil {
		return fmt.Errorf("failed to create migration table: %w", err)
	}

	// 读取迁移文件
	migrations, err := m.loadMigrations(migrationsDir)
	if err != nil {
		return fmt.Errorf("failed to load migrations: %w", err)
	}

	// 获取已执行的迁移
	executedMigrations, err := m.getExecutedMigrations(ctx)
	if err != nil {
		return fmt.Errorf("failed to get executed migrations: %w", err)
	}

	// 执行未执行的迁移
	for _, migration := range migrations {
		if _, executed := executedMigrations[migration.Version]; !executed {
			if err := m.executeMigration(ctx, migration); err != nil {
				return fmt.Errorf("failed to execute migration %s: %w", migration.Version, err)
			}
			m.logger.WithField("version", migration.Version).Info("Migration executed successfully")
		}
	}

	m.logger.Info("All migrations completed successfully")
	return nil
}

// createMigrationTable 创建迁移记录表
func (m *MigrationManager) createMigrationTable(ctx context.Context) error {
	query := `
		CREATE TABLE IF NOT EXISTS schema_migrations (
			version VARCHAR(255) PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
		)`

	_, err := m.db.ExecContext(ctx, query)
	return err
}

// loadMigrations 加载迁移文件
func (m *MigrationManager) loadMigrations(migrationsDir string) ([]Migration, error) {
	files, err := ioutil.ReadDir(migrationsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read migrations directory: %w", err)
	}

	var migrations []Migration
	for _, file := range files {
		if !strings.HasSuffix(file.Name(), ".sql") {
			continue
		}

		// 解析文件名获取版本号
		parts := strings.SplitN(file.Name(), "_", 2)
		if len(parts) != 2 {
			m.logger.WithField("filename", file.Name()).Warn("Invalid migration filename format")
			continue
		}

		version := parts[0]
		name := strings.TrimSuffix(parts[1], ".sql")

		// 读取SQL内容
		filePath := filepath.Join(migrationsDir, file.Name())
		content, err := ioutil.ReadFile(filePath)
		if err != nil {
			return nil, fmt.Errorf("failed to read migration file %s: %w", file.Name(), err)
		}

		migrations = append(migrations, Migration{
			Version: version,
			Name:    name,
			SQL:     string(content),
		})
	}

	// 按版本号排序
	sort.Slice(migrations, func(i, j int) bool {
		return migrations[i].Version < migrations[j].Version
	})

	return migrations, nil
}

// getExecutedMigrations 获取已执行的迁移
func (m *MigrationManager) getExecutedMigrations(ctx context.Context) (map[string]bool, error) {
	query := `SELECT version FROM schema_migrations`

	var versions []string
	err := m.db.SelectContext(ctx, &versions, query)
	if err != nil {
		return nil, err
	}

	executed := make(map[string]bool)
	for _, version := range versions {
		executed[version] = true
	}

	return executed, nil
}

// executeMigration 执行单个迁移
func (m *MigrationManager) executeMigration(ctx context.Context, migration Migration) error {
	tx, err := m.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// 执行迁移SQL
	_, err = tx.ExecContext(ctx, migration.SQL)
	if err != nil {
		return fmt.Errorf("failed to execute migration SQL: %w", err)
	}

	// 记录迁移执行
	recordQuery := `INSERT INTO schema_migrations (version, name) VALUES ($1, $2)`
	_, err = tx.ExecContext(ctx, recordQuery, migration.Version, migration.Name)
	if err != nil {
		return fmt.Errorf("failed to record migration: %w", err)
	}

	return tx.Commit()
}

// GetMigrationStatus 获取迁移状态
func (m *MigrationManager) GetMigrationStatus(ctx context.Context, migrationsDir string) ([]MigrationStatus, error) {
	// 加载所有迁移文件
	migrations, err := m.loadMigrations(migrationsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to load migrations: %w", err)
	}

	// 获取已执行的迁移
	executedMigrations, err := m.getExecutedMigrations(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get executed migrations: %w", err)
	}

	var status []MigrationStatus
	for _, migration := range migrations {
		executed := executedMigrations[migration.Version]
		status = append(status, MigrationStatus{
			Version:  migration.Version,
			Name:     migration.Name,
			Executed: executed,
		})
	}

	return status, nil
}

// MigrationStatus 迁移状态
type MigrationStatus struct {
	Version  string `json:"version"`
	Name     string `json:"name"`
	Executed bool   `json:"executed"`
}

// RollbackMigration 回滚迁移（简单实现，实际项目中需要更复杂的回滚逻辑）
func (m *MigrationManager) RollbackMigration(ctx context.Context, version string) error {
	tx, err := m.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// 删除迁移记录
	query := `DELETE FROM schema_migrations WHERE version = $1`
	result, err := tx.ExecContext(ctx, query, version)
	if err != nil {
		return fmt.Errorf("failed to delete migration record: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("migration %s not found", version)
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit rollback: %w", err)
	}

	m.logger.WithField("version", version).Info("Migration rolled back successfully")
	return nil
}

// CreateMigration 创建新的迁移文件
func (m *MigrationManager) CreateMigration(migrationsDir, name string) (string, error) {
	// 生成版本号（基于时间戳）
	version := fmt.Sprintf("%d", time.Now().Unix())

	// 生成文件名
	filename := fmt.Sprintf("%s_%s.sql", version, strings.ReplaceAll(name, " ", "_"))
	filepath := filepath.Join(migrationsDir, filename)

	// 创建迁移文件模板
	template := fmt.Sprintf(`-- Migration: %s
-- Created: %s

-- Add your migration SQL here

`, name, time.Now().Format("2006-01-02 15:04:05"))

	err := ioutil.WriteFile(filepath, []byte(template), 0644)
	if err != nil {
		return "", fmt.Errorf("failed to create migration file: %w", err)
	}

	return filepath, nil
}
