package database

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
	"github.com/sirupsen/logrus"
)

// PostgresConfig PostgreSQL配置
type PostgresConfig struct {
	Host            string        `mapstructure:"host"`
	Port            int           `mapstructure:"port"`
	User            string        `mapstructure:"user"`
	Password        string        `mapstructure:"password"`
	Database        string        `mapstructure:"database"`
	SSLMode         string        `mapstructure:"ssl_mode"`
	MaxOpenConns    int           `mapstructure:"max_open_conns"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `mapstructure:"conn_max_idle_time"`
}

// PostgresClient PostgreSQL客户端
type PostgresClient struct {
	db     *sqlx.DB
	logger *logrus.Logger
	config *PostgresConfig
}

// NewPostgresClient 创建PostgreSQL客户端
func NewPostgresClient(config *PostgresConfig, logger *logrus.Logger) (*PostgresClient, error) {
	// 构建安全的 DSN，添加连接超时和其他安全参数
	dsn := fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s connect_timeout=10 statement_timeout=30000",
		config.Host, config.Port, config.User, config.Password, config.Database, config.SSLMode,
	)

	// 在日志中隐藏密码
	safeDSN := fmt.Sprintf(
		"host=%s port=%d user=%s password=*** dbname=%s sslmode=%s",
		config.Host, config.Port, config.User, config.Database, config.SSLMode,
	)
	logger.WithField("dsn", safeDSN).Debug("Connecting to PostgreSQL")

	db, err := sqlx.Connect("postgres", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to PostgreSQL: %w", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(config.MaxOpenConns)
	db.SetMaxIdleConns(config.MaxIdleConns)
	db.SetConnMaxLifetime(config.ConnMaxLifetime)
	db.SetConnMaxIdleTime(config.ConnMaxIdleTime)

	client := &PostgresClient{
		db:     db,
		logger: logger,
		config: config,
	}

	// 测试连接
	if err := client.Ping(context.Background()); err != nil {
		return nil, fmt.Errorf("failed to ping PostgreSQL: %w", err)
	}

	logger.WithFields(logrus.Fields{
		"host":     config.Host,
		"port":     config.Port,
		"database": config.Database,
	}).Info("Connected to PostgreSQL")

	return client, nil
}

// GetDB 获取数据库连接
func (c *PostgresClient) GetDB() *sqlx.DB {
	return c.db
}

// Ping 测试数据库连接
func (c *PostgresClient) Ping(ctx context.Context) error {
	return c.db.PingContext(ctx)
}

// Close 关闭数据库连接
func (c *PostgresClient) Close() error {
	if c.db != nil {
		return c.db.Close()
	}
	return nil
}

// BeginTx 开始事务
func (c *PostgresClient) BeginTx(ctx context.Context, opts *sql.TxOptions) (*sqlx.Tx, error) {
	return c.db.BeginTxx(ctx, opts)
}

// WithTransaction 在事务中执行函数
func (c *PostgresClient) WithTransaction(ctx context.Context, fn func(*sqlx.Tx) error) error {
	tx, err := c.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if p := recover(); p != nil {
			if rollbackErr := tx.Rollback(); rollbackErr != nil {
				c.logger.WithError(rollbackErr).Error("Failed to rollback transaction after panic")
			}
			panic(p)
		}
	}()

	if err := fn(tx); err != nil {
		if rollbackErr := tx.Rollback(); rollbackErr != nil {
			c.logger.WithError(rollbackErr).Error("Failed to rollback transaction")
		}
		return err
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// GetStats 获取数据库连接统计信息
func (c *PostgresClient) GetStats() sql.DBStats {
	return c.db.Stats()
}

// ConnectionPoolMetrics 连接池指标
type ConnectionPoolMetrics struct {
	OpenConnections    int           `json:"open_connections"`
	InUseConnections   int           `json:"in_use_connections"`
	IdleConnections    int           `json:"idle_connections"`
	WaitCount          int64         `json:"wait_count"`
	WaitDuration       time.Duration `json:"wait_duration"`
	MaxIdleClosed      int64         `json:"max_idle_closed"`
	MaxLifetimeClosed  int64         `json:"max_lifetime_closed"`
	MaxOpenConnections int           `json:"max_open_connections"`
}

// GetConnectionMetrics 获取连接池详细指标
func (c *PostgresClient) GetConnectionMetrics() ConnectionPoolMetrics {
	stats := c.db.Stats()
	return ConnectionPoolMetrics{
		OpenConnections:    stats.OpenConnections,
		InUseConnections:   stats.InUse,
		IdleConnections:    stats.Idle,
		WaitCount:          stats.WaitCount,
		WaitDuration:       stats.WaitDuration,
		MaxIdleClosed:      stats.MaxIdleClosed,
		MaxLifetimeClosed:  stats.MaxLifetimeClosed,
		MaxOpenConnections: stats.MaxOpenConnections,
	}
}

// LogConnectionStats 记录连接池统计信息
func (c *PostgresClient) LogConnectionStats() {
	metrics := c.GetConnectionMetrics()
	c.logger.WithFields(logrus.Fields{
		"open_connections":     metrics.OpenConnections,
		"in_use_connections":   metrics.InUseConnections,
		"idle_connections":     metrics.IdleConnections,
		"wait_count":           metrics.WaitCount,
		"wait_duration":        metrics.WaitDuration,
		"max_idle_closed":      metrics.MaxIdleClosed,
		"max_lifetime_closed":  metrics.MaxLifetimeClosed,
		"max_open_connections": metrics.MaxOpenConnections,
	}).Info("Connection pool statistics")
}

// HealthCheck 健康检查
func (c *PostgresClient) HealthCheck(ctx context.Context) error {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	var result int
	err := c.db.GetContext(ctx, &result, "SELECT 1")
	if err != nil {
		return fmt.Errorf("health check failed: %w", err)
	}

	return nil
}

// ExecuteInTransaction 在事务中执行多个SQL语句
func (c *PostgresClient) ExecuteInTransaction(ctx context.Context, queries []string) error {
	return c.WithTransaction(ctx, func(tx *sqlx.Tx) error {
		for _, query := range queries {
			if _, err := tx.ExecContext(ctx, query); err != nil {
				return fmt.Errorf("failed to execute query: %s, error: %w", query, err)
			}
		}
		return nil
	})
}

// BulkInsert 批量插入数据
func (c *PostgresClient) BulkInsert(ctx context.Context, query string, args []interface{}) error {
	return c.WithTransaction(ctx, func(tx *sqlx.Tx) error {
		stmt, err := tx.PreparexContext(ctx, query)
		if err != nil {
			return fmt.Errorf("failed to prepare statement: %w", err)
		}
		defer stmt.Close()

		for _, arg := range args {
			if _, err := stmt.ExecContext(ctx, arg); err != nil {
				return fmt.Errorf("failed to execute bulk insert: %w", err)
			}
		}
		return nil
	})
}

// GetDefaultConfig 获取默认PostgreSQL配置
func GetDefaultPostgresConfig() *PostgresConfig {
	return &PostgresConfig{
		Host:            "localhost",
		Port:            5432,
		User:            "postgres",
		Password:        "", // 生产环境中应从环境变量获取
		Database:        "proxy_manager",
		SSLMode:         "prefer",         // 默认启用 SSL，生产环境应使用 require
		MaxOpenConns:    50,               // 增加连接池大小
		MaxIdleConns:    10,               // 增加空闲连接数
		ConnMaxLifetime: 30 * time.Minute, // 延长连接生命周期
		ConnMaxIdleTime: 10 * time.Minute, // 延长空闲时间
	}
}
