# ProxyFlow 环境变量配置模板
# 复制此文件为 .env 并填入实际值

# ================================
# 🔐 敏感信息配置 (必须设置)
# ================================

# JWT 密钥 - 用于用户认证 (使用 scripts/generate_jwt_secret.go 生成)
PROXY_MANAGER_JWT_SECRET=""

# 超级 API 密钥 - 用于管理员操作 (使用 scripts/generate_jwt_secret.go 生成)
PROXY_MANAGER_SUPER_API_KEY=""

# API 密钥加密密钥 - 用于加密存储的API密钥 (32字节)
PROXY_MANAGER_API_KEY_ENCRYPTION_KEY=""

# ================================
# 🗄️ 数据库配置
# ================================

# PostgreSQL 数据库密码
PROXY_MANAGER_POSTGRES_PASSWORD=""

# PostgreSQL 连接配置 (可选，使用默认值)
# PROXY_MANAGER_POSTGRES_HOST="localhost"
# PROXY_MANAGER_POSTGRES_PORT="5432"
# PROXY_MANAGER_POSTGRES_USER="postgres"
# PROXY_MANAGER_POSTGRES_DATABASE="proxy_manager"
# PROXY_MANAGER_POSTGRES_SSL_MODE="disable"

# Redis 连接配置 (可选，使用默认值)
# PROXY_MANAGER_REDIS_ADDR="localhost:6379"
# PROXY_MANAGER_REDIS_PASSWORD=""
# PROXY_MANAGER_REDIS_DB="0"

# ================================
# 🚀 部署配置 (可选)
# ================================

# 应用环境 (development, testing, production)
# PROXY_MANAGER_ENV="development"

# 服务器端口
# PROXY_MANAGER_SERVER_PORT="8080"

# 服务器模式 (debug, release)
# PROXY_MANAGER_SERVER_MODE="debug"

# ================================
# 📝 日志配置 (可选)
# ================================

# 日志级别 (debug, info, warn, error)
# PROXY_MANAGER_LOG_LEVEL="info"

# 日志格式 (json, text)
# PROXY_MANAGER_LOG_FORMAT="json"

# 日志输出 (stdout, file)
# PROXY_MANAGER_LOG_OUTPUT="stdout"

# ================================
# 🔧 高级配置 (可选)
# ================================

# 启用开发模式特性
# PROXY_MANAGER_DEV_MODE="false"

# 启用调试日志
# PROXY_MANAGER_DEBUG="false"

# 配置文件路径覆盖
# PROXY_MANAGER_CONFIG_PATH="config/config.yaml"

# ================================
# 🌐 代理提供商配置
# ================================

# Webshare API Token - 用于从 Webshare 获取代理列表
# 获取方式: 登录 https://proxy.webshare.io/ -> Account -> API Token
WEBSHARE_API_TOKEN=""