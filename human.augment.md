基于当前的 ProxyFlow 项目，我需要你实现一个自动化的免费代理采集和管理系统。请按照以下详细要求完成：

**项目背景**：
- 项目已有完整的代理管理后端系统（Go + PostgreSQL + Redis）
- `freeProxy/sources.json` 文件包含了各种类型的免费代理源URL
- 需要实现一个独立的代理采集服务，自动从这些源获取并验证代理

**具体实现要求**：

1. **代理采集模块**：
   - 创建独立的包/文件夹（建议：`internal/collector/` 或 `pkg/collector/`）
   - 解析 `freeProxy/sources.json` 中的代理源配置
   - 对每个代理源发起HTTP请求，解析返回的代理列表
   - 支持不同格式的代理数据（IP:PORT, 带协议前缀等）
   - 实现错误处理和重试机制
   - 将原始采集数据保存到文件（如 `data/raw_proxies`）

2. **代理验证模块**：
   - 实现并发的代理可用性检测
   - 检测指标包括：连通性、响应时间、匿名级别
   - 使用测试URL（如 httpbin.org/ip）验证代理功能
   - 设置合理的超时时间（建议5-10秒）
   - 记录验证结果和性能指标

3. **数据库集成**：
   - 将验证通过的代理自动添加到现有的 PostgreSQL 数据库
   - 使用现有的 `models.Proxy` 结构和 `repository.ProxyRepository` 接口
   - 避免重复添加相同的代理（基于 host:port 去重）
   - 更新代理的质量评分和地理位置信息

4. **服务架构**：
   - 创建独立的 `ProxyCollector` 服务
   - 使用 goroutine 在后台运行，不阻塞主程序
   - 实现可配置的采集间隔（建议30分钟-2小时）
   - 支持优雅关闭和错误恢复

5. **配置和监控**：
   - 在 `config.yaml` 中添加采集器相关配置
   - 集成到现有的日志系统（logrus）
   - 添加 Prometheus 指标（采集数量、成功率等）
   - 提供健康检查端点

6. **额外功能建议**：
   - 实现代理源的健康度评估（成功率统计）
   - 支持代理源的动态启用/禁用
   - 添加代理去重和清理机制
   - 实现采集历史记录和统计
   - 支持手动触发采集的API接口

**技术要求**：
- 遵循项目现有的代码风格和架构模式
- 使用项目已有的依赖（避免引入新的外部库）
- 实现适当的错误处理和日志记录
- 编写必要的单元测试
- 确保线程安全和资源管理

**交付物**：
- 完整的代理采集模块代码
- 配置文件更新
- 数据库迁移文件（如需要）
- 集成到主程序的代码修改
- 简要的使用文档和配置说明

请按照这个详细规范实现该功能，确保与现有系统的良好集成。


------------------------------------------------------------------------------------
 请优化 `internal/collector/verifier.go` 文件中的 `verifyProxies` 函数，以处理大规模代理验证场景（可能超过10万条代理）。具体需求如下：

**性能优化要求：**
1. 提高验证效率，能够处理10万+代理的并发验证
2. 优化内存使用，避免一次性加载所有代理到内存
3. 实现批量处理机制，分批验证代理列表

**进度监控要求：**
1. 添加实时进度提示功能，显示验证进度百分比
2. 提供验证速度统计（每秒验证数量）
3. 显示预计剩余时间
4. 支持通过API查询当前验证进度

**缓存机制要求：**
1. 创建本地文件缓存系统，记录每个代理的最后验证时间
2. 如果代理在24小时内已验证过且状态为有效，则跳过重复验证
3. 缓存文件格式建议使用JSON或TXT，包含字段：代理地址、最后验证时间、验证结果、质量分数
4. 实现缓存清理机制，自动清理过期或无效的缓存记录

**技术实现建议：**
1. 使用分页或流式处理避免内存溢出
2. 优化goroutine池大小和超时设置
3. 添加验证结果统计和日志记录
4. 考虑实现验证任务的暂停/恢复功能
5. 集成到现有的Prometheus监控系统中

**配置扩展：**
在 `config.yaml` 中添加相关配置项：
- 批处理大小
- 缓存文件路径
- 缓存有效期
- 进度报告间隔

请保持与现有代码架构的兼容性，并提供相应的单元测试。



------------------------------------------------------------------------------------
我觉得当前的大规模代理验证优化方案过于复杂，代码量太多。请帮我精简实现，要求如下：

**核心要求：**
1. 在不影响现有 ProxyFlow 功能的前提下，为 `internal/collector/verifier.go` 中的 `verifyProxies` 函数添加大规模验证优化
2. 只实现最核心的功能：批处理机制、基本缓存、简单进度显示
3. 移除不必要的复杂功能：详细的进度跟踪器、复杂的API接口、过度的配置选项

**具体实现要求：**
1. **批处理优化**：修改现有 `verifyProxies` 函数，支持分批处理大量代理（如10万+）
2. **简单缓存**：添加基本的文件缓存机制，避免24小时内重复验证相同代理
3. **基础进度**：在日志中显示验证进度，无需复杂的实时监控
4. **配置集成**：在现有配置文件中添加必要的配置项（批次大小、缓存路径等）

**限制条件：**
- 不要创建新的复杂结构体和接口
- 不要添加新的API端点
- 不要创建测试文件
- 保持代码简洁，总修改量控制在合理范围内
- 最终通过 `go run cmd/main.go` 能够正常运行验证功能

**验证方式：**
完成后能够通过 `go run cmd/main.go` 启动程序，并且大规模代理验证功能能够正常工作，显示基本的进度信息。


------------------------------------------------------------------------------------
我正在开发一个基于 Go 语言的代理池项目，其中包含一个 collector 收集器模块，负责收集代理并验证其有效性。核心逻辑位于 `internal/collector/` 目录中。

请帮我完成以下任务：

1. **源码分析**：深入阅读 `internal/collector/` 目录下的源码，特别关注 `verifyProxies` 函数的实现逻辑，理解当前的代理验证流程和数据结构。

2. **技术调研**：调研并推荐适合 Go 语言的命令行界面显示库，要求能够：
   - 实时动态更新显示内容
   - 支持进度条显示
   - 支持多行状态信息展示
   - 性能良好，适合高频更新

3. **功能需求**：为 `verifyProxies` 代理验证过程设计一个用户友好的命令行显示界面，需要包含：
   - 实时显示当前验证进度（已完成/总数）
   - 动态更新的进度条
   - 预估剩余时间计算
   - 验证成功/失败的实时统计
   - 当前正在验证的代理信息
   - 验证速度（代理/秒）

4. **实现建议**：基于源码分析结果，提供具体的实现方案，包括如何集成到现有的 `verifyProxies` 函数中，以及相关的代码修改建议。

请先分析现有代码结构，然后提供技术选型建议和具体的实现方案。
基于前面的技术调研和源码分析，请帮我选择最适合的 Go 语言命令行界面显示库（从 mpb、pterm、bubbletea 中选择），并提供具体的实现方案。

具体要求：
1. **技术选型决策**：基于我们项目的代理验证场景，明确推荐使用哪个库，并说明选择理由
2. **代码实现**：修改现有的 `verifyProxies` 和 `verifyProxiesConcurrently` 函数，集成选定的 CLI 库
3. **功能实现**：确保新的界面能够显示：
   - 实时验证进度（已完成/总数）
   - 动态进度条
   - 预估剩余时间（ETA）
   - 验证成功/失败统计
   - 当前验证速度（代理/秒）
   - 当前正在验证的代理信息
4. **集成要求**：
   - 保持与现有配置系统的兼容性
   - 不影响现有的批处理和并发机制
   - 支持优雅的错误处理和取消操作
   - 考虑性能影响，确保显示更新不会影响验证效率

请提供完整的代码修改方案，包括必要的依赖添加、配置调整和具体的代码实现。


------------------------------------------------------------------------------------
请帮我分析项目的启动流程和代理验证执行路径。具体需要：

1. **源码分析**：阅读 `cmd/main.go` 文件，理解项目的启动入口和初始化流程

2. **执行路径追踪**：从 main 函数开始，追踪代理验证功能的调用链路，找到以下关键点：
   - 代理收集器（collector）的初始化位置
   - `verifyProxies` 函数的调用入口
   - 代理验证任务的触发机制（定时任务、API调用等）

3. **集成验证**：检查我们刚刚添加的进度显示功能是否正确集成到实际的运行流程中，确认：
   - 新的进度显示配置是否被正确加载
   - `VerificationProgressDisplay` 是否在实际验证过程中被创建和使用
   - 进度更新是否在真实的代理验证流程中生效

4. **问题诊断**：如果发现进度显示功能没有在实际运行中生效，请：
   - 识别可能的原因（配置问题、调用路径问题、初始化问题等）
   - 提供具体的修复建议
   - 指出需要修改的文件和代码位置

请从 `cmd/main.go` 开始，逐步分析整个代理验证的执行流程，确保我们的进度显示功能能够在实际运行中正常工作。


------------------------------------------------------------------------------------
请对代理池项目中的高并发验证模块进行全面的代码审查和优化分析。该模块位于 `internal/concurrent/` 目录，主要包含 `engine.go`、`proxy_verifier.go` 等核心文件。

请按以下具体步骤进行分析：

**1. 架构和设计合理性审查**
- 分析 `Engine` 和 `ProxyVerifier` 的设计模式是否合理
- 检查并发控制机制（goroutine 池、通道缓冲区、锁机制）是否存在死锁、竞态条件或资源泄漏风险
- 评估错误处理和超时机制的完整性
- 检查内存管理和 goroutine 生命周期管理

**2. 集成使用情况分析**
- 检查 `internal/collector/verifier.go` 中对该模块的调用方式是否正确
- 验证配置参数传递和默认值设置是否合理
- 分析缓存集成（`SetCache` 方法）的实现是否存在问题
- 检查进度回调和统计信息更新的准确性

**3. 过度设计识别**
- 评估是否存在不必要的抽象层或复杂的设计模式
- 识别可能未被充分利用的功能特性（如自动扩缩容、指标收集等）
- 分析配置选项的复杂度是否超出实际需求
- 检查是否有重复或冗余的代码逻辑

**4. 精简和优化建议**
- 提出具体的代码简化方案，保持高性能的同时提高可读性
- 建议移除或合并不必要的组件和接口
- 优化错误处理和日志记录的复杂度
- 提供重构后的核心代码示例，确保功能完整性

**5. 可维护性改进**
- 建议如何简化调试过程（如添加关键调试点、改进日志输出）
- 提出单元测试和集成测试的改进方案
- 建议文档和注释的改进方向

请基于当前项目的实际使用场景（代理验证）和性能要求，提供平衡性能、可读性和可维护性的优化方案。



------------------------------------------------------------------------------------



------------------------------------------------------------------------------------




------------------------------------------------------------------------------------



------------------------------------------------------------------------------------




------------------------------------------------------------------------------------


