# 构建阶段
FROM golang:1.24-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装依赖
RUN apk add --no-cache git

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o proxyFlow cmd/main.go

# 运行阶段
FROM alpine:latest

# 安装ca-certificates
RUN apk --no-cache add ca-certificates

# 创建非root用户
RUN addgroup -g 1001 -S proxyuser && \
    adduser -u 1001 -S proxyuser -G proxyuser

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/proxyFlow .

# 复制配置文件
COPY --from=builder /app/config/config.example.yaml ./config/config.yaml

# 创建日志目录
RUN mkdir -p logs && chown -R proxyuser:proxyuser /app

# 切换到非root用户
USER proxyuser

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# 启动应用
CMD ["./proxyFlow"] 