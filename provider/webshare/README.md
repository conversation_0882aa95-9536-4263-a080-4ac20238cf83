# Webshare 代理导入工具

这个工具用于从 Webshare API 获取代理并导入到 ProxyFlow 中。

## 功能特性

- ✅ 从 Webshare API 直接获取所有可用代理
- ✅ 支持分页获取，自动处理所有页面
- ✅ 支持从本地 JSON 文件导入（向后兼容）
- ✅ 批量导入和单个导入的回退机制
- ✅ 详细的日志输出和错误处理

## 环境配置

### 1. 获取 Webshare API Token

1. 登录 [Webshare](https://proxy.webshare.io/)
2. 进入 Account -> API Token
3. 复制你的 API Token

### 2. 配置环境变量

在项目根目录的 `.env` 文件中添加：

```bash
# Webshare API Token
WEBSHARE_API_TOKEN="your_webshare_api_token_here"

# ProxyFlow 认证（二选一）
PROXY_MANAGER_SUPER_API_KEY="your_super_api_key"
# 或者使用用户名密码
PROXY_ADMIN_USER="admin"
PROXY_ADMIN_PASS="your_password"
```

## 使用方法

### 方法 1: 从 API 直接获取（推荐）

```bash
# 从 Webshare API 获取所有代理并导入
go run provider/webshare/fetchAndImport.go -fetch-api

# 指定代理类型
go run provider/webshare/fetchAndImport.go -fetch-api -proxy-type=socks5

# 指定 API 服务器地址
go run provider/webshare/fetchAndImport.go -fetch-api -api-url=http://localhost:8080
```

### 方法 2: 从文件导入（向后兼容）

```bash
# 从 JSON 文件导入
go run provider/webshare/fetchAndImport.go -file=webshare_proxy.json

# 如果没有指定文件路径，会自动尝试从 API 获取
go run provider/webshare/fetchAndImport.go
```

## 命令行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `-api-url` | `http://localhost:8080` | ProxyFlow API 服务器地址 |
| `-username` | 从环境变量获取 | 管理员用户名 |
| `-password` | 从环境变量获取 | 管理员密码 |
| `-file` | 空 | JSON 文件路径（可选） |
| `-proxy-type` | `http` | 代理类型 (http, https, socks5) |
| `-fetch-api` | `false` | 强制从 API 获取 |

## API 响应格式

Webshare API 返回的数据格式：

```json
{
  "count": 100,
  "next": "https://proxy.webshare.io/api/v2/proxy/list/?page=2",
  "previous": null,
  "results": [
    {
      "id": "d-10513",
      "username": "username",
      "password": "password",
      "proxy_address": "*******",
      "port": 8168,
      "valid": true,
      "last_verification": "2019-06-09T23:34:00.095501-07:00",
      "country_code": "US",
      "city_name": "New York",
      "asn_name": "Example ASN",
      "asn_number": 12345,
      "high_country_confidence": true,
      "created_at": "2022-06-14T11:58:10.246406-07:00"
    }
  ]
}
```

## 工作流程

1. **认证检查**: 检查 SUPER_API_KEY 或用户名密码
2. **数据获取**: 
   - 如果指定 `-fetch-api` 或没有文件路径，从 API 获取
   - 否则从指定的 JSON 文件读取
3. **API 分页**: 自动处理所有分页，获取完整的代理列表
4. **数据转换**: 将 Webshare 格式转换为 ProxyFlow 格式
5. **批量导入**: 优先使用批量导入 API，失败时回退到单个导入
6. **结果报告**: 显示导入成功和失败的统计信息

## 错误处理

- **API Token 无效**: 检查 WEBSHARE_API_TOKEN 是否正确
- **网络错误**: 检查网络连接和 API 服务状态
- **认证失败**: 检查 ProxyFlow 的认证配置
- **数据格式错误**: 检查 JSON 文件格式是否正确

## 日志示例

```
2025/01/23 10:30:00 Fetching proxies from Webshare API...
2025/01/23 10:30:01 Fetched page 1: 100 proxies (total so far: 100)
2025/01/23 10:30:02 Fetched page 2: 50 proxies (total so far: 150)
2025/01/23 10:30:02 Total proxies fetched from API: 150
2025/01/23 10:30:02 Found 150 proxies to import.
2025/01/23 10:30:03 Batch import completed. Success: 150, Failed: 0
2025/01/23 10:30:03 Proxy import completed successfully.
```

## 注意事项

1. **API 限制**: Webshare API 可能有速率限制，工具已添加适当的延迟
2. **大量数据**: 如果代理数量很大，导入过程可能需要一些时间
3. **重复导入**: ProxyFlow 会自动处理重复的代理，不会重复添加
4. **网络超时**: 每个 API 请求有 30 秒超时限制
