package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// WebshareProxy defines the structure for a proxy entry from webshare API
type WebshareProxy struct {
	ID               string    `json:"id"`
	Username         string    `json:"username"`
	Password         string    `json:"password"`
	ProxyAddress     string    `json:"proxy_address"`
	Port             int       `json:"port"`
	Valid            bool      `json:"valid"`
	LastVerification time.Time `json:"last_verification"`
	CountryCode      string    `json:"country_code"`
	CityName         string    `json:"city_name"`
	ASNName          string    `json:"asn_name"`
	ASNNumber        int       `json:"asn_number"`
	HighCountryConf  bool      `json:"high_country_confidence"`
	CreatedAt        time.Time `json:"created_at"`
}

// WebshareAPIResponse defines the structure of the API response from webshare
type WebshareAPIResponse struct {
	Count    int             `json:"count"`
	Next     *string         `json:"next"`
	Previous *string         `json:"previous"`
	Results  []WebshareProxy `json:"results"`
}

// WebshareProxyFile defines the structure of the file from webshare (for backward compatibility)
type WebshareProxyFile struct {
	Results []WebshareProxy `json:"results"`
}

// ProxyRequest is the payload for adding a proxy to the manager
// This should match the backend models.ProxyRequest structure exactly
type ProxyRequest struct {
	Host                  string   `json:"host"`
	Port                  int      `json:"port"`
	Type                  string   `json:"type"`
	Username              string   `json:"username,omitempty"`
	Password              string   `json:"password,omitempty"`
	Weight                int      `json:"weight,omitempty"`
	CountryCode           string   `json:"country_code,omitempty"`
	CityName              string   `json:"city_name,omitempty"`
	ASNName               string   `json:"asn_name,omitempty"`
	ASNNumber             int      `json:"asn_number,omitempty"`
	HighCountryConfidence bool     `json:"high_country_confidence,omitempty"`
	AnonymityLevel        string   `json:"anonymity_level,omitempty"`
	Scenario              string   `json:"scenario,omitempty"`
	Priority              int      `json:"priority,omitempty"`
	TagIDs                []string `json:"tag_ids,omitempty"`
}

// LoginRequest is the payload for the login endpoint
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// LoginResponse is the response from the login endpoint
type LoginResponse struct {
	Token string `json:"token"`
}

func main() {
	// 优先加载根目录 .env 以获取 SUPER_API_KEY
	_ = godotenv.Load(".env")

	apiURL := flag.String("api-url", "http://localhost:8080", "API server URL")
	username := flag.String("username", "", "Admin username. Can also be set via PROXY_ADMIN_USER env var.")
	password := flag.String("password", "", "Admin password. Can also be set via PROXY_ADMIN_PASS env var.")
	filePath := flag.String("file", "", "Path to webshare_proxy.json (optional, will fetch from API if not provided)")
	proxyType := flag.String("proxy-type", "http", "Proxy type to import (http, https, socks5)")
	fetchFromAPI := flag.Bool("fetch-api", false, "Fetch proxies directly from Webshare API")
	flag.Parse()

	// 尝试两种环境变量名以保持向后兼容
	superApiKey := os.Getenv("PROXY_MANAGER_SUPER_API_KEY")
	if superApiKey == "" {
		superApiKey = os.Getenv("SUPER_API_KEY") // 向后兼容
	}

	var token string
	var err error
	var proxies []WebshareProxy

	// 如果指定从API获取或者没有提供文件路径，则从API获取
	if *fetchFromAPI || *filePath == "" {
		webshareToken := os.Getenv("WEBSHARE_API_TOKEN")
		if webshareToken == "" {
			log.Fatal("WEBSHARE_API_TOKEN is required when fetching from API. Please set it in .env file.")
		}

		log.Println("Fetching proxies from Webshare API...")
		proxies, err = fetchProxiesFromAPI(webshareToken)
		if err != nil {
			log.Fatalf("Failed to fetch proxies from API: %v", err)
		}
		log.Printf("Successfully fetched %d proxies from API", len(proxies))
	} else {
		// 从文件读取代理
		log.Printf("Reading proxies from file: %s", *filePath)
		proxies, err = readProxiesFromFile(*filePath)
		if err != nil {
			log.Fatalf("Failed to read proxies from file: %v", err)
		}
		log.Printf("Successfully read %d proxies from file", len(proxies))
	}

	if superApiKey != "" {
		log.Println("Using SUPER_API_KEY for authentication.")
		err = importProxiesToManager(*apiURL, "", superApiKey, true, proxies, *proxyType)
	} else {
		// 如果命令行参数未提供，则从环境变量中获取
		if *username == "" {
			*username = os.Getenv("PROXY_ADMIN_USER")
		}
		if *password == "" {
			*password = os.Getenv("PROXY_ADMIN_PASS")
		}

		if *username == "" || *password == "" {
			log.Fatal("Username and password are required. Provide them via flags, .env file, or set PROXY_MANAGER_SUPER_API_KEY.")
		}

		token, err = login(*apiURL, *username, *password)
		if err != nil {
			log.Fatalf("Login failed: %v", err)
		}
		log.Println("Login successful.")

		err = importProxiesToManager(*apiURL, token, "", false, proxies, *proxyType)
	}
	if err != nil {
		log.Fatalf("Import failed: %v", err)
	}

	log.Println("Proxy import completed successfully.")
}

func login(apiURL, username, password string) (string, error) {
	loginURL := fmt.Sprintf("%s/api/v1/auth/login", apiURL)
	loginReq := LoginRequest{Username: username, Password: password}
	reqBody, err := json.Marshal(loginReq)
	if err != nil {
		return "", fmt.Errorf("could not marshal login request: %w", err)
	}

	resp, err := http.Post(loginURL, "application/json", bytes.NewBuffer(reqBody))
	if err != nil {
		return "", fmt.Errorf("could not send login request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("login failed with status %d: %s", resp.StatusCode, string(body))
	}

	var loginResp LoginResponse
	if err := json.NewDecoder(resp.Body).Decode(&loginResp); err != nil {
		return "", fmt.Errorf("could not decode login response: %w", err)
	}

	if loginResp.Token == "" {
		return "", fmt.Errorf("token not found in login response")
	}

	return loginResp.Token, nil
}

// fetchProxiesFromAPI fetches all proxies from Webshare API with pagination
func fetchProxiesFromAPI(token string) ([]WebshareProxy, error) {
	var allProxies []WebshareProxy
	page := 1
	pageSize := 100 // 使用较大的页面大小以减少请求次数

	for {
		url := fmt.Sprintf("https://proxy.webshare.io/api/v2/proxy/list/?mode=direct&page=%d&page_size=%d", page, pageSize)

		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			return nil, fmt.Errorf("could not create request: %w", err)
		}

		req.Header.Set("Authorization", "Token "+token)
		req.Header.Set("Content-Type", "application/json")

		client := &http.Client{Timeout: 30 * time.Second}
		resp, err := client.Do(req)
		if err != nil {
			return nil, fmt.Errorf("could not send request: %w", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
		}

		var apiResp WebshareAPIResponse
		if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
			return nil, fmt.Errorf("could not decode API response: %w", err)
		}

		// 添加当前页的代理到总列表
		allProxies = append(allProxies, apiResp.Results...)

		log.Printf("Fetched page %d: %d proxies (total so far: %d)", page, len(apiResp.Results), len(allProxies))

		// 如果没有下一页，退出循环
		if apiResp.Next == nil {
			break
		}

		page++

		// 添加小延迟以避免过于频繁的请求
		time.Sleep(100 * time.Millisecond)
	}

	log.Printf("Total proxies fetched from API: %d", len(allProxies))
	return allProxies, nil
}

// readProxiesFromFile reads proxies from a JSON file
func readProxiesFromFile(filePath string) ([]WebshareProxy, error) {
	fileContent, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("could not read file %s: %w", filePath, err)
	}

	var webshareFile WebshareProxyFile
	var proxies []WebshareProxy

	// Try parsing as {"results": [...]} first
	if err := json.Unmarshal(fileContent, &webshareFile); err == nil && len(webshareFile.Results) > 0 {
		proxies = webshareFile.Results
	} else {
		// If that fails, try parsing as a simple array [...]
		if err := json.Unmarshal(fileContent, &proxies); err != nil {
			return nil, fmt.Errorf("could not parse json file: %w", err)
		}
	}

	return proxies, nil
}

func importProxiesToManager(apiURL, token, superApiKey string, useApiKey bool, proxies []WebshareProxy, proxyType string) error {
	if len(proxies) == 0 {
		log.Println("No proxies to import.")
		return nil
	}
	log.Printf("Found %d proxies to import.", len(proxies))

	// 构造 ProxyRequest 数组
	var proxyReqs []ProxyRequest
	for i, p := range proxies {
		var host string
		var port int
		var err error

		if p.ProxyAddress != "" && p.Port != 0 {
			host = p.ProxyAddress
			port = p.Port
		} else if p.ProxyAddress != "" {
			parts := strings.Split(p.ProxyAddress, ":")
			if len(parts) == 2 {
				host = parts[0]
				port, err = strconv.Atoi(parts[1])
				if err != nil {
					log.Printf("Skipping proxy %d: Invalid port in proxy_address '%s'", i+1, p.ProxyAddress)
					continue
				}
			} else {
				log.Printf("Skipping proxy %d: Invalid proxy_address format '%s'", i+1, p.ProxyAddress)
				continue
			}
		} else {
			log.Printf("Skipping proxy %d: missing proxy address or port", i+1)
			continue
		}

		proxyReqs = append(proxyReqs, ProxyRequest{
			Host:                  host,
			Port:                  port,
			Type:                  proxyType,
			Username:              p.Username,
			Password:              p.Password,
			Weight:                1, // 默认权重为1
			CountryCode:           p.CountryCode,
			CityName:              p.CityName,
			ASNName:               p.ASNName,
			ASNNumber:             p.ASNNumber,
			HighCountryConfidence: p.HighCountryConf,
			AnonymityLevel:        "unknown", // 默认设置为 unknown
			Scenario:              "",        // 可以根据需要设置场景
			Priority:              1,         // 默认优先级为1
			TagIDs:                nil,       // 暂时不设置标签
		})
	}

	if len(proxyReqs) == 0 {
		log.Println("No valid proxies to import.")
		return nil
	}

	// 优先尝试批量导入
	batchURL := fmt.Sprintf("%s/api/v1/proxies/batch-import", apiURL)
	batchBody, err := json.Marshal(proxyReqs)
	if err != nil {
		return fmt.Errorf("could not marshal batch import request: %w", err)
	}

	req, err := http.NewRequest("POST", batchURL, bytes.NewBuffer(batchBody))
	if err != nil {
		return fmt.Errorf("could not create batch import request: %w", err)
	}
	if useApiKey {
		req.Header.Set("X-API-Key", superApiKey)
	} else {
		req.Header.Set("Authorization", "Bearer "+token)
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Batch import request failed: %v, fallback to single import", err)
	} else if resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		resp.Body.Close()

		var result map[string]interface{}
		if err := json.Unmarshal(body, &result); err != nil {
			log.Printf("Failed to parse batch import response: %v, fallback to single import", err)
		} else {
			log.Printf("Batch import response: %s", string(body))

			// 检查是否有成功导入的代理
			successCount := 0
			failedCount := 0

			if success, ok := result["success"]; ok {
				if successFloat, ok := success.(float64); ok {
					successCount = int(successFloat)
				}
			}

			if failed, ok := result["failed"]; ok {
				if failedFloat, ok := failed.(float64); ok {
					failedCount = int(failedFloat)
				}
			}

			log.Printf("Batch import completed. Success: %d, Failed: %d", successCount, failedCount)

			// 显示详细错误信息
			if errs, ok := result["errors"].([]interface{}); ok && len(errs) > 0 {
				log.Printf("Batch import errors:")
				for i, e := range errs {
					if errStr, ok := e.(string); ok && errStr != "" {
						log.Printf("- Error %d: %s", i+1, errStr)
					} else if e != nil {
						log.Printf("- Error %d: %v", i+1, e)
					}
				}
			}

			// 如果有部分成功，我们认为批量导入基本成功
			if successCount > 0 {
				return nil
			}

			// 如果全部失败，回退到单个导入
			log.Printf("All proxies failed in batch import, fallback to single import")
		}
	} else if resp != nil {
		body, _ := io.ReadAll(resp.Body)
		log.Printf("Batch import failed (status %d), fallback to single import. Response: %s", resp.StatusCode, string(body))
		resp.Body.Close()
	}

	// 回退为逐条导入
	log.Println("Falling back to single proxy import...")
	addProxyURL := fmt.Sprintf("%s/api/v1/proxies", apiURL)
	for i, proxyReq := range proxyReqs {
		reqBody, err := json.Marshal(proxyReq)
		if err != nil {
			log.Printf("Skipping proxy %d: could not marshal request: %v", i+1, err)
			continue
		}

		// 调试：打印单个代理请求数据
		log.Printf("Single proxy %d request body: %s", i+1, string(reqBody))

		req, err := http.NewRequest("POST", addProxyURL, bytes.NewBuffer(reqBody))
		if err != nil {
			log.Printf("Skipping proxy %d: could not create request: %v", i+1, err)
			continue
		}

		if useApiKey {
			req.Header.Set("X-API-Key", superApiKey)
		} else {
			req.Header.Set("Authorization", "Bearer "+token)
		}
		req.Header.Set("Content-Type", "application/json")

		resp, err := client.Do(req)
		if err != nil {
			log.Printf("Skipping proxy %d: request failed: %v", i+1, err)
			continue
		}

		if resp.StatusCode == http.StatusCreated || resp.StatusCode == http.StatusOK {
			log.Printf("Successfully imported proxy %d/%d: %s:%d", i+1, len(proxyReqs), proxyReq.Host, proxyReq.Port)
		} else {
			body, _ := io.ReadAll(resp.Body)
			log.Printf("Failed to import proxy %d/%d: %s:%d. Status: %d, Response: %s", i+1, len(proxyReqs), proxyReq.Host, proxyReq.Port, resp.StatusCode, string(body))
		}
		resp.Body.Close()
	}

	return nil
}
