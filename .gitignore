# 二进制文件
*.exe
*.exe~
*.dll
*.so
*.dylib
proxyFlow
build/

# 测试文件
*.test
*.out
coverage.out
coverage.html

# 日志文件
*.log
logs/

# 配置文件（包含敏感信息）
config/config.yaml

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# OS文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 依赖目录
vendor/

# 临时文件
tmp/
temp/

# Docker
.dockerignore

# 环境变量文件
.env
.env.local
.env.*.local

# 监控数据
monitoring/data/ 
node_modules
freeProxy/proxy_sources_cache.json
freeProxy/unified_proxy_cache.json
freeProxy/verification_progress.json
main