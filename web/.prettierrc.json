{"$schema": "https://json.schemastore.org/prettierrc", "printWidth": 100, "tabWidth": 2, "useTabs": false, "semi": false, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": false, "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "proseWrap": "preserve", "insertPragma": false, "requirePragma": false, "rangeStart": 0, "rangeEnd": 100}