# Dependencies
node_modules/
.pnpm-store/

# Build outputs
build/
dist/
.next/
out/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Vite cache
.vite

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Package manager lock files (optional - uncomment if you want to format them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Generated files
*.min.js
*.min.css

# Documentation
*.md

# Configuration files that should maintain their formatting
.gitignore
.dockerignore
Dockerfile*
docker-compose*.yml
*.config.js
*.config.ts
vite.config.*
tailwind.config.*
postcss.config.*

# Public assets
public/
static/

# Auto-generated type definitions
*.d.ts
vite-env.d.ts

# Migration scripts
migrate-to-vite.sh
