<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 外圈 -->
  <circle cx="16" cy="16" r="15" stroke="url(#gradient1)" stroke-width="2" fill="none"/>
  
  <!-- 内部网络节点 -->
  <circle cx="16" cy="8" r="2" fill="url(#gradient2)"/>
  <circle cx="8" cy="16" r="2" fill="url(#gradient2)"/>
  <circle cx="24" cy="16" r="2" fill="url(#gradient2)"/>
  <circle cx="16" cy="24" r="2" fill="url(#gradient2)"/>
  
  <!-- 中心节点 -->
  <circle cx="16" cy="16" r="3" fill="url(#gradient3)"/>
  
  <!-- 连接线 -->
  <line x1="16" y1="11" x2="16" y2="13" stroke="url(#gradient1)" stroke-width="1.5" opacity="0.8"/>
  <line x1="16" y1="19" x2="16" y2="21" stroke="url(#gradient1)" stroke-width="1.5" opacity="0.8"/>
  <line x1="11" y1="16" x2="13" y2="16" stroke="url(#gradient1)" stroke-width="1.5" opacity="0.8"/>
  <line x1="19" y1="16" x2="21" y2="16" stroke="url(#gradient1)" stroke-width="1.5" opacity="0.8"/>
  
  <!-- 数据流动效果 -->
  <circle cx="16" cy="10" r="0.5" fill="#60A5FA" opacity="0.6">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="12" cy="16" r="0.5" fill="#60A5FA" opacity="0.4">
    <animate attributeName="opacity" values="0.4;1;0.4" dur="2s" begin="0.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="20" cy="16" r="0.5" fill="#60A5FA" opacity="0.4">
    <animate attributeName="opacity" values="0.4;1;0.4" dur="2s" begin="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="16" cy="22" r="0.5" fill="#60A5FA" opacity="0.6">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" begin="1.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#60A5FA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
