<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆 -->
  <circle cx="16" cy="16" r="16" fill="url(#bgGradient)"/>
  
  <!-- 外圈 -->
  <circle cx="16" cy="16" r="13" stroke="#ffffff" stroke-width="1.5" fill="none" opacity="0.8"/>
  
  <!-- 网络节点 -->
  <circle cx="16" cy="7" r="1.5" fill="#ffffff"/>
  <circle cx="9" cy="16" r="1.5" fill="#ffffff"/>
  <circle cx="23" cy="16" r="1.5" fill="#ffffff"/>
  <circle cx="16" cy="25" r="1.5" fill="#ffffff"/>
  
  <!-- 中心节点 -->
  <circle cx="16" cy="16" r="2.5" fill="#ffffff"/>
  
  <!-- 连接线 -->
  <line x1="16" y1="9.5" x2="16" y2="13.5" stroke="#ffffff" stroke-width="1.2" opacity="0.9"/>
  <line x1="16" y1="18.5" x2="16" y2="22.5" stroke="#ffffff" stroke-width="1.2" opacity="0.9"/>
  <line x1="11.5" y1="16" x2="13.5" y2="16" stroke="#ffffff" stroke-width="1.2" opacity="0.9"/>
  <line x1="18.5" y1="16" x2="20.5" y2="16" stroke="#ffffff" stroke-width="1.2" opacity="0.9"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1D4ED8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
