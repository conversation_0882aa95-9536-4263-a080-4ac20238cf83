import { tagAPI } from './api'
import {
  apiCall,
  batchApiCall,
  cachedApiCall,
  clearApiCacheBatch,
  typeSafeApiCall,
} from '../utils/apiWrapper'
import { validateProxyTagRequest } from '../utils/validation'
import type { ProxyTag, ProxyTagRequest, ProxyTagAssignRequest } from '../types'

export class TagService {
  // 缓存键前缀
  private static readonly CACHE_PREFIX = 'tag_service'

  /**
   * 获取所有标签
   */
  static async getTags(useCache = true): Promise<ProxyTag[]> {
    const cacheKey = `${this.CACHE_PREFIX}_all_tags`

    const result = useCache
      ? await cachedApiCall(cacheKey, () => tagAPI.getTags(), {
          ttl: 5 * 60 * 1000, // 5分钟缓存
          showErrorToast: true,
          customErrorMessage: '获取标签列表失败',
        })
      : await apiCall(() => tagAPI.getTags(), {
          showErrorToast: true,
          customErrorMessage: '获取标签列表失败',
        })

    return result.success ? result.data || [] : []
  }

  /**
   * 获取单个标签
   */
  static async getTag(tagId: string): Promise<ProxyTag | null> {
    const result = await apiCall(() => tagAPI.getTag(tagId), {
      showErrorToast: true,
      customErrorMessage: '获取标签详情失败',
    })

    return result.success ? result.data || null : null
  }

  /**
   * 创建标签
   */
  static async createTag(tagData: ProxyTagRequest): Promise<ProxyTag | null> {
    const result = await typeSafeApiCall(tagAPI.createTag, tagData, {
      showLoading: true,
      loadingMessage: '创建标签中...',
      showSuccessToast: true,
      successMessage: '标签创建成功',
      customErrorMessage: '创建标签失败',
      validateRequest: true,
      requestValidator: (data) => {
        const validation = validateProxyTagRequest(data)
        return {
          valid: validation.valid,
          errors: Object.values(validation.errors).flat(),
        }
      },
    })

    if (result.success) {
      // 清除标签缓存
      this.clearTagCache()
    }

    return result.success ? result.data || null : null
  }

  /**
   * 更新标签
   */
  static async updateTag(tagId: string, tagData: ProxyTagRequest): Promise<ProxyTag | null> {
    const result = await apiCall(() => tagAPI.updateTag(tagId, tagData), {
      showLoading: true,
      loadingMessage: '更新标签中...',
      showSuccessToast: true,
      successMessage: '标签更新成功',
      customErrorMessage: '更新标签失败',
    })

    if (result.success) {
      // 清除标签缓存
      this.clearTagCache()
    }

    return result.success ? result.data || null : null
  }

  /**
   * 删除标签
   */
  static async deleteTag(tagId: string): Promise<boolean> {
    const result = await apiCall(() => tagAPI.deleteTag(tagId), {
      showLoading: true,
      loadingMessage: '删除标签中...',
      showSuccessToast: true,
      successMessage: '标签删除成功',
      customErrorMessage: '删除标签失败',
    })

    if (result.success) {
      // 清除标签缓存
      this.clearTagCache()
    }

    return result.success
  }

  /**
   * 为代理分配标签
   */
  static async assignTags(assignData: ProxyTagAssignRequest): Promise<boolean> {
    const { proxy_ids, tag_ids } = assignData

    const result = await apiCall(() => tagAPI.assignTags(assignData), {
      showLoading: true,
      loadingMessage: `为 ${proxy_ids.length} 个代理分配 ${tag_ids.length} 个标签中...`,
      showSuccessToast: true,
      successMessage: '标签分配成功',
      customErrorMessage: '分配标签失败',
    })

    return result.success
  }

  /**
   * 批量创建标签
   */
  static async batchCreateTags(tagsData: ProxyTagRequest[]): Promise<{
    results: Array<{ success: boolean; tag?: ProxyTag; error?: string; name: string }>
    summary: { total: number; success: number; failed: number }
  }> {
    const apiCalls = tagsData.map((tagData) => ({
      name: tagData.name,
      apiFunction: () => tagAPI.createTag(tagData),
      options: {
        showErrorToast: false, // 批量操作时不显示单个错误
        showSuccessToast: false,
      },
    }))

    const { results, summary } = await batchApiCall(apiCalls, {
      showProgress: true,
      showSummary: true,
      continueOnError: true,
    })

    if (summary.success > 0) {
      // 清除标签缓存
      this.clearTagCache()
    }

    return {
      results: results.map((result) => ({
        success: result.success,
        tag: result.data,
        error: result.error?.message,
        name: result.name,
      })),
      summary,
    }
  }

  /**
   * 批量删除标签
   */
  static async batchDeleteTags(tagIds: string[]): Promise<{
    results: Array<{ success: boolean; error?: string; tagId: string }>
    summary: { total: number; success: number; failed: number }
  }> {
    const apiCalls = tagIds.map((tagId) => ({
      name: tagId,
      apiFunction: () => tagAPI.deleteTag(tagId),
      options: {
        showErrorToast: false,
        showSuccessToast: false,
      },
    }))

    const { results, summary } = await batchApiCall(apiCalls, {
      showProgress: true,
      showSummary: true,
      continueOnError: true,
    })

    if (summary.success > 0) {
      // 清除标签缓存
      this.clearTagCache()
    }

    return {
      results: results.map((result) => ({
        success: result.success,
        error: result.error?.message,
        tagId: result.name,
      })),
      summary,
    }
  }

  /**
   * 验证标签数据
   */
  static validateTagData(tagData: ProxyTagRequest): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // 验证标签名称
    if (!tagData.name || tagData.name.trim().length === 0) {
      errors.push('标签名称不能为空')
    } else if (tagData.name.length > 50) {
      errors.push('标签名称不能超过50个字符')
    } else if (!/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/.test(tagData.name)) {
      errors.push('标签名称只能包含字母、数字、中文、下划线和连字符')
    }

    // 验证描述
    if (tagData.description && tagData.description.length > 200) {
      errors.push('标签描述不能超过200个字符')
    }

    // 验证颜色
    if (tagData.color && !/^#[0-9A-Fa-f]{6}$/.test(tagData.color)) {
      errors.push('颜色格式不正确，请使用十六进制格式（如 #FF0000）')
    }

    return {
      valid: errors.length === 0,
      errors,
    }
  }

  /**
   * 生成随机颜色
   */
  static generateRandomColor(): string {
    const colors = [
      '#3B82F6', // blue
      '#10B981', // green
      '#F59E0B', // yellow
      '#EF4444', // red
      '#8B5CF6', // purple
      '#06B6D4', // cyan
      '#84CC16', // lime
      '#F97316', // orange
      '#EC4899', // pink
      '#6B7280', // gray
    ]

    return colors[Math.floor(Math.random() * colors.length)]
  }

  /**
   * 格式化标签显示
   */
  static formatTagDisplay(tag: ProxyTag): string {
    return tag.description ? `${tag.name} (${tag.description})` : tag.name
  }

  /**
   * 按颜色分组标签
   */
  static groupTagsByColor(tags: ProxyTag[]): Record<string, ProxyTag[]> {
    return tags.reduce(
      (groups, tag) => {
        const color = tag.color || '#6B7280'
        if (!groups[color]) {
          groups[color] = []
        }
        groups[color].push(tag)
        return groups
      },
      {} as Record<string, ProxyTag[]>
    )
  }

  /**
   * 搜索标签
   */
  static searchTags(tags: ProxyTag[], searchTerm: string): ProxyTag[] {
    if (!searchTerm.trim()) {
      return tags
    }

    const term = searchTerm.toLowerCase().trim()
    return tags.filter(
      (tag) =>
        tag.name.toLowerCase().includes(term) ||
        (tag.description && tag.description.toLowerCase().includes(term))
    )
  }

  /**
   * 排序标签
   */
  static sortTags(tags: ProxyTag[], sortBy: 'name' | 'created_at' = 'name'): ProxyTag[] {
    return [...tags].sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'created_at':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        default:
          return 0
      }
    })
  }

  /**
   * 清除标签缓存
   */
  static clearTagCache(): void {
    const cacheKeys = [`${this.CACHE_PREFIX}_all_tags`]

    clearApiCacheBatch(cacheKeys)
  }
}