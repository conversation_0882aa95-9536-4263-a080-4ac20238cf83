import { APIKeyInfo, APIKeyResponse, CreateAPIKeyRequest } from '../types'
import { apiKeyAPI } from './api'
import { apiCall } from '../utils/apiWrapper'
import { enhancedToast, specialToasts } from '../utils/toastConfig'

export class APIKeyService {
  /**
   * 获取所有API Key列表
   */
  static async getAPIKeys(): Promise<APIKeyInfo[]> {
    const result = await apiCall(() => apiKeyAPI.getAPIKeys(), {
      showErrorToast: true,
      customErrorMessage: '获取API Key列表失败',
    })

    return result.success ? result.data || [] : []
  }

  /**
   * 获取当前用户的 API Key 信息（兼容旧版本）
   */
  static async getAPIKeyInfo(): Promise<APIKeyInfo | null> {
    const result = await apiCall(() => apiKeyAPI.getAPIKey(), {
      showErrorToast: true,
      customErrorMessage: '获取API Key信息失败',
    })

    return result.success ? result.data || null : null
  }

  /**
   * 创建新的API Key
   */
  static async createAPIKey(data: CreateAPIKeyRequest): Promise<APIKeyResponse | null> {
    const result = await apiCall(() => apiKeyAPI.createAPIKey(data), {
      showLoading: true,
      loadingMessage: '创建API Key中...',
      showSuccessToast: true,
      successMessage: 'API Key创建成功',
      customErrorMessage: '创建API Key失败',
    })

    return result.success ? result.data || null : null
  }

  /**
   * 更新API Key
   */
  static async updateAPIKey(
    keyId: string,
    data: { name?: string; is_active?: boolean }
  ): Promise<APIKeyInfo | null> {
    const result = await apiCall(() => apiKeyAPI.updateAPIKey(keyId, data), {
      showLoading: true,
      loadingMessage: '更新API Key中...',
      showSuccessToast: true,
      successMessage: 'API Key更新成功',
      customErrorMessage: '更新API Key失败',
    })

    return result.success ? result.data || null : null
  }

  /**
   * 删除特定API Key
   */
  static async deleteAPIKey(keyId: string): Promise<boolean> {
    const result = await apiCall(() => apiKeyAPI.deleteAPIKey(keyId), {
      showLoading: true,
      loadingMessage: '删除API Key中...',
      showSuccessToast: true,
      successMessage: 'API Key删除成功',
      customErrorMessage: '删除API Key失败',
    })

    return result.success
  }

  /**
   * 复制文本到剪贴板
   */
  static async copyToClipboard(text: string): Promise<boolean> {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text)
        specialToasts.copySuccess()
        return true
      } else {
        // 降级方案：使用 document.execCommand
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        const result = document.execCommand('copy')
        document.body.removeChild(textArea)

        if (result) {
          specialToasts.copySuccess()
        } else {
          enhancedToast.error('复制失败，请手动复制')
        }

        return result
      }
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
      enhancedToast.error('复制失败，请手动复制')
      return false
    }
  }

  /**
   * 格式化日期时间
   */
  static formatDateTime(dateString: string): string {
    if (!dateString || dateString === '0001-01-01T00:00:00Z') {
      return 'Never'
    }

    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffDays === 0) {
      return 'Today'
    } else if (diffDays === 1) {
      return 'Yesterday'
    } else if (diffDays < 7) {
      return `${diffDays} days ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  /**
   * 格式化使用次数
   */
  static formatUsageCount(count: number): string {
    if (count === 0) {
      return 'Never used'
    } else if (count === 1) {
      return '1 time'
    } else if (count < 1000) {
      return `${count} times`
    } else if (count < 1000000) {
      return `${(count / 1000).toFixed(1)}K times`
    } else {
      return `${(count / 1000000).toFixed(1)}M times`
    }
  }
}