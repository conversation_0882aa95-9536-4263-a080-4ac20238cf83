import { proxyAPI } from './api'
import {
  apiCall,
  cachedApiCall,
  apiCallConfigs,
  clearApiCacheBatch,
  typeSafeApiCall,
} from '../utils/apiWrapper'
import { validateProxyFormData, validateBatchImportProxiesRequest } from '../utils/validation'
import type {
  Proxy,
  ProxyFormData,
  ProxyStats,
  ProxyLocationStats,
  ProxyGroupByLocation,
  QualityScore,
  ProxyTag,
  ProxyFilterByTags,
  BatchImportProxiesRequest,
} from '../types'

export class ProxyService {
  // 缓存键前缀
  private static readonly CACHE_PREFIX = 'proxy_service'

  /**
   * 获取代理列表
   */
  static async getProxies(useCache = false): Promise<Proxy[]> {
    const cacheKey = `${this.CACHE_PREFIX}_proxies`

    const result = useCache
      ? await cachedApiCall(cacheKey, () => proxyAPI.getProxies(), {
          ttl: 30 * 1000, // 30秒缓存
          showErrorToast: true,
          customErrorMessage: '获取代理列表失败',
        })
      : await apiCall(() => proxyAPI.getProxies(), {
          showErrorToast: true,
          customErrorMessage: '获取代理列表失败',
        })

    return result.success ? result.data || [] : []
  }

  /**
   * 获取可用代理
   */
  static async getAvailableProxies(): Promise<Proxy[]> {
    const result = await apiCall(() => proxyAPI.getAvailableProxies(), {
      showErrorToast: true,
      customErrorMessage: '获取可用代理失败',
    })

    return result.success ? result.data || [] : []
  }

  /**
   * 获取单个代理详情
   */
  static async getProxy(proxyId: string): Promise<Proxy | null> {
    const result = await apiCall(() => proxyAPI.getProxy(proxyId), {
      showErrorToast: true,
      customErrorMessage: '获取代理详情失败',
    })

    return result.success ? result.data || null : null
  }

  /**
   * 添加代理
   */
  static async addProxy(proxyData: ProxyFormData): Promise<Proxy | null> {
    const result = await typeSafeApiCall(proxyAPI.addProxy, proxyData, {
      ...apiCallConfigs.create,
      validateRequest: true,
      requestValidator: (data) => {
        const validation = validateProxyFormData(data)
        return {
          valid: validation.valid,
          errors: Object.values(validation.errors).flat(),
        }
      },
    })

    if (result.success) {
      // 清除代理列表缓存
      this.clearProxyCache()
    }

    return result.success ? result.data || null : null
  }

  /**
   * 删除代理
   */
  static async deleteProxy(proxyId: string): Promise<boolean> {
    const result = await apiCall(() => proxyAPI.deleteProxy(proxyId), apiCallConfigs.delete)

    if (result.success) {
      // 清除代理列表缓存
      this.clearProxyCache()
    }

    return result.success
  }

  /**
   * 批量导入代理
   */
  static async batchImportProxies(proxies: ProxyFormData[]): Promise<boolean> {
    const request: BatchImportProxiesRequest = { proxies }

    const result = await typeSafeApiCall(proxyAPI.batchImportProxies, request, {
      showLoading: true,
      loadingMessage: `导入 ${proxies.length} 个代理中...`,
      showSuccessToast: true,
      successMessage: `成功导入 ${proxies.length} 个代理`,
      customErrorMessage: '批量导入代理失败',
      validateRequest: true,
      requestValidator: (data) => {
        const validation = validateBatchImportProxiesRequest(data)
        return {
          valid: validation.valid,
          errors: Object.values(validation.errors).flat(),
        }
      },
    })

    if (result.success) {
      // 清除代理列表缓存
      this.clearProxyCache()
    }

    return result.success
  }

  /**
   * 获取代理统计
   */
  static async getProxyStats(): Promise<ProxyStats | null> {
    const cacheKey = `${this.CACHE_PREFIX}_stats`

    const result = await cachedApiCall(cacheKey, () => proxyAPI.getProxyStats(), {
      ttl: 60 * 1000, // 1分钟缓存
      showErrorToast: true,
      customErrorMessage: '获取代理统计失败',
    })

    return result.success ? result.data || null : null
  }

  /**
   * 健康检查单个代理
   */
  static async healthCheck(proxyId: string): Promise<boolean> {
    const result = await apiCall(() => proxyAPI.healthCheck(proxyId), {
      showLoading: true,
      loadingMessage: '健康检查中...',
      showSuccessToast: true,
      successMessage: '健康检查已启动',
      customErrorMessage: '健康检查失败',
    })

    return result.success
  }

  /**
   * 批量健康检查
   */
  static async batchHealthCheck(proxyIds: string[]): Promise<boolean> {
    const result = await apiCall(() => proxyAPI.batchHealthCheck(proxyIds), {
      showLoading: true,
      loadingMessage: `检查 ${proxyIds.length} 个代理中...`,
      showSuccessToast: true,
      successMessage: `已启动 ${proxyIds.length} 个代理的健康检查`,
      customErrorMessage: '批量健康检查失败',
    })

    return result.success
  }

  /**
   * 检查所有代理
   */
  static async healthCheckAll(): Promise<boolean> {
    const result = await apiCall(() => proxyAPI.healthCheckAll(), {
      showLoading: true,
      loadingMessage: '检查所有代理中...',
      showSuccessToast: true,
      successMessage: '已启动所有代理的健康检查',
      customErrorMessage: '全量健康检查失败',
    })

    return result.success
  }

  /**
   * 地理位置相关方法
   */
  static async getProxyLocationStats(): Promise<ProxyLocationStats | null> {
    const cacheKey = `${this.CACHE_PREFIX}_location_stats`

    const result = await cachedApiCall(cacheKey, () => proxyAPI.getProxyLocationStats(), {
      ttl: 2 * 60 * 1000, // 2分钟缓存
      showErrorToast: true,
      customErrorMessage: '获取地理位置统计失败',
    })

    return result.success ? result.data || null : null
  }

  static async groupProxiesByLocation(
    groupBy: 'country' | 'city' = 'country'
  ): Promise<ProxyGroupByLocation[]> {
    const cacheKey = `${this.CACHE_PREFIX}_location_groups_${groupBy}`

    const result = await cachedApiCall(cacheKey, () => proxyAPI.groupProxiesByLocation(groupBy), {
      ttl: 2 * 60 * 1000, // 2分钟缓存
      showErrorToast: true,
      customErrorMessage: '获取地理位置分组失败',
    })

    return result.success ? result.data || [] : []
  }

  static async getProxiesByLocation(countryCode?: string, cityName?: string): Promise<Proxy[]> {
    const result = await apiCall(() => proxyAPI.getProxiesByLocation(countryCode, cityName), {
      showErrorToast: true,
      customErrorMessage: '按地理位置获取代理失败',
    })

    return result.success ? result.data || [] : []
  }

  /**
   * 质量评估相关方法
   */
  static async assessProxyQuality(proxyId: string): Promise<QualityScore | null> {
    const result = await apiCall(() => proxyAPI.assessProxyQuality(proxyId), {
      showLoading: true,
      loadingMessage: '评估代理质量中...',
      showSuccessToast: true,
      successMessage: '质量评估已启动',
      customErrorMessage: '质量评估失败',
    })

    return result.success ? result.data || null : null
  }

  static async batchAssessQuality(proxyIds: string[]): Promise<boolean> {
    const result = await apiCall(() => proxyAPI.batchAssessQuality(proxyIds), {
      showLoading: true,
      loadingMessage: `评估 ${proxyIds.length} 个代理质量中...`,
      showSuccessToast: true,
      successMessage: `已启动 ${proxyIds.length} 个代理的质量评估`,
      customErrorMessage: '批量质量评估失败',
    })

    return result.success
  }

  static async getProxiesByQuality(minScore?: number, maxScore?: number): Promise<Proxy[]> {
    const result = await apiCall(() => proxyAPI.getProxiesByQuality(minScore, maxScore), {
      showErrorToast: true,
      customErrorMessage: '按质量获取代理失败',
    })

    return result.success ? result.data || [] : []
  }

  static async getTopQualityProxies(limit?: number): Promise<Proxy[]> {
    const result = await apiCall(() => proxyAPI.getTopQualityProxies(limit), {
      showErrorToast: true,
      customErrorMessage: '获取高质量代理失败',
    })

    return result.success ? result.data || [] : []
  }

  /**
   * 标签和智能路由相关方法
   */
  static async getProxiesByTags(tags: string[]): Promise<Proxy[]> {
    const result = await apiCall(() => proxyAPI.getProxiesByTags(tags), {
      showErrorToast: true,
      customErrorMessage: '按标签获取代理失败',
    })

    return result.success ? result.data || [] : []
  }

  static async getProxiesByScenario(scenario: string): Promise<Proxy[]> {
    const result = await apiCall(() => proxyAPI.getProxiesByScenario(scenario), {
      showErrorToast: true,
      customErrorMessage: '按场景获取代理失败',
    })

    return result.success ? result.data || [] : []
  }

  static async getProxyWithSmartRouting(filter: ProxyFilterByTags): Promise<Proxy | null> {
    const result = await apiCall(() => proxyAPI.getProxyWithSmartRouting(filter), {
      showErrorToast: true,
      customErrorMessage: '智能路由获取代理失败',
    })

    return result.success ? result.data || null : null
  }

  static async getProxyTags(proxyId: string): Promise<ProxyTag[]> {
    const result = await apiCall(() => proxyAPI.getProxyTags(proxyId), {
      showErrorToast: true,
      customErrorMessage: '获取代理标签失败',
    })

    return result.success ? result.data || [] : []
  }

  static async removeProxyTags(proxyId: string, tagIds: string[]): Promise<boolean> {
    const result = await apiCall(() => proxyAPI.removeProxyTags(proxyId, tagIds), {
      showSuccessToast: true,
      successMessage: '标签移除成功',
      customErrorMessage: '移除代理标签失败',
    })

    return result.success
  }

  /**
   * 清除代理相关缓存
   */
  static clearProxyCache(): void {
    const cacheKeys = [
      `${this.CACHE_PREFIX}_proxies`,
      `${this.CACHE_PREFIX}_stats`,
      `${this.CACHE_PREFIX}_location_stats`,
      `${this.CACHE_PREFIX}_location_groups_country`,
      `${this.CACHE_PREFIX}_location_groups_city`,
    ]

    clearApiCacheBatch(cacheKeys)
  }
}