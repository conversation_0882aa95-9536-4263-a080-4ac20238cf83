// 简单的EventEmitter实现，用于浏览器环境
class SimpleEventEmitter {
  private events: Record<string, Function[]> = {}

  on(event: string, listener: Function): void {
    if (!this.events[event]) {
      this.events[event] = []
    }
    this.events[event].push(listener)
  }

  off(event: string, listener: Function): void {
    if (!this.events[event]) return
    this.events[event] = this.events[event].filter(l => l !== listener)
  }

  emit(event: string, ...args: any[]): void {
    if (!this.events[event]) return
    this.events[event].forEach(listener => {
      try {
        listener(...args)
      } catch (error) {
        console.error('Error in event listener:', error)
      }
    })
  }

  removeAllListeners(event?: string): void {
    if (event) {
      delete this.events[event]
    } else {
      this.events = {}
    }
  }

  setMaxListeners(n: number): void {
    // 在简单实现中忽略此方法
  }
}

export interface RealtimeMessage {
  type: string
  timestamp: string
  data: any
}

export interface SystemMetricsUpdate {
  cpu_usage: number
  memory_usage: number
  disk_usage: number
  network_in: number
  network_out: number
  active_connections: number
  active_tasks: number
  active_proxies: number
}

export interface TaskStatusUpdate {
  task_id: string
  status: string
  progress?: number
  result?: any
  error?: string
}

export interface ProxyStatusUpdate {
  proxy_id: string
  status: string
  quality_score?: number
  response_time?: number
  last_check: string
}

export interface SystemEvent {
  id: string
  type: string
  severity: 'info' | 'warning' | 'error' | 'critical'
  title: string
  description: string
  timestamp: string
  metadata?: any
}

class WebSocketService extends SimpleEventEmitter {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private isConnecting = false
  private shouldReconnect = true
  private heartbeatInterval: NodeJS.Timeout | null = null
  private connectionTimeout: NodeJS.Timeout | null = null

  constructor() {
    super()
  }

  connect(): void {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.CONNECTING)) {
      return
    }

    this.isConnecting = true
    
    try {
      // 构建WebSocket URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const host = window.location.host
      const wsUrl = `${protocol}//${host}/api/realtime`
      
      console.log('Connecting to WebSocket:', wsUrl)
      this.ws = new WebSocket(wsUrl)

      // 设置连接超时
      this.connectionTimeout = setTimeout(() => {
        if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
          console.warn('WebSocket connection timeout')
          this.ws.close()
        }
      }, 10000)

      this.ws.onopen = this.handleOpen.bind(this)
      this.ws.onmessage = this.handleMessage.bind(this)
      this.ws.onclose = this.handleClose.bind(this)
      this.ws.onerror = this.handleError.bind(this)

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      this.isConnecting = false
      this.scheduleReconnect()
    }
  }

  disconnect(): void {
    this.shouldReconnect = false
    this.clearHeartbeat()
    this.clearConnectionTimeout()
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
    
    this.emit('disconnected')
  }

  private handleOpen(): void {
    console.log('WebSocket connected')
    this.isConnecting = false
    this.reconnectAttempts = 0
    this.clearConnectionTimeout()
    this.startHeartbeat()
    this.emit('connected')
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: RealtimeMessage = JSON.parse(event.data)
      
      // 处理不同类型的消息
      switch (message.type) {
        case 'system_metrics':
          this.emit('systemMetrics', message.data as SystemMetricsUpdate)
          break
        case 'task_status':
          this.emit('taskStatus', message.data as TaskStatusUpdate)
          break
        case 'proxy_status':
          this.emit('proxyStatus', message.data as ProxyStatusUpdate)
          break
        case 'system_event':
          this.emit('systemEvent', message.data as SystemEvent)
          break
        case 'heartbeat':
          // 心跳响应，不需要特殊处理
          break
        default:
          console.warn('Unknown message type:', message.type)
          this.emit('message', message)
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error, event.data)
    }
  }

  private handleClose(event: CloseEvent): void {
    console.log('WebSocket closed:', event.code, event.reason)
    this.isConnecting = false
    this.clearHeartbeat()
    this.clearConnectionTimeout()
    this.ws = null
    
    this.emit('disconnected', { code: event.code, reason: event.reason })
    
    if (this.shouldReconnect && event.code !== 1000) {
      this.scheduleReconnect()
    }
  }

  private handleError(error: Event): void {
    console.error('WebSocket error:', error)
    this.emit('error', error)
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      this.emit('maxReconnectAttemptsReached')
      return
    }

    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts)
    console.log(`Scheduling reconnect in ${delay}ms (attempt ${this.reconnectAttempts + 1})`)
    
    setTimeout(() => {
      this.reconnectAttempts++
      this.connect()
    }, delay)
  }

  private startHeartbeat(): void {
    this.clearHeartbeat()
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'ping' }))
      }
    }, 30000) // 每30秒发送一次心跳
  }

  private clearHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  private clearConnectionTimeout(): void {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout)
      this.connectionTimeout = null
    }
  }

  // 订阅特定类型的事件
  subscribeToSystemMetrics(callback: (data: SystemMetricsUpdate) => void): () => void {
    this.on('systemMetrics', callback)
    return () => this.off('systemMetrics', callback)
  }

  subscribeToTaskStatus(callback: (data: TaskStatusUpdate) => void): () => void {
    this.on('taskStatus', callback)
    return () => this.off('taskStatus', callback)
  }

  subscribeToProxyStatus(callback: (data: ProxyStatusUpdate) => void): () => void {
    this.on('proxyStatus', callback)
    return () => this.off('proxyStatus', callback)
  }

  subscribeToSystemEvents(callback: (data: SystemEvent) => void): () => void {
    this.on('systemEvent', callback)
    return () => this.off('systemEvent', callback)
  }

  // 获取连接状态
  getConnectionState(): string {
    if (!this.ws) return 'disconnected'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting'
      case WebSocket.OPEN:
        return 'connected'
      case WebSocket.CLOSING:
        return 'closing'
      case WebSocket.CLOSED:
        return 'disconnected'
      default:
        return 'unknown'
    }
  }

  // 发送消息到服务器
  send(message: any): boolean {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message))
        return true
      } catch (error) {
        console.error('Failed to send WebSocket message:', error)
        return false
      }
    }
    return false
  }

  // 订阅特定的数据流
  subscribe(topics: string[]): void {
    this.send({
      type: 'subscribe',
      topics: topics
    })
  }

  // 取消订阅
  unsubscribe(topics: string[]): void {
    this.send({
      type: 'unsubscribe',
      topics: topics
    })
  }
}

// 创建单例实例
export const websocketService = new WebSocketService()

// 自动连接管理
let autoConnectEnabled = false

export const enableAutoConnect = (): void => {
  if (autoConnectEnabled) return
  
  autoConnectEnabled = true
  
  // 页面可见时自动连接
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
      if (websocketService.getConnectionState() === 'disconnected') {
        websocketService.connect()
      }
    } else {
      // 页面隐藏时断开连接以节省资源
      websocketService.disconnect()
    }
  }

  document.addEventListener('visibilitychange', handleVisibilityChange)
  
  // 页面卸载时断开连接
  window.addEventListener('beforeunload', () => {
    websocketService.disconnect()
  })

  // 初始连接
  if (document.visibilityState === 'visible') {
    websocketService.connect()
  }
}

export const disableAutoConnect = (): void => {
  autoConnectEnabled = false
  websocketService.disconnect()
}

export default websocketService
