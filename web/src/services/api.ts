import axios, { AxiosResponse, AxiosError } from 'axios'
import { enhancedToast, specialToasts } from '../utils/toastConfig'
import { extractErrorFromAxios } from '../utils/errorHandler'
import { mockApiKeyAPI, mockSettingsAPI, shouldUseMockAPI } from './mockApiService'
import type {
  ApiResponse,
  User,
  LoginCredentials,
  RegisterData,
  AuthResponse,
  Proxy,
  ProxyFormData,
  ProxyStats,
  ProxyLocationStats,
  ProxyGroupByLocation,
  QualityScore,
  ProxyTag,
  ProxyTagRequest,
  ProxyTagAssignRequest,
  ProxyFilterByTags,
  Task,
  TaskFormData,
  TaskStats,
  SystemHealth,
  APIKeyInfo,
  APIKeyResponse,
  CreateAPIKeyRequest,
  BatchImportProxiesRequest,
  BackendPaginatedResponse,
  ProxyListParams,
} from '../types'

// 创建axios实例
const api = axios.create({
  baseURL: '/api/v1',
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // 检查业务层面的成功状态
    const data = response.data as any
    if (data && typeof data.success === 'boolean' && !data.success) {
      // 业务失败，但HTTP状态码是200
      const errorMessage = data.error || data.message || '操作失败'
      enhancedToast.error(errorMessage)
      return Promise.reject(new Error(errorMessage))
    }
    return response
  },
  (error: AxiosError) => {
    const { response, request } = error
    let shouldShowToast = true

    if (response) {
      const appError = extractErrorFromAxios(error)

      switch (response.status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          // 避免在登录页面重复跳转
          if (!window.location.pathname.includes('/login')) {
            window.location.href = '/login'
          }
          specialToasts.sessionExpired()
          shouldShowToast = false
          break
        case 403:
          // 权限不足
          specialToasts.permissionError()
          shouldShowToast = false
          break
        case 422:
          // 数据验证错误，通常包含详细的字段错误信息
          const validationErrors = (response.data as any)?.errors
          if (validationErrors && typeof validationErrors === 'object') {
            // 显示第一个验证错误
            const firstError = Object.values(validationErrors)[0]
            if (Array.isArray(firstError) && firstError.length > 0) {
              specialToasts.validationError(firstError[0] as string)
              shouldShowToast = false
            }
          }
          break
        case 429:
          // 限流错误，显示重试时间
          const retryAfter = response.headers['retry-after']
          if (retryAfter) {
            enhancedToast.warning(`请求过于频繁，请在 ${retryAfter} 秒后重试`)
            shouldShowToast = false
          }
          break
      }

      if (shouldShowToast) {
        enhancedToast.error(appError.message)
      }
    } else if (request) {
      // 网络错误
      specialToasts.networkError()
    } else {
      // 请求配置错误
      console.error('Request configuration error:', error.message)
      enhancedToast.error('请求配置错误')
    }

    return Promise.reject(error)
  }
)

// 认证相关API
export const authAPI = {
  // 用户注册
  register: (userData: RegisterData): Promise<AxiosResponse<ApiResponse<User>>> =>
    api.post('/auth/register', userData),

  // 用户登录
  login: (credentials: LoginCredentials): Promise<AxiosResponse<ApiResponse<AuthResponse>>> =>
    api.post('/auth/login', credentials),

  // 获取用户资料
  getProfile: (): Promise<AxiosResponse<ApiResponse<User>>> => api.get('/auth/profile'),
}

// 代理相关API
export const proxyAPI = {
  // 获取代理列表（支持分页和筛选）
  getProxies: (
    params?: ProxyListParams
  ): Promise<AxiosResponse<BackendPaginatedResponse<Proxy>>> => {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.status) searchParams.append('status', params.status)
    if (params?.type) searchParams.append('type', params.type)
    if (params?.country_code) searchParams.append('country_code', params.country_code)
    if (params?.search) searchParams.append('search', params.search)

    const queryString = searchParams.toString()
    return api.get(`/proxies${queryString ? '?' + queryString : ''}`)
  },

  // 获取代理列表（兼容旧版本，不带分页）
  getAllProxies: (): Promise<AxiosResponse<ApiResponse<Proxy[]>>> =>
    api.get('/proxies?limit=10000'), // 使用大的limit来获取所有数据

  // 获取可用代理
  getAvailableProxies: (): Promise<AxiosResponse<ApiResponse<Proxy[]>>> =>
    api.get('/proxies/available'),

  // 添加代理
  addProxy: (proxyData: ProxyFormData): Promise<AxiosResponse<ApiResponse<Proxy>>> =>
    api.post('/proxies', proxyData),

  // 删除代理
  deleteProxy: (proxyId: string): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.delete(`/proxies/${proxyId}`),

  // 获取代理统计
  getProxyStats: (): Promise<AxiosResponse<ApiResponse<ProxyStats>>> => api.get('/proxies/stats'),

  // 根据策略获取代理
  getProxyByStrategy: (strategy: string): Promise<AxiosResponse<ApiResponse<Proxy>>> =>
    api.get(`/proxies/strategy?strategy=${strategy}`),

  // 按地理位置获取代理
  getProxiesByLocation: (
    countryCode?: string,
    cityName?: string
  ): Promise<AxiosResponse<ApiResponse<Proxy[]>>> => {
    const params = new URLSearchParams()
    if (countryCode) params.append('country_code', countryCode)
    if (cityName) params.append('city_name', cityName)
    return api.get(`/proxies/location?${params.toString()}`)
  },

  // 获取代理地理位置统计
  getProxyLocationStats: (): Promise<AxiosResponse<ApiResponse<ProxyLocationStats>>> =>
    api.get('/proxies/location/stats'),

  // 按地理位置分组代理
  groupProxiesByLocation: (
    groupBy: 'country' | 'city' = 'country'
  ): Promise<AxiosResponse<ApiResponse<ProxyGroupByLocation[]>>> =>
    api.get(`/proxies/location/groups?group_by=${groupBy}`),

  // 代理健康检查
  healthCheck: (proxyId: string): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.post(`/proxies/${proxyId}/health-check`),

  // 批量健康检查
  batchHealthCheck: (proxyIds: string[]): Promise<AxiosResponse<ApiResponse<any[]>>> =>
    api.post('/proxies/batch/health-check', { proxy_ids: proxyIds }),

  // 检查所有代理
  healthCheckAll: (): Promise<AxiosResponse<ApiResponse<any[]>>> =>
    api.post('/proxies/health-check-all'),

  // 批量导入代理
  batchImportProxies: (
    request: BatchImportProxiesRequest
  ): Promise<AxiosResponse<ApiResponse<any>>> => api.post('/proxies/batch-import', request),

  // 获取单个代理详情
  getProxy: (proxyId: string): Promise<AxiosResponse<ApiResponse<Proxy>>> =>
    api.get(`/proxies/${proxyId}`),

  // 质量评估相关API
  // 评估单个代理质量
  assessProxyQuality: (proxyId: string): Promise<AxiosResponse<ApiResponse<QualityScore>>> =>
    api.post(`/proxies/${proxyId}/quality/assess`),

  // 批量评估代理质量
  batchAssessQuality: (proxyIds: string[]): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.post('/proxies/quality/assess', { proxy_ids: proxyIds }),

  // 根据质量评分获取代理
  getProxiesByQuality: (
    minScore?: number,
    maxScore?: number
  ): Promise<AxiosResponse<ApiResponse<Proxy[]>>> => {
    const params = new URLSearchParams()
    if (minScore !== undefined) params.append('min_score', minScore.toString())
    if (maxScore !== undefined) params.append('max_score', maxScore.toString())
    return api.get(`/proxies/quality?${params.toString()}`)
  },

  // 获取质量最高的代理
  getTopQualityProxies: (limit?: number): Promise<AxiosResponse<ApiResponse<Proxy[]>>> => {
    const params = new URLSearchParams()
    if (limit !== undefined) params.append('limit', limit.toString())
    return api.get(`/proxies/quality/top?${params.toString()}`)
  },

  // 标签和智能路由相关API
  // 根据标签获取代理
  getProxiesByTags: (tags: string[]): Promise<AxiosResponse<ApiResponse<Proxy[]>>> => {
    const params = new URLSearchParams()
    tags.forEach((tag) => params.append('tags', tag))
    return api.get(`/proxies/tags?${params.toString()}`)
  },

  // 根据场景获取代理
  getProxiesByScenario: (scenario: string): Promise<AxiosResponse<ApiResponse<Proxy[]>>> => {
    const params = new URLSearchParams()
    params.append('scenario', scenario)
    return api.get(`/proxies/scenario?${params.toString()}`)
  },

  // 智能路由获取代理
  getProxyWithSmartRouting: (
    filter: ProxyFilterByTags
  ): Promise<AxiosResponse<ApiResponse<Proxy>>> => {
    const params = new URLSearchParams()
    if (filter.tags) filter.tags.forEach((tag) => params.append('tags', tag))
    if (filter.scenario) params.append('scenario', filter.scenario)
    if (filter.strategy) params.append('strategy', filter.strategy)
    return api.get(`/proxies/smart-routing?${params.toString()}`)
  },

  // 获取代理的标签 - 注意：后端路由使用 proxy_id 参数
  getProxyTags: (proxyId: string): Promise<AxiosResponse<ApiResponse<ProxyTag[]>>> =>
    api.get(`/proxies/${proxyId}/tags`),

  // 移除代理的标签 - 注意：后端路由使用 proxy_id 参数
  removeProxyTags: (proxyId: string, tagIds: string[]): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.delete(`/proxies/${proxyId}/tags`, { data: { tag_ids: tagIds } }),
}

// 标签管理API
export const tagAPI = {
  // 获取所有标签
  getTags: (): Promise<AxiosResponse<ApiResponse<ProxyTag[]>>> => api.get('/tags'),

  // 创建标签
  createTag: (data: ProxyTagRequest): Promise<AxiosResponse<ApiResponse<ProxyTag>>> =>
    api.post('/tags', data),

  // 获取单个标签
  getTag: (id: string): Promise<AxiosResponse<ApiResponse<ProxyTag>>> => api.get(`/tags/${id}`),

  // 更新标签
  updateTag: (id: string, data: ProxyTagRequest): Promise<AxiosResponse<ApiResponse<ProxyTag>>> =>
    api.put(`/tags/${id}`, data),

  // 删除标签
  deleteTag: (id: string): Promise<AxiosResponse<ApiResponse<void>>> => api.delete(`/tags/${id}`),

  // 分配标签
  assignTags: (data: ProxyTagAssignRequest): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.post('/tags/assign', data),
}

// 任务相关API
export const taskAPI = {
  // 基础任务操作
  getTasks: (): Promise<AxiosResponse<ApiResponse<Task[]>>> => api.get('/tasks'),

  getTask: (taskId: string): Promise<AxiosResponse<ApiResponse<Task>>> =>
    api.get(`/tasks/${taskId}`),

  createTask: (taskData: TaskFormData): Promise<AxiosResponse<ApiResponse<Task>>> =>
    api.post('/tasks', taskData),

  // 批量创建任务
  createBatchTasks: (
    tasksData: TaskFormData[]
  ): Promise<AxiosResponse<ApiResponse<{ created: number; failed: number; errors?: string[] }>>> =>
    api.post('/tasks/batch', { tasks: tasksData }),

  updateTask: (taskId: string, taskData: TaskFormData): Promise<AxiosResponse<ApiResponse<Task>>> =>
    api.put(`/tasks/${taskId}`, taskData),

  deleteTask: (taskId: string): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.delete(`/tasks/${taskId}`),

  // 逻辑删除任务（设置deleted=true）
  softDeleteTask: (taskId: string): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.patch(`/tasks/${taskId}/soft-delete`),

  cancelTask: (taskId: string): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.patch(`/tasks/${taskId}/cancel`),

  getTaskStats: (): Promise<AxiosResponse<ApiResponse<TaskStats>>> => api.get('/tasks/stats'),

  // 批量URL测试任务
  createBatchUrlTask: (taskData: any): Promise<AxiosResponse<ApiResponse<Task>>> =>
    api.post('/tasks/batch-url', taskData),

  // 代理性能对比任务
  createProxyComparisonTask: (taskData: any): Promise<AxiosResponse<ApiResponse<Task>>> =>
    api.post('/tasks/proxy-comparison', taskData),

  // 任务模板相关
  getTemplates: (): Promise<AxiosResponse<ApiResponse<any[]>>> => api.get('/tasks/templates'),

  createTemplate: (templateData: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.post('/tasks/templates', templateData),

  updateTemplate: (
    templateId: string,
    templateData: any
  ): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.put(`/tasks/templates/${templateId}`, templateData),

  deleteTemplate: (templateId: string): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.delete(`/tasks/templates/${templateId}`),

  // 任务分析
  getTaskAnalytics: (timeRange?: string): Promise<AxiosResponse<ApiResponse<any>>> => {
    const params = timeRange ? `?range=${timeRange}` : ''
    return api.get(`/tasks/analytics${params}`)
  },

  // 批量操作
  batchCancelTasks: (taskIds: string[]): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.post('/tasks/batch-cancel', { task_ids: taskIds }),

  batchDeleteTasks: (taskIds: string[]): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.post('/tasks/batch-delete', { task_ids: taskIds }),

  // 定时任务相关
  createScheduledTask: (taskData: any): Promise<AxiosResponse<ApiResponse<Task>>> =>
    api.post('/tasks/scheduled', taskData),

  getScheduledTasks: (): Promise<AxiosResponse<ApiResponse<any[]>>> => api.get('/tasks/scheduled'),

  updateScheduledTask: (taskId: string, taskData: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.put(`/tasks/scheduled/${taskId}`, taskData),
}

// 采集器相关API
export const collectorAPI = {
  // 获取采集器状态
  getStatus: (): Promise<AxiosResponse<ApiResponse<any>>> => api.get('/collector/status'),

  // 获取采集器统计
  getStats: (): Promise<AxiosResponse<ApiResponse<any>>> => api.get('/collector/stats'),

  // 获取采集器指标
  getMetrics: (): Promise<AxiosResponse<ApiResponse<any>>> => api.get('/collector/metrics'),

  // 获取采集历史
  getHistory: (): Promise<AxiosResponse<ApiResponse<any>>> => api.get('/collector/history'),

  // 触发采集
  triggerCollection: (): Promise<AxiosResponse<ApiResponse<any>>> => api.post('/collector/collect'),

  // 启动采集器
  startCollector: (): Promise<AxiosResponse<ApiResponse<any>>> => api.post('/collector/start'),

  // 停止采集器
  stopCollector: (): Promise<AxiosResponse<ApiResponse<any>>> => api.post('/collector/stop'),

  // 任务管理
  tasks: {
    // 获取任务列表
    list: (params?: { status?: string; limit?: number; offset?: number }): Promise<AxiosResponse<ApiResponse<any>>> => {
      const searchParams = new URLSearchParams()
      if (params?.status) searchParams.append('status', params.status)
      if (params?.limit) searchParams.append('limit', params.limit.toString())
      if (params?.offset) searchParams.append('offset', params.offset.toString())
      return api.get(`/collector/tasks?${searchParams.toString()}`)
    },

    // 创建任务
    create: (config: any): Promise<AxiosResponse<ApiResponse<any>>> =>
      api.post('/collector/tasks', config),

    // 获取任务详情
    get: (taskId: string): Promise<AxiosResponse<ApiResponse<any>>> =>
      api.get(`/collector/tasks/${taskId}`),

    // 暂停任务
    pause: (taskId: string): Promise<AxiosResponse<ApiResponse<any>>> =>
      api.post(`/collector/tasks/${taskId}/pause`),

    // 恢复任务
    resume: (taskId: string): Promise<AxiosResponse<ApiResponse<any>>> =>
      api.post(`/collector/tasks/${taskId}/resume`),

    // 取消任务
    cancel: (taskId: string): Promise<AxiosResponse<ApiResponse<any>>> =>
      api.post(`/collector/tasks/${taskId}/cancel`),

    // 获取任务进度
    getProgress: (taskId: string): Promise<AxiosResponse<ApiResponse<any>>> =>
      api.get(`/collector/tasks/${taskId}/progress`),
  },

  // 队列管理
  queue: {
    // 获取队列状态
    getStatus: (): Promise<AxiosResponse<ApiResponse<any>>> =>
      api.get('/collector/queue/status'),
  }
}

// 系统相关API
export const systemAPI = {
  // 健康检查
  healthCheck: (): Promise<AxiosResponse<ApiResponse<SystemHealth>>> => axios.get('/health'),

  // 获取系统指标
  getMetrics: (): Promise<AxiosResponse<string>> => axios.get('/metrics'),

  // 获取系统性能指标
  getPerformanceMetrics: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/system/performance'),

  // 获取系统日志
  getLogs: (level?: string, limit?: number): Promise<AxiosResponse<ApiResponse<any[]>>> => {
    const params = new URLSearchParams()
    if (level) params.append('level', level)
    if (limit) params.append('limit', limit.toString())
    return api.get(`/system/logs?${params.toString()}`)
  },

  // 获取系统事件
  getEvents: (limit?: number): Promise<AxiosResponse<ApiResponse<any[]>>> => {
    const params = new URLSearchParams()
    if (limit) params.append('limit', limit.toString())
    return api.get(`/system/events?${params.toString()}`)
  },

  // 获取系统配置
  getConfig: (): Promise<AxiosResponse<ApiResponse<any>>> => api.get('/system/config'),

  // 更新系统配置
  updateConfig: (config: any): Promise<AxiosResponse<ApiResponse<void>>> =>
    api.put('/system/config', config),

  // 获取系统统计信息
  getSystemStats: (): Promise<AxiosResponse<ApiResponse<any>>> => api.get('/system/stats'),

  // 获取网络连接状态
  getNetworkStatus: (): Promise<AxiosResponse<ApiResponse<any>>> => api.get('/system/network'),

  // 获取磁盘使用情况
  getDiskUsage: (): Promise<AxiosResponse<ApiResponse<any>>> => api.get('/system/disk'),
}

// API Key 管理API
export const apiKeyAPI = {
  // 获取所有API Key列表
  getAPIKeys: async (): Promise<AxiosResponse<ApiResponse<APIKeyInfo[]>>> => {
    try {
      return await api.get('/user/apikeys')
    } catch (error) {
      if (shouldUseMockAPI()) {
        console.warn('API endpoint not found, using mock data for API keys')
        return (await mockApiKeyAPI.getAPIKeys()) as any
      }
      throw error
    }
  },

  // 获取单个API Key信息（兼容旧版本）
  getAPIKey: async (): Promise<AxiosResponse<ApiResponse<APIKeyInfo>>> => {
    try {
      return await api.get('/user/apikey')
    } catch (error) {
      if (shouldUseMockAPI()) {
        console.warn('API endpoint not found, using mock data for API key')
        return (await mockApiKeyAPI.getAPIKey()) as any
      }
      throw error
    }
  },

  // 创建新的API Key
  createAPIKey: async (
    data: CreateAPIKeyRequest
  ): Promise<AxiosResponse<ApiResponse<APIKeyResponse>>> => {
    try {
      return await api.post('/user/apikeys', data)
    } catch (error) {
      if (shouldUseMockAPI()) {
        console.warn('API endpoint not found, using mock data for creating API key')
        return (await mockApiKeyAPI.createAPIKey(data)) as any
      }
      throw error
    }
  },

  // 更新API Key
  updateAPIKey: async (
    keyId: string,
    data: { name?: string; is_active?: boolean }
  ): Promise<AxiosResponse<ApiResponse<APIKeyInfo>>> => {
    try {
      return await api.put(`/user/apikeys/${keyId}`, data)
    } catch (error) {
      if (shouldUseMockAPI()) {
        console.warn('API endpoint not found, using mock data for updating API key')
        return (await mockApiKeyAPI.updateAPIKey(keyId, data)) as any
      }
      throw error
    }
  },

  // 删除特定API Key
  deleteAPIKey: async (keyId: string): Promise<AxiosResponse<ApiResponse<void>>> => {
    try {
      return await api.delete(`/user/apikeys/${keyId}`)
    } catch (error) {
      if (shouldUseMockAPI()) {
        console.warn('API endpoint not found, using mock data for deleting API key')
        return (await mockApiKeyAPI.deleteAPIKey(keyId)) as any
      }
      throw error
    }
  },
}

// 设置相关API
export const settingsAPI = {
  // 获取用户设置
  getUserSettings: async (): Promise<AxiosResponse<ApiResponse<any>>> => {
    try {
      return await api.get('/user/settings')
    } catch (error) {
      if (shouldUseMockAPI()) {
        console.warn('API endpoint not found, using mock data for user settings')
        return (await mockSettingsAPI.getUserSettings()) as any
      }
      throw error
    }
  },

  // 更新用户设置
  updateUserSettings: async (settings: any): Promise<AxiosResponse<ApiResponse<any>>> => {
    try {
      return await api.put('/user/settings', settings)
    } catch (error) {
      if (shouldUseMockAPI()) {
        console.warn('API endpoint not found, using mock data for updating user settings')
        return (await mockSettingsAPI.updateUserSettings(settings)) as any
      }
      throw error
    }
  },

  // 获取系统设置
  getSystemSettings: (): Promise<AxiosResponse<ApiResponse<any>>> => api.get('/system/settings'),

  // 更新系统设置（管理员权限）
  updateSystemSettings: (settings: any): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.put('/system/settings', settings),

  // 获取代理默认设置
  getProxySettings: async (): Promise<AxiosResponse<ApiResponse<any>>> => {
    try {
      return await api.get('/user/proxy-settings')
    } catch (error) {
      if (shouldUseMockAPI()) {
        console.warn('API endpoint not found, using mock data for proxy settings')
        return (await mockSettingsAPI.getProxySettings()) as any
      }
      throw error
    }
  },

  // 更新代理默认设置
  updateProxySettings: async (settings: any): Promise<AxiosResponse<ApiResponse<any>>> => {
    try {
      return await api.put('/user/proxy-settings', settings)
    } catch (error) {
      if (shouldUseMockAPI()) {
        console.warn('API endpoint not found, using mock data for updating proxy settings')
        return (await mockSettingsAPI.updateProxySettings(settings)) as any
      }
      throw error
    }
  },

  // 重置设置到默认值
  resetSettings: async (): Promise<AxiosResponse<ApiResponse<void>>> => {
    try {
      return await api.post('/user/settings/reset')
    } catch (error) {
      if (shouldUseMockAPI()) {
        console.warn('API endpoint not found, using mock data for resetting settings')
        return (await mockSettingsAPI.resetSettings()) as any
      }
      throw error
    }
  },
}

// 导出 API 客户端
export const apiClient = api
export default api