// 模拟API服务 - 用于在后端API未实现时提供临时数据
import { APIKeyInfo, CreateAPIKeyRequest } from '../types'

// 模拟数据存储
class MockDataStore {
  private apiKeys: APIKeyInfo[] = []
  private userSettings: any = {}
  private notificationSettings: any = {}
  private proxySettings: any = {}

  constructor() {
    // 初始化一些示例数据
    this.initializeData()
  }

  private initializeData() {
    // 示例API密钥
    this.apiKeys = [
      {
        id: 'key_1',
        name: '生产环境密钥',
        api_key: 'pk_live_1234567890abcdef1234567890abcdef',
        masked_api_key: 'pk_live_1234...cdef',
        is_active: true,
        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1年后过期
        api_key_created: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天前创建
        api_key_last_used: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2小时前使用
        api_key_usage_count: 1250
      },
      {
        id: 'key_2',
        name: '测试环境密钥',
        api_key: 'pk_test_abcdef1234567890abcdef1234567890',
        masked_api_key: 'pk_test_abcd...7890',
        is_active: true,
        expires_at: null, // 永不过期
        api_key_created: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天前创建
        api_key_last_used: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1天前使用
        api_key_usage_count: 89
      }
    ]

    // 示例用户设置
    this.userSettings = {
      username: 'admin',
      email: '<EMAIL>',
      theme: 'light',
      language: 'zh-CN',
      autoRefresh: true,
      refreshInterval: 30
    }



    // 示例代理设置
    this.proxySettings = {
      defaultTimeout: 30,
      maxRetries: 3,
      defaultStrategy: 'round_robin'
    }
  }

  // API密钥相关方法
  getAPIKeys(): APIKeyInfo[] {
    return this.apiKeys
  }

  createAPIKey(request: CreateAPIKeyRequest): APIKeyInfo {
    const newKey: APIKeyInfo = {
      id: `key_${Date.now()}`,
      name: request.name,
      api_key: `pk_live_${Math.random().toString(36).substring(2, 34)}`,
      masked_api_key: `pk_live_${Math.random().toString(36).substring(2, 6)}...${Math.random().toString(36).substring(2, 6)}`,
      is_active: true,
      expires_at: request.expires_in_days 
        ? new Date(Date.now() + request.expires_in_days * 24 * 60 * 60 * 1000).toISOString()
        : null,
      api_key_created: new Date().toISOString(),
      api_key_last_used: null,
      api_key_usage_count: 0
    }
    
    this.apiKeys.push(newKey)
    return newKey
  }

  updateAPIKey(id: string, updates: Partial<APIKeyInfo>): APIKeyInfo | null {
    const index = this.apiKeys.findIndex(key => key.id === id)
    if (index === -1) return null

    this.apiKeys[index] = { ...this.apiKeys[index], ...updates }
    return this.apiKeys[index]
  }

  deleteAPIKey(id: string): boolean {
    const index = this.apiKeys.findIndex(key => key.id === id)
    if (index === -1) return false

    this.apiKeys.splice(index, 1)
    return true
  }

  // 设置相关方法
  getUserSettings(): any {
    return this.userSettings
  }

  updateUserSettings(settings: any): any {
    this.userSettings = { ...this.userSettings, ...settings }
    return this.userSettings
  }

  getNotificationSettings(): any {
    return this.notificationSettings
  }

  updateNotificationSettings(settings: any): any {
    this.notificationSettings = { ...this.notificationSettings, ...settings }
    return this.notificationSettings
  }

  getProxySettings(): any {
    return this.proxySettings
  }

  updateProxySettings(settings: any): any {
    this.proxySettings = { ...this.proxySettings, ...settings }
    return this.proxySettings
  }





  resetSettings(): void {
    this.initializeData()
  }
}

// 创建单例实例
const mockDataStore = new MockDataStore()

// 模拟API响应的延迟
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟API响应格式
const createMockResponse = <T>(data: T, success: boolean = true) => ({
  data: {
    success,
    data,
    message: success ? 'Success' : 'Error'
  }
})

// 模拟API密钥服务
export const mockApiKeyAPI = {
  async getAPIKeys() {
    await delay(300)
    return createMockResponse(mockDataStore.getAPIKeys())
  },

  async createAPIKey(request: CreateAPIKeyRequest) {
    await delay(800)
    const newKey = mockDataStore.createAPIKey(request)
    return createMockResponse(newKey)
  },

  async updateAPIKey(id: string, updates: any) {
    await delay(500)
    const updatedKey = mockDataStore.updateAPIKey(id, updates)
    if (!updatedKey) {
      throw new Error('API Key not found')
    }
    return createMockResponse(updatedKey)
  },

  async deleteAPIKey(id: string) {
    await delay(400)
    const success = mockDataStore.deleteAPIKey(id)
    if (!success) {
      throw new Error('API Key not found')
    }
    return createMockResponse(null)
  },

  // 兼容旧版本API
  async getAPIKey() {
    await delay(300)
    const keys = mockDataStore.getAPIKeys()
    return createMockResponse(keys[0] || null)
  }
}

// 模拟设置服务
export const mockSettingsAPI = {
  async getUserSettings() {
    await delay(200)
    return createMockResponse(mockDataStore.getUserSettings())
  },

  async updateUserSettings(settings: any) {
    await delay(400)
    const updated = mockDataStore.updateUserSettings(settings)
    return createMockResponse(updated)
  },



  async getProxySettings() {
    await delay(200)
    return createMockResponse(mockDataStore.getProxySettings())
  },

  async updateProxySettings(settings: any) {
    await delay(400)
    const updated = mockDataStore.updateProxySettings(settings)
    return createMockResponse(updated)
  },



  async resetSettings() {
    await delay(500)
    mockDataStore.resetSettings()
    return createMockResponse(null)
  }
}

// 检查是否应该使用模拟API
export const shouldUseMockAPI = () => {
  // 检查localStorage中的设置
  const useMockAPI = localStorage.getItem('use-mock-api')
  if (useMockAPI === 'true') return true
  if (useMockAPI === 'false') return false

  // 默认在开发环境中启用模拟API
  return import.meta.env.DEV
}

// 启用模拟API模式
export const enableMockAPI = () => {
  localStorage.setItem('use-mock-api', 'true')
  console.log('模拟API模式已启用')
}

// 禁用模拟API模式
export const disableMockAPI = () => {
  localStorage.setItem('use-mock-api', 'false')
  console.log('模拟API模式已禁用')
}

export default {
  mockApiKeyAPI,
  mockSettingsAPI,
  shouldUseMockAPI
}
