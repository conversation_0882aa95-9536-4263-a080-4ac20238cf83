import { useEffect, useCallback, useRef } from 'react'

// 快捷键配置类型
export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  metaKey?: boolean
  description: string
  action: () => void
  preventDefault?: boolean
  stopPropagation?: boolean
  disabled?: boolean
  global?: boolean // 是否为全局快捷键（即使在输入框中也生效）
}

// 快捷键组合类型
export type ShortcutCombo = {
  key: string
  modifiers: string[]
}

// 解析快捷键字符串
function parseShortcut(shortcut: string): ShortcutCombo {
  const parts = shortcut.toLowerCase().split('+').map(part => part.trim())
  const key = parts.pop() || ''
  const modifiers = parts

  return { key, modifiers }
}

// 检查修饰键
function checkModifiers(event: KeyboardEvent, modifiers: string[]): boolean {
  const eventModifiers = {
    ctrl: event.ctrlKey,
    alt: event.altKey,
    shift: event.shiftKey,
    meta: event.metaKey,
    cmd: event.metaKey, // macOS Command key
    win: event.metaKey  // Windows key
  }

  // 检查所有需要的修饰键都被按下
  for (const modifier of modifiers) {
    if (!eventModifiers[modifier as keyof typeof eventModifiers]) {
      return false
    }
  }

  // 检查没有额外的修饰键被按下
  const requiredModifiers = new Set(modifiers)
  if (event.ctrlKey && !requiredModifiers.has('ctrl')) return false
  if (event.altKey && !requiredModifiers.has('alt')) return false
  if (event.shiftKey && !requiredModifiers.has('shift')) return false
  if (event.metaKey && !requiredModifiers.has('meta') && !requiredModifiers.has('cmd') && !requiredModifiers.has('win')) return false

  return true
}

// 检查是否在输入元素中
function isInputElement(element: Element): boolean {
  const tagName = element.tagName.toLowerCase()
  const inputTypes = ['input', 'textarea', 'select']
  
  if (inputTypes.includes(tagName)) return true
  
  // 检查 contenteditable
  if (element.getAttribute('contenteditable') === 'true') return true
  
  // 检查是否在可编辑区域内
  let parent = element.parentElement
  while (parent) {
    if (parent.getAttribute('contenteditable') === 'true') return true
    parent = parent.parentElement
  }
  
  return false
}

// 格式化快捷键显示
export function formatShortcut(shortcut: string): string {
  const { key, modifiers } = parseShortcut(shortcut)
  
  const modifierMap: Record<string, string> = {
    ctrl: navigator.platform.includes('Mac') ? '⌃' : 'Ctrl',
    alt: navigator.platform.includes('Mac') ? '⌥' : 'Alt',
    shift: navigator.platform.includes('Mac') ? '⇧' : 'Shift',
    meta: navigator.platform.includes('Mac') ? '⌘' : 'Win',
    cmd: '⌘',
    win: 'Win'
  }
  
  const keyMap: Record<string, string> = {
    ' ': 'Space',
    'arrowup': '↑',
    'arrowdown': '↓',
    'arrowleft': '←',
    'arrowright': '→',
    'enter': '↵',
    'escape': 'Esc',
    'backspace': '⌫',
    'delete': 'Del',
    'tab': '⇥'
  }
  
  const formattedModifiers = modifiers.map(mod => modifierMap[mod] || mod).join('')
  const formattedKey = keyMap[key.toLowerCase()] || key.toUpperCase()
  
  return formattedModifiers + formattedKey
}

// 快捷键Hook
export function useKeyboardShortcuts(shortcuts: Record<string, KeyboardShortcut>) {
  const shortcutsRef = useRef(shortcuts)
  
  // 更新快捷键引用
  useEffect(() => {
    shortcutsRef.current = shortcuts
  }, [shortcuts])

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const target = event.target as Element
    
    // 遍历所有快捷键
    Object.entries(shortcutsRef.current).forEach(([shortcutKey, config]) => {
      if (config.disabled) return
      
      const { key, modifiers } = parseShortcut(shortcutKey)
      
      // 检查按键是否匹配
      if (event.key.toLowerCase() !== key.toLowerCase()) return
      
      // 检查修饰键是否匹配
      if (!checkModifiers(event, modifiers)) return
      
      // 检查是否在输入元素中（除非是全局快捷键）
      if (!config.global && isInputElement(target)) return
      
      // 执行快捷键动作
      if (config.preventDefault !== false) {
        event.preventDefault()
      }
      
      if (config.stopPropagation) {
        event.stopPropagation()
      }
      
      config.action()
    })
  }, [])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown])

  return {
    formatShortcut,
    shortcuts: shortcutsRef.current
  }
}

// 全局快捷键Hook
export function useGlobalShortcuts() {
  const shortcuts: Record<string, KeyboardShortcut> = {
    // 导航快捷键
    'ctrl+1': {
      key: '1',
      ctrlKey: true,
      description: '跳转到仪表盘',
      action: () => window.location.hash = '#/dashboard',
      global: true
    },
    'ctrl+2': {
      key: '2',
      ctrlKey: true,
      description: '跳转到代理管理',
      action: () => window.location.hash = '#/proxies',
      global: true
    },
    'ctrl+3': {
      key: '3',
      ctrlKey: true,
      description: '跳转到任务管理',
      action: () => window.location.hash = '#/tasks',
      global: true
    },
    'ctrl+4': {
      key: '4',
      ctrlKey: true,
      description: '跳转到系统监控',
      action: () => window.location.hash = '#/monitoring',
      global: true
    },
    'ctrl+5': {
      key: '5',
      ctrlKey: true,
      description: '跳转到API密钥',
      action: () => window.location.hash = '#/keys',
      global: true
    },
    'ctrl+6': {
      key: '6',
      ctrlKey: true,
      description: '跳转到设置',
      action: () => window.location.hash = '#/settings',
      global: true
    },
    
    // 功能快捷键
    'ctrl+n': {
      key: 'n',
      ctrlKey: true,
      description: '新建（根据当前页面）',
      action: () => {
        const hash = window.location.hash
        if (hash.includes('proxies')) {
          // 触发添加代理
          const event = new CustomEvent('shortcut:new-proxy')
          window.dispatchEvent(event)
        } else if (hash.includes('tasks')) {
          // 触发创建任务
          const event = new CustomEvent('shortcut:new-task')
          window.dispatchEvent(event)
        } else if (hash.includes('apikeys')) {
          // 触发创建API密钥
          const event = new CustomEvent('shortcut:new-api-key')
          window.dispatchEvent(event)
        }
      },
      global: true
    },
    
    'ctrl+r': {
      key: 'r',
      ctrlKey: true,
      description: '刷新当前页面数据',
      action: () => {
        const event = new CustomEvent('shortcut:refresh')
        window.dispatchEvent(event)
      },
      global: true,
      preventDefault: true
    },
    
    'ctrl+f': {
      key: 'f',
      ctrlKey: true,
      description: '搜索',
      action: () => {
        const searchInput = document.querySelector('input[type="search"], input[placeholder*="搜索"], input[placeholder*="search"]') as HTMLInputElement
        if (searchInput) {
          searchInput.focus()
          searchInput.select()
        }
      },
      global: true,
      preventDefault: true
    },
    
    'escape': {
      key: 'Escape',
      description: '关闭模态框或取消操作',
      action: () => {
        const event = new CustomEvent('shortcut:escape')
        window.dispatchEvent(event)
      },
      global: true
    },
    
    // 主题切换
    'ctrl+shift+t': {
      key: 't',
      ctrlKey: true,
      shiftKey: true,
      description: '切换主题',
      action: () => {
        const event = new CustomEvent('shortcut:toggle-theme')
        window.dispatchEvent(event)
      },
      global: true
    },
    
    // 帮助
    'f1': {
      key: 'F1',
      description: '显示快捷键帮助',
      action: () => {
        const event = new CustomEvent('shortcut:show-help')
        window.dispatchEvent(event)
      },
      global: true,
      preventDefault: true
    }
  }

  return useKeyboardShortcuts(shortcuts)
}

// 快捷键帮助组件数据
export function getShortcutCategories() {
  return {
    navigation: {
      title: '导航',
      shortcuts: [
        { key: 'Ctrl+1', description: '仪表盘' },
        { key: 'Ctrl+2', description: '代理管理' },
        { key: 'Ctrl+3', description: '任务管理' },
        { key: 'Ctrl+4', description: '系统监控' },
        { key: 'Ctrl+5', description: 'API密钥' },
        { key: 'Ctrl+6', description: '设置' }
      ]
    },
    actions: {
      title: '操作',
      shortcuts: [
        { key: 'Ctrl+N', description: '新建' },
        { key: 'Ctrl+R', description: '刷新' },
        { key: 'Ctrl+F', description: '搜索' },
        { key: 'Escape', description: '取消/关闭' }
      ]
    },
    interface: {
      title: '界面',
      shortcuts: [
        { key: 'Ctrl+Shift+T', description: '切换主题' },
        { key: 'F1', description: '显示帮助' }
      ]
    }
  }
}

// 页面特定快捷键Hook
export function usePageShortcuts(pageShortcuts: Record<string, KeyboardShortcut>) {
  return useKeyboardShortcuts(pageShortcuts)
}

export default {
  useKeyboardShortcuts,
  useGlobalShortcuts,
  usePageShortcuts,
  formatShortcut,
  getShortcutCategories
}
