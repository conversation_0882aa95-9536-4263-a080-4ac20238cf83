import { useState, useEffect, useCallback, useMemo } from 'react'
import { ProxyService } from '../services/proxyService'
import { TagService } from '../services/tagService'
import { specialToasts } from '../utils/toastConfig'
import type {
  Proxy,
  ProxyFormData,
  ProxyStats,
  ProxyLocationStats,
  ProxyGroupByLocation,
  ProxyTag,
  LocationFilter,
} from '../types'

export interface UseProxyManagementOptions {
  autoRefresh?: boolean
  refreshInterval?: number
  enableCache?: boolean
}

export interface ProxyManagementState {
  // 数据状态
  proxies: Proxy[]
  stats: ProxyStats | null
  locationStats: ProxyLocationStats | null
  locationGroups: ProxyGroupByLocation[]
  tags: ProxyTag[]

  // 加载状态
  loading: boolean
  refreshing: boolean
  operationLoading: boolean

  // 错误状态
  error: string | null

  // 筛选和搜索状态
  searchTerm: string
  locationFilter: LocationFilter
  selectedTags: string[]
  qualityFilter: { min?: number; max?: number }

  // 选择状态
  selectedProxies: string[]
  isSelectMode: boolean

  // 视图状态
  viewMode: 'list' | 'grid' | 'location'
  groupBy: 'country' | 'city'
}

export interface ProxyManagementActions {
  // 数据获取
  fetchProxies: (showLoading?: boolean) => Promise<void>
  fetchStats: () => Promise<void>
  fetchLocationStats: () => Promise<void>
  fetchLocationGroups: () => Promise<void>
  fetchTags: () => Promise<void>
  refreshAll: () => Promise<void>

  // 代理操作
  addProxy: (proxyData: ProxyFormData) => Promise<boolean>
  deleteProxy: (proxyId: string) => Promise<boolean>
  batchDeleteProxies: (proxyIds: string[]) => Promise<boolean>
  batchImportProxies: (proxies: ProxyFormData[]) => Promise<boolean>

  // 健康检查
  healthCheck: (proxyId: string) => Promise<boolean>
  batchHealthCheck: (proxyIds: string[]) => Promise<boolean>
  healthCheckAll: () => Promise<boolean>

  // 质量评估
  assessQuality: (proxyId: string) => Promise<boolean>
  batchAssessQuality: (proxyIds: string[]) => Promise<boolean>

  // 标签操作
  assignTags: (proxyIds: string[], tagIds: string[]) => Promise<boolean>
  removeProxyTags: (proxyId: string, tagIds: string[]) => Promise<boolean>

  // 筛选和搜索
  setSearchTerm: (term: string) => void
  setLocationFilter: (filter: LocationFilter) => void
  setSelectedTags: (tags: string[]) => void
  setQualityFilter: (filter: { min?: number; max?: number }) => void
  clearFilters: () => void

  // 选择操作
  selectProxy: (proxyId: string) => void
  selectMultipleProxies: (proxyIds: string[]) => void
  selectAllProxies: () => void
  clearSelection: () => void
  toggleSelectMode: () => void

  // 视图操作
  setViewMode: (mode: 'list' | 'grid' | 'location') => void
  setGroupBy: (groupBy: 'country' | 'city') => void
}

export const useProxyManagement = (
  options: UseProxyManagementOptions = {}
): ProxyManagementState & ProxyManagementActions => {
  const {
    autoRefresh = false,
    refreshInterval = 30000, // 30秒
    enableCache = true,
  } = options

  // 状态定义
  const [state, setState] = useState<ProxyManagementState>({
    // 数据状态
    proxies: [],
    stats: null,
    locationStats: null,
    locationGroups: [],
    tags: [],

    // 加载状态
    loading: true,
    refreshing: false,
    operationLoading: false,

    // 错误状态
    error: null,

    // 筛选和搜索状态
    searchTerm: '',
    locationFilter: { countries: [], cities: [], searchTerm: '' },
    selectedTags: [],
    qualityFilter: {},

    // 选择状态
    selectedProxies: [],
    isSelectMode: false,

    // 视图状态
    viewMode: 'list',
    groupBy: 'country',
  })

  // 更新状态的辅助函数
  const updateState = useCallback((updates: Partial<ProxyManagementState>) => {
    setState((prev) => ({ ...prev, ...updates }))
  }, [])

  // 数据获取方法
  const fetchProxies = useCallback(
    async (showLoading = true) => {
      try {
        if (showLoading) {
          updateState({ loading: true, error: null })
        } else {
          updateState({ refreshing: true, error: null })
        }

        const proxies = await ProxyService.getProxies(enableCache)
        updateState({ proxies })
      } catch (error) {
        const errorMessage = '获取代理列表失败'
        updateState({ error: errorMessage })
        specialToasts.operationFailed('获取代理列表')
      } finally {
        updateState({ loading: false, refreshing: false })
      }
    },
    [enableCache, updateState]
  )

  const fetchStats = useCallback(async () => {
    try {
      const stats = await ProxyService.getProxyStats()
      updateState({ stats })
    } catch (error) {
      console.error('Failed to fetch proxy stats:', error)
    }
  }, [updateState])

  const fetchLocationStats = useCallback(async () => {
    try {
      const locationStats = await ProxyService.getProxyLocationStats()
      updateState({ locationStats })
    } catch (error) {
      console.error('Failed to fetch location stats:', error)
    }
  }, [updateState])

  const fetchLocationGroups = useCallback(async () => {
    try {
      const locationGroups = await ProxyService.groupProxiesByLocation(state.groupBy)
      updateState({ locationGroups })
    } catch (error) {
      console.error('Failed to fetch location groups:', error)
    }
  }, [state.groupBy, updateState])

  const fetchTags = useCallback(async () => {
    try {
      const tags = await TagService.getTags(enableCache)
      updateState({ tags })
    } catch (error) {
      console.error('Failed to fetch tags:', error)
    }
  }, [enableCache, updateState])

  const refreshAll = useCallback(async () => {
    updateState({ refreshing: true })
    await Promise.all([
      fetchProxies(false),
      fetchStats(),
      fetchLocationStats(),
      fetchLocationGroups(),
      fetchTags(),
    ])
    updateState({ refreshing: false })
  }, [fetchProxies, fetchStats, fetchLocationStats, fetchLocationGroups, fetchTags, updateState])

  // 代理操作方法
  const addProxy = useCallback(
    async (proxyData: ProxyFormData): Promise<boolean> => {
      updateState({ operationLoading: true })
      try {
        const result = await ProxyService.addProxy(proxyData)
        if (result) {
          await fetchProxies(false)
          await fetchStats()
          return true
        }
        return false
      } finally {
        updateState({ operationLoading: false })
      }
    },
    [fetchProxies, fetchStats, updateState]
  )

  const deleteProxy = useCallback(
    async (proxyId: string): Promise<boolean> => {
      updateState({ operationLoading: true })
      try {
        const result = await ProxyService.deleteProxy(proxyId)
        if (result) {
          await fetchProxies(false)
          await fetchStats()
          // 从选中列表中移除
          updateState({
            selectedProxies: state.selectedProxies.filter((id) => id !== proxyId),
          })
          return true
        }
        return false
      } finally {
        updateState({ operationLoading: false })
      }
    },
    [fetchProxies, fetchStats, state.selectedProxies, updateState]
  )

  const batchDeleteProxies = useCallback(
    async (proxyIds: string[]): Promise<boolean> => {
      updateState({ operationLoading: true })
      try {
        // 这里需要实现批量删除的逻辑
        // 目前后端可能没有批量删除接口，所以使用循环删除
        let successCount = 0
        for (const proxyId of proxyIds) {
          const result = await ProxyService.deleteProxy(proxyId)
          if (result) successCount++
        }

        if (successCount > 0) {
          await fetchProxies(false)
          await fetchStats()
          updateState({ selectedProxies: [] })
          specialToasts.batchOperationComplete(successCount, proxyIds.length)
        }

        return successCount === proxyIds.length
      } finally {
        updateState({ operationLoading: false })
      }
    },
    [fetchProxies, fetchStats, updateState]
  )

  const batchImportProxies = useCallback(
    async (proxies: ProxyFormData[]): Promise<boolean> => {
      updateState({ operationLoading: true })
      try {
        const result = await ProxyService.batchImportProxies(proxies)
        if (result) {
          await fetchProxies(false)
          await fetchStats()
          return true
        }
        return false
      } finally {
        updateState({ operationLoading: false })
      }
    },
    [fetchProxies, fetchStats, updateState]
  )

  // 健康检查方法
  const healthCheck = useCallback(
    async (proxyId: string): Promise<boolean> => {
      const result = await ProxyService.healthCheck(proxyId)
      if (result) {
        // 延迟刷新以获取最新状态
        setTimeout(() => fetchProxies(false), 1000)
      }
      return result
    },
    [fetchProxies]
  )

  const batchHealthCheck = useCallback(
    async (proxyIds: string[]): Promise<boolean> => {
      const result = await ProxyService.batchHealthCheck(proxyIds)
      if (result) {
        // 延迟刷新以获取最新状态
        setTimeout(() => fetchProxies(false), 1000)
      }
      return result
    },
    [fetchProxies]
  )

  const healthCheckAll = useCallback(async (): Promise<boolean> => {
    const result = await ProxyService.healthCheckAll()
    if (result) {
      // 延迟刷新以获取最新状态
      setTimeout(() => fetchProxies(false), 1000)
    }
    return result
  }, [fetchProxies])

  // 质量评估方法
  const assessQuality = useCallback(
    async (proxyId: string): Promise<boolean> => {
      const result = await ProxyService.assessProxyQuality(proxyId)
      if (result) {
        setTimeout(() => fetchProxies(false), 1000)
      }
      return !!result
    },
    [fetchProxies]
  )

  const batchAssessQuality = useCallback(
    async (proxyIds: string[]): Promise<boolean> => {
      const result = await ProxyService.batchAssessQuality(proxyIds)
      if (result) {
        setTimeout(() => fetchProxies(false), 1000)
      }
      return result
    },
    [fetchProxies]
  )

  // 标签操作方法
  const assignTags = useCallback(
    async (proxyIds: string[], tagIds: string[]): Promise<boolean> => {
      const result = await TagService.assignTags({ proxy_ids: proxyIds, tag_ids: tagIds })
      if (result) {
        await fetchProxies(false)
      }
      return result
    },
    [fetchProxies]
  )

  const removeProxyTags = useCallback(
    async (proxyId: string, tagIds: string[]): Promise<boolean> => {
      const result = await ProxyService.removeProxyTags(proxyId, tagIds)
      if (result) {
        await fetchProxies(false)
      }
      return result
    },
    [fetchProxies]
  )

  // 筛选和搜索方法
  const setSearchTerm = useCallback(
    (term: string) => {
      updateState({ searchTerm: term })
    },
    [updateState]
  )

  const setLocationFilter = useCallback(
    (filter: LocationFilter) => {
      updateState({ locationFilter: filter })
    },
    [updateState]
  )

  const setSelectedTags = useCallback(
    (tags: string[]) => {
      updateState({ selectedTags: tags })
    },
    [updateState]
  )

  const setQualityFilter = useCallback(
    (filter: { min?: number; max?: number }) => {
      updateState({ qualityFilter: filter })
    },
    [updateState]
  )

  const clearFilters = useCallback(() => {
    updateState({
      searchTerm: '',
      locationFilter: { countries: [], cities: [], searchTerm: '' },
      selectedTags: [],
      qualityFilter: {},
    })
  }, [updateState])

  // 选择操作方法
  const selectProxy = useCallback(
    (proxyId: string) => {
      updateState({
        selectedProxies: state.selectedProxies.includes(proxyId)
          ? state.selectedProxies.filter((id) => id !== proxyId)
          : [...state.selectedProxies, proxyId],
      })
    },
    [state.selectedProxies, updateState]
  )

  const selectMultipleProxies = useCallback(
    (proxyIds: string[]) => {
      updateState({ selectedProxies: proxyIds })
    },
    [updateState]
  )

  const selectAllProxies = useCallback(() => {
    updateState({ selectedProxies: state.proxies.map((p) => p.id) })
  }, [state.proxies, updateState])

  const clearSelection = useCallback(() => {
    updateState({ selectedProxies: [] })
  }, [updateState])

  const toggleSelectMode = useCallback(() => {
    updateState({
      isSelectMode: !state.isSelectMode,
      selectedProxies: !state.isSelectMode ? [] : state.selectedProxies,
    })
  }, [state.isSelectMode, state.selectedProxies, updateState])

  // 视图操作方法
  const setViewMode = useCallback(
    (mode: 'list' | 'grid' | 'location') => {
      updateState({ viewMode: mode })
    },
    [updateState]
  )

  const setGroupBy = useCallback(
    (groupBy: 'country' | 'city') => {
      updateState({ groupBy })
      // 重新获取分组数据
      fetchLocationGroups()
    },
    [updateState, fetchLocationGroups]
  )

  // 筛选后的代理列表
  const filteredProxies = useMemo(() => {
    let filtered = [...state.proxies]

    // 搜索筛选
    if (state.searchTerm) {
      const term = state.searchTerm.toLowerCase()
      filtered = filtered.filter(
        (proxy) =>
          proxy.host.toLowerCase().includes(term) ||
          proxy.type.toLowerCase().includes(term) ||
          proxy.country_code?.toLowerCase().includes(term) ||
          proxy.city_name?.toLowerCase().includes(term)
      )
    }

    // 地理位置筛选
    if (state.locationFilter.countries.length > 0) {
      filtered = filtered.filter(
        (proxy) => proxy.country_code && state.locationFilter.countries.includes(proxy.country_code)
      )
    }

    if (state.locationFilter.cities.length > 0) {
      filtered = filtered.filter(
        (proxy) =>
          proxy.city_name &&
          state.locationFilter.cities.some((city) => city.includes(proxy.city_name!))
      )
    }

    // 标签筛选
    if (state.selectedTags.length > 0) {
      filtered = filtered.filter(
        (proxy) => proxy.tags && proxy.tags.some((tag) => state.selectedTags.includes(tag.id))
      )
    }

    // 质量筛选
    if (state.qualityFilter.min !== undefined || state.qualityFilter.max !== undefined) {
      filtered = filtered.filter((proxy) => {
        const score = proxy.quality_score || 0
        const minMatch = state.qualityFilter.min === undefined || score >= state.qualityFilter.min
        const maxMatch = state.qualityFilter.max === undefined || score <= state.qualityFilter.max
        return minMatch && maxMatch
      })
    }

    return filtered
  }, [
    state.proxies,
    state.searchTerm,
    state.locationFilter,
    state.selectedTags,
    state.qualityFilter,
  ])

  // 初始化和自动刷新
  useEffect(() => {
    fetchProxies()
    fetchStats()
    fetchLocationStats()
    fetchTags()
  }, [])

  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(() => {
        refreshAll()
      }, refreshInterval)

      return () => clearInterval(interval)
    }
  }, [autoRefresh, refreshInterval, refreshAll])

  return {
    // 状态
    ...state,
    proxies: filteredProxies, // 返回筛选后的代理列表

    // 操作方法
    fetchProxies,
    fetchStats,
    fetchLocationStats,
    fetchLocationGroups,
    fetchTags,
    refreshAll,
    addProxy,
    deleteProxy,
    batchDeleteProxies,
    batchImportProxies,
    healthCheck,
    batchHealthCheck,
    healthCheckAll,
    assessQuality,
    batchAssessQuality,
    assignTags,
    removeProxyTags,
    setSearchTerm,
    setLocationFilter,
    setSelectedTags,
    setQualityFilter,
    clearFilters,
    selectProxy,
    selectMultipleProxies,
    selectAllProxies,
    clearSelection,
    toggleSelectMode,
    setViewMode,
    setGroupBy,
  }
}