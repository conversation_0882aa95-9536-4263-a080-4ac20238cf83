import { useState, useEffect, useCallback, useRef } from 'react'
import {
  websocketService,
  SystemMetricsUpdate,
  TaskStatusUpdate,
  ProxyStatusUpdate,
  SystemEvent,
  enableAutoConnect,
  disableAutoConnect,
} from '../services/websocketService'

export interface RealtimeConnectionState {
  isConnected: boolean
  isConnecting: boolean
  connectionState: string
  lastConnected: Date | null
  reconnectAttempts: number
}

export interface UseRealtimeDataOptions {
  autoConnect?: boolean
  subscribeToMetrics?: boolean
  subscribeToTasks?: boolean
  subscribeToProxies?: boolean
  subscribeToEvents?: boolean
}

export interface UseRealtimeDataReturn {
  connectionState: RealtimeConnectionState
  systemMetrics: SystemMetricsUpdate | null
  taskUpdates: TaskStatusUpdate[]
  proxyUpdates: ProxyStatusUpdate[]
  systemEvents: SystemEvent[]
  connect: () => void
  disconnect: () => void
  clearTaskUpdates: () => void
  clearProxyUpdates: () => void
  clearSystemEvents: () => void
}

export const useRealtimeData = (options: UseRealtimeDataOptions = {}): UseRealtimeDataReturn => {
  const {
    autoConnect = true,
    subscribeToMetrics = true,
    subscribeToTasks = true,
    subscribeToProxies = true,
    subscribeToEvents = true,
  } = options

  // 连接状态
  const [connectionState, setConnectionState] = useState<RealtimeConnectionState>({
    isConnected: false,
    isConnecting: false,
    connectionState: 'disconnected',
    lastConnected: null,
    reconnectAttempts: 0,
  })

  // 实时数据状态
  const [systemMetrics, setSystemMetrics] = useState<SystemMetricsUpdate | null>(null)
  const [taskUpdates, setTaskUpdates] = useState<TaskStatusUpdate[]>([])
  const [proxyUpdates, setProxyUpdates] = useState<ProxyStatusUpdate[]>([])
  const [systemEvents, setSystemEvents] = useState<SystemEvent[]>([])

  // 使用ref来避免闭包问题
  const taskUpdatesRef = useRef<TaskStatusUpdate[]>([])
  const proxyUpdatesRef = useRef<ProxyStatusUpdate[]>([])
  const systemEventsRef = useRef<SystemEvent[]>([])

  // 更新连接状态
  const updateConnectionState = useCallback(() => {
    const state = websocketService.getConnectionState()
    setConnectionState((prev) => ({
      ...prev,
      isConnected: state === 'connected',
      isConnecting: state === 'connecting',
      connectionState: state,
    }))
  }, [])

  // 连接事件处理
  const handleConnected = useCallback(() => {
    setConnectionState((prev) => ({
      ...prev,
      isConnected: true,
      isConnecting: false,
      connectionState: 'connected',
      lastConnected: new Date(),
      reconnectAttempts: 0,
    }))

    // 订阅数据流
    const topics = []
    if (subscribeToMetrics) topics.push('system_metrics')
    if (subscribeToTasks) topics.push('task_status')
    if (subscribeToProxies) topics.push('proxy_status')
    if (subscribeToEvents) topics.push('system_events')

    if (topics.length > 0) {
      websocketService.subscribe(topics)
    }
  }, [subscribeToMetrics, subscribeToTasks, subscribeToProxies, subscribeToEvents])

  const handleDisconnected = useCallback(() => {
    setConnectionState((prev) => ({
      ...prev,
      isConnected: false,
      isConnecting: false,
      connectionState: 'disconnected',
    }))
  }, [])

  const handleError = useCallback((error: any) => {
    console.error('WebSocket error in hook:', error)
  }, [])

  // 数据更新处理
  const handleSystemMetrics = useCallback((data: SystemMetricsUpdate) => {
    setSystemMetrics(data)
  }, [])

  const handleTaskStatus = useCallback((data: TaskStatusUpdate) => {
    setTaskUpdates((prev) => {
      const newUpdates = [data, ...prev.slice(0, 99)] // 保留最近100条更新
      taskUpdatesRef.current = newUpdates
      return newUpdates
    })
  }, [])

  const handleProxyStatus = useCallback((data: ProxyStatusUpdate) => {
    setProxyUpdates((prev) => {
      const newUpdates = [data, ...prev.slice(0, 99)] // 保留最近100条更新
      proxyUpdatesRef.current = newUpdates
      return newUpdates
    })
  }, [])

  const handleSystemEvent = useCallback((data: SystemEvent) => {
    setSystemEvents((prev) => {
      const newEvents = [data, ...prev.slice(0, 49)] // 保留最近50条事件
      systemEventsRef.current = newEvents
      return newEvents
    })
  }, [])

  // 清理函数
  const clearTaskUpdates = useCallback(() => {
    setTaskUpdates([])
    taskUpdatesRef.current = []
  }, [])

  const clearProxyUpdates = useCallback(() => {
    setProxyUpdates([])
    proxyUpdatesRef.current = []
  }, [])

  const clearSystemEvents = useCallback(() => {
    setSystemEvents([])
    systemEventsRef.current = []
  }, [])

  // 连接控制
  const connect = useCallback(() => {
    websocketService.connect()
  }, [])

  const disconnect = useCallback(() => {
    websocketService.disconnect()
  }, [])

  // 设置事件监听器
  useEffect(() => {
    const unsubscribers: (() => void)[] = []

    // 连接事件
    websocketService.on('connected', handleConnected)
    websocketService.on('disconnected', handleDisconnected)
    websocketService.on('error', handleError)

    unsubscribers.push(
      () => websocketService.off('connected', handleConnected),
      () => websocketService.off('disconnected', handleDisconnected),
      () => websocketService.off('error', handleError)
    )

    // 数据事件
    if (subscribeToMetrics) {
      unsubscribers.push(websocketService.subscribeToSystemMetrics(handleSystemMetrics))
    }

    if (subscribeToTasks) {
      unsubscribers.push(websocketService.subscribeToTaskStatus(handleTaskStatus))
    }

    if (subscribeToProxies) {
      unsubscribers.push(websocketService.subscribeToProxyStatus(handleProxyStatus))
    }

    if (subscribeToEvents) {
      unsubscribers.push(websocketService.subscribeToSystemEvents(handleSystemEvent))
    }

    // 初始化连接状态
    updateConnectionState()

    // 自动连接
    if (autoConnect) {
      enableAutoConnect()
    }

    // 清理函数
    return () => {
      unsubscribers.forEach((unsubscribe) => unsubscribe())
      if (autoConnect) {
        disableAutoConnect()
      }
    }
  }, [
    autoConnect,
    subscribeToMetrics,
    subscribeToTasks,
    subscribeToProxies,
    subscribeToEvents,
    handleConnected,
    handleDisconnected,
    handleError,
    handleSystemMetrics,
    handleTaskStatus,
    handleProxyStatus,
    handleSystemEvent,
    updateConnectionState,
  ])

  return {
    connectionState,
    systemMetrics,
    taskUpdates,
    proxyUpdates,
    systemEvents,
    connect,
    disconnect,
    clearTaskUpdates,
    clearProxyUpdates,
    clearSystemEvents,
  }
}

// 专门用于系统监控的Hook
export const useSystemMonitoring = () => {
  return useRealtimeData({
    autoConnect: true,
    subscribeToMetrics: true,
    subscribeToTasks: false,
    subscribeToProxies: false,
    subscribeToEvents: true,
  })
}

// 专门用于任务监控的Hook
export const useTaskMonitoring = () => {
  return useRealtimeData({
    autoConnect: true,
    subscribeToMetrics: false,
    subscribeToTasks: true,
    subscribeToProxies: false,
    subscribeToEvents: false,
  })
}

// 专门用于代理监控的Hook
export const useProxyMonitoring = () => {
  return useRealtimeData({
    autoConnect: true,
    subscribeToMetrics: false,
    subscribeToTasks: false,
    subscribeToProxies: true,
    subscribeToEvents: false,
  })
}

export default useRealtimeData