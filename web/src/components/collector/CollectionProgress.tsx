import React, { useState, useEffect, useCallback } from 'react'
import {
  Play,
  Pause,
  Square,
  Refresh<PERSON><PERSON>,
  Clock,
  TrendingUp,
  MemoryStick,
  Cpu,
  Users,
  AlertTriangle,
  CheckCircle,
  XCircle,
} from 'lucide-react'

interface CollectionProgress {
  task_id: string
  phase: string
  processed_count: number
  total_count: number
  progress_percent: number
  speed: number
  eta: string
  start_time: string
  last_update: string
  status: string
  message: string
  memory_usage_mb: number
  cpu_usage: number
  worker_count: number
}

interface CollectionTask {
  id: string
  status: string
  progress: CollectionProgress
  created_at: string
  started_at?: string
  completed_at?: string
  error?: string
}

interface CollectionProgressProps {
  taskId: string
  autoRefresh?: boolean
  refreshInterval?: number
  onTaskUpdate?: (task: CollectionTask) => void
}

const CollectionProgressComponent: React.FC<CollectionProgressProps> = ({
  taskId,
  autoRefresh = true,
  refreshInterval = 1000,
  onTaskUpdate,
}) => {
  const [task, setTask] = useState<CollectionTask | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // 获取任务详情
  const fetchTask = useCallback(async () => {
    try {
      const response = await fetch(`/api/v1/collector/tasks/${taskId}`)
      const data = await response.json()

      if (data.success) {
        setTask(data.data)
        setError(null)
        onTaskUpdate?.(data.data)
      } else {
        setError(data.error || 'Failed to fetch task')
      }
    } catch (err) {
      setError('Network error')
    } finally {
      setIsLoading(false)
    }
  }, [taskId, onTaskUpdate])

  // SSE 连接进度流
  useEffect(() => {
    if (!autoRefresh || !taskId) return

    const eventSource = new EventSource(`/api/v1/collector/tasks/${taskId}/progress/stream`)

    eventSource.onopen = () => {
      setIsConnected(true)
      setError(null)
    }

    eventSource.onmessage = (event) => {
      try {
        const progressData = JSON.parse(event.data)
        setTask((prevTask) => ({
          ...prevTask!,
          progress: progressData,
        }))
      } catch (err) {
        console.error('Failed to parse progress data:', err)
      }
    }

    eventSource.onerror = () => {
      setIsConnected(false)
      setError('Connection lost')
    }

    return () => {
      eventSource.close()
    }
  }, [taskId, autoRefresh])

  // 初始加载
  useEffect(() => {
    fetchTask()
  }, [fetchTask])

  // 任务控制操作
  const handleTaskControl = async (action: 'pause' | 'resume' | 'cancel') => {
    try {
      const response = await fetch(`/api/v1/collector/tasks/${taskId}/${action}`, {
        method: 'POST',
      })
      const data = await response.json()

      if (data.success) {
        // 刷新任务状态
        await fetchTask()
      } else {
        setError(data.error || `Failed to ${action} task`)
      }
    } catch (err) {
      setError(`Network error during ${action}`)
    }
  }

  // 格式化时间
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`
    } else {
      return `${secs}s`
    }
  }

  // 格式化速度
  const formatSpeed = (speed: number): string => {
    if (speed >= 1000) {
      return `${(speed / 1000).toFixed(1)}k/s`
    }
    return `${speed.toFixed(0)}/s`
  }

  // 获取状态图标和颜色
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'running':
        return { icon: Play, color: 'text-green-500', bgColor: 'bg-green-100' }
      case 'paused':
        return { icon: Pause, color: 'text-yellow-500', bgColor: 'bg-yellow-100' }
      case 'completed':
        return { icon: CheckCircle, color: 'text-blue-500', bgColor: 'bg-blue-100' }
      case 'error':
        return { icon: XCircle, color: 'text-red-500', bgColor: 'bg-red-100' }
      case 'cancelled':
        return { icon: Square, color: 'text-gray-500', bgColor: 'bg-gray-100' }
      default:
        return { icon: RefreshCw, color: 'text-gray-500', bgColor: 'bg-gray-100' }
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin text-gray-500" />
        <span className="ml-2 text-gray-600">Loading task...</span>
      </div>
    )
  }

  if (error || !task) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
          <span className="text-red-700">{error || 'Task not found'}</span>
        </div>
      </div>
    )
  }

  const { progress } = task
  const statusInfo = getStatusInfo(progress.status)
  const StatusIcon = statusInfo.icon

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 space-y-6">
      {/* 任务头部信息 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-full ${statusInfo.bgColor}`}>
            <StatusIcon className={`h-5 w-5 ${statusInfo.color}`} />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Collection Task {task.id.slice(-8)}
            </h3>
            <p className="text-sm text-gray-600">
              Phase: {progress.phase} • Status: {progress.status}
            </p>
          </div>
        </div>

        {/* 连接状态指示器 */}
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-xs text-gray-500">{isConnected ? 'Live' : 'Disconnected'}</span>
        </div>
      </div>

      {/* 进度条 */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">
            Progress: {progress.processed_count.toLocaleString()} /{' '}
            {progress.total_count.toLocaleString()}
          </span>
          <span className="font-medium text-gray-900">{progress.progress_percent.toFixed(1)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div
            className="bg-blue-500 h-3 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${Math.min(progress.progress_percent, 100)}%` }}
          />
        </div>
      </div>

      {/* 统计信息网格 */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <TrendingUp className="h-4 w-4 text-blue-500" />
            <span className="text-xs font-medium text-gray-600">Speed</span>
          </div>
          <p className="text-lg font-semibold text-gray-900 mt-1">{formatSpeed(progress.speed)}</p>
        </div>

        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-green-500" />
            <span className="text-xs font-medium text-gray-600">ETA</span>
          </div>
          <p className="text-lg font-semibold text-gray-900 mt-1">{progress.eta || 'N/A'}</p>
        </div>

        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <MemoryStick className="h-4 w-4 text-purple-500" />
            <span className="text-xs font-medium text-gray-600">Memory</span>
          </div>
          <p className="text-lg font-semibold text-gray-900 mt-1">
            {progress.memory_usage_mb.toFixed(0)}MB
          </p>
        </div>

        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4 text-orange-500" />
            <span className="text-xs font-medium text-gray-600">Workers</span>
          </div>
          <p className="text-lg font-semibold text-gray-900 mt-1">{progress.worker_count}</p>
        </div>
      </div>

      {/* 任务控制按钮 */}
      <div className="flex items-center justify-between pt-4 border-t">
        <div className="flex space-x-2">
          {progress.status === 'running' && (
            <button
              onClick={() => handleTaskControl('pause')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Pause className="h-4 w-4 mr-1" />
              Pause
            </button>
          )}

          {progress.status === 'paused' && (
            <button
              onClick={() => handleTaskControl('resume')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Play className="h-4 w-4 mr-1" />
              Resume
            </button>
          )}

          {['running', 'paused'].includes(progress.status) && (
            <button
              onClick={() => handleTaskControl('cancel')}
              className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <Square className="h-4 w-4 mr-1" />
              Cancel
            </button>
          )}
        </div>

        <div className="text-xs text-gray-500">
          Last updated: {new Date(progress.last_update).toLocaleTimeString()}
        </div>
      </div>

      {/* 错误信息 */}
      {task.error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-red-800">Error</h4>
              <p className="text-sm text-red-700 mt-1">{task.error}</p>
            </div>
          </div>
        </div>
      )}

      {/* 消息信息 */}
      {progress.message && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <p className="text-sm text-blue-700">{progress.message}</p>
        </div>
      )}
    </div>
  )
}

export default CollectionProgressComponent