import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Tag, X } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { tagAPI } from '../../services/api'
import type { ProxyTag, ProxyTagRequest } from '../../types'

interface TagManagerProps {
  onTagSelect?: (tag: ProxyTag) => void
  selectedTags?: ProxyTag[]
  showActions?: boolean
}

const TagManager: React.FC<TagManagerProps> = ({
  onTagSelect,
  selectedTags = [],
  showActions = true,
}) => {
  const [tags, setTags] = useState<ProxyTag[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingTag, setEditingTag] = useState<ProxyTag | null>(null)
  const [formData, setFormData] = useState<ProxyTagRequest>({
    name: '',
    description: '',
    color: '#3B82F6',
  })

  // 预定义颜色
  const predefinedColors = [
    '#3B82F6',
    '#EF4444',
    '#10B981',
    '#F59E0B',
    '#8B5CF6',
    '#EC4899',
    '#06B6D4',
    '#84CC16',
    '#F97316',
    '#6B7280',
  ]

  useEffect(() => {
    fetchTags()
  }, [])

  const fetchTags = async () => {
    try {
      setLoading(true)
      const response = await tagAPI.getTags()
      setTags(response.data.data || [])
    } catch (error) {
      toast.error('获取标签失败')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (editingTag) {
        // 更新标签
        await tagAPI.updateTag(editingTag.id, formData)
        toast.success('标签更新成功')
      } else {
        // 创建标签
        await tagAPI.createTag(formData)
        toast.success('标签创建成功')
      }

      await fetchTags()
      resetForm()
    } catch (error) {
      toast.error(editingTag ? '标签更新失败' : '标签创建失败')
    }
  }

  const handleDelete = async (tag: ProxyTag) => {
    if (!confirm(`确定要删除标签 "${tag.name}" 吗？`)) {
      return
    }

    try {
      await tagAPI.deleteTag(tag.id)
      toast.success('标签删除成功')
      await fetchTags()
    } catch (error) {
      toast.error('标签删除失败')
    }
  }

  const handleEdit = (tag: ProxyTag) => {
    setEditingTag(tag)
    setFormData({
      name: tag.name,
      description: tag.description || '',
      color: tag.color,
    })
    setShowCreateForm(true)
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      color: '#3B82F6',
    })
    setEditingTag(null)
    setShowCreateForm(false)
  }

  const isTagSelected = (tag: ProxyTag) => {
    return selectedTags.some((selected) => selected.id === tag.id)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* 标题和创建按钮 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 flex items-center">
          <Tag className="h-5 w-5 mr-2" />
          代理标签管理
        </h3>
        {showActions && (
          <button onClick={() => setShowCreateForm(true)} className="btn-sm btn-primary">
            <Plus className="h-4 w-4 mr-1" />
            创建标签
          </button>
        )}
      </div>

      {/* 创建/编辑表单 */}
      {showCreateForm && (
        <div className="card">
          <div className="card-header">
            <h4 className="text-md font-medium">{editingTag ? '编辑标签' : '创建标签'}</h4>
            <button onClick={resetForm} className="text-gray-400 hover:text-gray-600">
              <X className="h-5 w-5" />
            </button>
          </div>
          <div className="card-body">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">标签名称 *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="input"
                  placeholder="输入标签名称"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="input"
                  rows={2}
                  placeholder="输入标签描述（可选）"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">颜色</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="color"
                    value={formData.color}
                    onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                    className="w-10 h-10 border border-gray-300 rounded cursor-pointer"
                  />
                  <div className="flex space-x-1">
                    {predefinedColors.map((color) => (
                      <button
                        key={color}
                        type="button"
                        onClick={() => setFormData({ ...formData, color })}
                        className={`w-6 h-6 rounded border-2 ${
                          formData.color === color ? 'border-gray-800' : 'border-gray-300'
                        }`}
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex space-x-2">
                <button type="submit" className="btn-sm btn-primary">
                  {editingTag ? '更新' : '创建'}
                </button>
                <button type="button" onClick={resetForm} className="btn-sm btn-outline">
                  取消
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 标签列表 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
        {tags.map((tag) => (
          <div
            key={tag.id}
            className={`p-3 border rounded-lg cursor-pointer transition-all ${
              isTagSelected(tag)
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => onTagSelect?.(tag)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: tag.color }} />
                <span className="font-medium text-gray-900">{tag.name}</span>
              </div>
              {showActions && (
                <div className="flex space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleEdit(tag)
                    }}
                    className="text-gray-400 hover:text-blue-600"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDelete(tag)
                    }}
                    className="text-gray-400 hover:text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              )}
            </div>
            {tag.description && <p className="text-sm text-gray-600 mt-1">{tag.description}</p>}
          </div>
        ))}
      </div>

      {tags.length === 0 && (
        <div className="text-center py-8">
          <Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">暂无标签</p>
          {showActions && (
            <button onClick={() => setShowCreateForm(true)} className="btn-sm btn-primary mt-2">
              创建第一个标签
            </button>
          )}
        </div>
      )}
    </div>
  )
}

export default TagManager