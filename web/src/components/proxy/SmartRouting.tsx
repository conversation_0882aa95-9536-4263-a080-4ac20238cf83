import React, { useState, useEffect } from 'react'
import { Filter, Zap, Target, Settings } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { proxyAPI, tagAPI } from '../../services/api'
import type { Proxy, ProxyTag, ProxyFilterByTags } from '../../types'
import QualityScoreDisplay from './QualityScoreDisplay'

const SmartRouting: React.FC = () => {
  const [filter, setFilter] = useState<ProxyFilterByTags>({
    strategy: 'quality',
  })
  const [availableTags, setAvailableTags] = useState<ProxyTag[]>([])
  const [selectedProxy, setSelectedProxy] = useState<Proxy | null>(null)
  const [loading, setLoading] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)

  // 策略选项
  const strategies = [
    { value: 'quality', label: '质量优先', description: '选择质量评分最高的代理' },
    { value: 'speed', label: '速度优先', description: '选择响应时间最快的代理' },
    { value: 'stability', label: '稳定性优先', description: '选择稳定性最高的代理' },
    { value: 'priority', label: '优先级优先', description: '选择优先级最高的代理' },
    { value: 'round_robin', label: '轮询', description: '按顺序轮流选择代理' },
    { value: 'random', label: '随机', description: '随机选择代理' },
    { value: 'least_used', label: '最少使用', description: '选择使用次数最少的代理' },
  ]

  // 预定义场景
  const scenarios = [
    'web-scraping',
    'api-testing',
    'social-media',
    'ecommerce',
    'search-engine',
    'streaming',
    'gaming',
  ]

  useEffect(() => {
    fetchTags()
  }, [])

  const fetchTags = async () => {
    try {
      const response = await tagAPI.getTags()
      setAvailableTags(response.data.data || [])
    } catch (error) {
      console.error('Failed to fetch tags:', error)
    }
  }

  const handleGetProxy = async () => {
    if (!filter.tags?.length && !filter.scenario) {
      toast.error('请至少选择一个标签或场景')
      return
    }

    try {
      setLoading(true)
      const response = await proxyAPI.getProxyWithSmartRouting(filter)
      setSelectedProxy(response.data.data || null)
      toast.success('成功获取代理')
    } catch (error) {
      toast.error('获取代理失败')
      setSelectedProxy(null)
    } finally {
      setLoading(false)
    }
  }

  const handleTagToggle = (tagName: string) => {
    const currentTags = filter.tags || []
    const newTags = currentTags.includes(tagName)
      ? currentTags.filter((t) => t !== tagName)
      : [...currentTags, tagName]

    setFilter({ ...filter, tags: newTags.length > 0 ? newTags : undefined })
  }

  const getSelectedStrategy = () => {
    return strategies.find((s) => s.value === filter.strategy) || strategies[0]
  }

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <div className="flex items-center space-x-2">
        <Target className="h-6 w-6 text-blue-600" />
        <h2 className="text-xl font-semibold text-gray-900">智能代理路由</h2>
      </div>

      {/* 筛选条件 */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              筛选条件
            </h3>
            <button onClick={() => setShowAdvanced(!showAdvanced)} className="btn-sm btn-outline">
              <Settings className="h-4 w-4 mr-1" />
              {showAdvanced ? '简化' : '高级'}
            </button>
          </div>
        </div>
        <div className="card-body space-y-4">
          {/* 标签选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">选择标签</label>
            <div className="flex flex-wrap gap-2">
              {availableTags.map((tag) => (
                <button
                  key={tag.id}
                  onClick={() => handleTagToggle(tag.name)}
                  className={`px-3 py-1 rounded-full text-sm border transition-colors ${
                    filter.tags?.includes(tag.name)
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-300 text-gray-700 hover:border-gray-400'
                  }`}
                >
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 rounded-full" style={{ backgroundColor: tag.color }} />
                    <span>{tag.name}</span>
                  </div>
                </button>
              ))}
            </div>
            {filter.tags && filter.tags.length > 0 && (
              <div className="mt-2">
                <span className="text-sm text-gray-600">已选择: {filter.tags.join(', ')}</span>
              </div>
            )}
          </div>

          {/* 场景选择 */}
          {showAdvanced && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">使用场景</label>
              <select
                value={filter.scenario || ''}
                onChange={(e) => setFilter({ ...filter, scenario: e.target.value || undefined })}
                className="input"
              >
                <option value="">选择场景（可选）</option>
                {scenarios.map((scenario) => (
                  <option key={scenario} value={scenario}>
                    {scenario}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* 路由策略 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">路由策略</label>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {strategies.map((strategy) => (
                <button
                  key={strategy.value}
                  onClick={() => setFilter({ ...filter, strategy: strategy.value as any })}
                  className={`p-3 border rounded-lg text-left transition-colors ${
                    filter.strategy === strategy.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium text-gray-900">{strategy.label}</div>
                  <div className="text-sm text-gray-600 mt-1">{strategy.description}</div>
                </button>
              ))}
            </div>
          </div>

          {/* 获取代理按钮 */}
          <div className="flex justify-center pt-4">
            <button
              onClick={handleGetProxy}
              disabled={loading || (!filter.tags?.length && !filter.scenario)}
              className="btn btn-primary"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  获取中...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  获取最佳代理
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 结果显示 */}
      {selectedProxy && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">推荐代理</h3>
            <div className="text-sm text-gray-600">策略: {getSelectedStrategy().label}</div>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 代理信息 */}
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">代理信息</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">地址:</span>
                      <span className="font-mono">
                        {selectedProxy.host}:{selectedProxy.port}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">类型:</span>
                      <span className="uppercase">{selectedProxy.type}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">状态:</span>
                      <span
                        className={`px-2 py-1 rounded text-xs ${
                          selectedProxy.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {selectedProxy.status === 'active' ? '活跃' : '不可用'}
                      </span>
                    </div>
                    {selectedProxy.scenario && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">场景:</span>
                        <span>{selectedProxy.scenario}</span>
                      </div>
                    )}
                    {selectedProxy.priority && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">优先级:</span>
                        <span>{selectedProxy.priority}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* 标签 */}
                {selectedProxy.tags && selectedProxy.tags.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">标签</h4>
                    <div className="flex flex-wrap gap-1">
                      {selectedProxy.tags.map((tag) => (
                        <span
                          key={tag.id}
                          className="px-2 py-1 rounded text-xs border"
                          style={{
                            backgroundColor: tag.color + '20',
                            borderColor: tag.color,
                            color: tag.color,
                          }}
                        >
                          {tag.name}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* 质量评分 */}
              <div>
                <h4 className="font-medium text-gray-900 mb-2">质量评分</h4>
                <QualityScoreDisplay proxy={selectedProxy} showDetails={true} />
              </div>
            </div>

            {/* 使用说明 */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">使用说明</h4>
              <div className="text-sm text-blue-800 space-y-1">
                <p>
                  • 代理地址: {selectedProxy.host}:{selectedProxy.port}
                </p>
                <p>• 协议类型: {selectedProxy.type.toUpperCase()}</p>
                {selectedProxy.username && <p>• 需要认证: 是</p>}
                <p>• 建议用途: {selectedProxy.scenario || '通用'}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default SmartRouting