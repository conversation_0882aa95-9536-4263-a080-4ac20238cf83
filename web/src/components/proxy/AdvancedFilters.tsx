import React from 'react'
import { X, Filter } from 'lucide-react'
import type { ProxyTag } from '../../types'

interface FilterOptions {
  status: string[]
  types: string[]
  tags: string[]
  qualityRange: [number, number]
  showAdvanced: boolean
}

interface AdvancedFiltersProps {
  filters: FilterOptions
  onFiltersChange: (filters: FilterOptions) => void
  tags: ProxyTag[]
  onClose: () => void
}

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  filters,
  onFiltersChange,
  tags,
  onClose,
}) => {
  const statusOptions = [
    { value: 'active', label: '活跃', color: 'bg-green-100 text-green-800' },
    { value: 'inactive', label: '不活跃', color: 'bg-gray-100 text-gray-800' },
    { value: 'failed', label: '失败', color: 'bg-red-100 text-red-800' },
  ]

  const typeOptions = [
    { value: 'http', label: 'HTTP' },
    { value: 'https', label: 'HTTPS' },
    { value: 'socks5', label: 'SOCKS5' },
  ]

  const handleStatusChange = (status: string) => {
    const newStatus = filters.status.includes(status)
      ? filters.status.filter((s) => s !== status)
      : [...filters.status, status]

    onFiltersChange({ ...filters, status: newStatus })
  }

  const handleTypeChange = (type: string) => {
    const newTypes = filters.types.includes(type)
      ? filters.types.filter((t) => t !== type)
      : [...filters.types, type]

    onFiltersChange({ ...filters, types: newTypes })
  }

  const handleTagChange = (tagName: string) => {
    const newTags = filters.tags.includes(tagName)
      ? filters.tags.filter((t) => t !== tagName)
      : [...filters.tags, tagName]

    onFiltersChange({ ...filters, tags: newTags })
  }

  const handleQualityRangeChange = (index: number, value: number) => {
    const newRange: [number, number] = [...filters.qualityRange]
    newRange[index] = value
    onFiltersChange({ ...filters, qualityRange: newRange })
  }

  const clearAllFilters = () => {
    onFiltersChange({
      status: [],
      types: [],
      tags: [],
      qualityRange: [0, 1],
      showAdvanced: filters.showAdvanced,
    })
  }

  const hasActiveFilters =
    filters.status.length > 0 ||
    filters.types.length > 0 ||
    filters.tags.length > 0 ||
    filters.qualityRange[0] > 0 ||
    filters.qualityRange[1] < 1

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            高级筛选
          </h3>
          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <button
                onClick={clearAllFilters}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                清除所有
              </button>
            )}
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      <div className="card-body space-y-6">
        {/* 状态筛选 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">代理状态</label>
          <div className="flex flex-wrap gap-2">
            {statusOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => handleStatusChange(option.value)}
                className={`px-3 py-1 rounded-full text-sm border transition-colors ${
                  filters.status.includes(option.value)
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 text-gray-700 hover:border-gray-400'
                }`}
              >
                <span
                  className={`inline-block w-2 h-2 rounded-full mr-2 ${option.color.split(' ')[0]}`}
                />
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* 类型筛选 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">代理类型</label>
          <div className="flex flex-wrap gap-2">
            {typeOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => handleTypeChange(option.value)}
                className={`px-3 py-1 rounded-full text-sm border transition-colors ${
                  filters.types.includes(option.value)
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 text-gray-700 hover:border-gray-400'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* 标签筛选 */}
        {tags.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">代理标签</label>
            <div className="flex flex-wrap gap-2">
              {tags.map((tag) => (
                <button
                  key={tag.id}
                  onClick={() => handleTagChange(tag.name)}
                  className={`px-3 py-1 rounded-full text-sm border transition-colors ${
                    filters.tags.includes(tag.name)
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-300 text-gray-700 hover:border-gray-400'
                  }`}
                >
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 rounded-full" style={{ backgroundColor: tag.color }} />
                    <span>{tag.name}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* 质量评分范围 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">质量评分范围</label>
          <div className="space-y-3">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <label className="block text-xs text-gray-500 mb-1">最低评分</label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={filters.qualityRange[0]}
                  onChange={(e) => handleQualityRangeChange(0, parseFloat(e.target.value))}
                  className="w-full"
                />
                <div className="text-xs text-gray-600 mt-1">
                  {(filters.qualityRange[0] * 100).toFixed(0)}%
                </div>
              </div>
              <div className="flex-1">
                <label className="block text-xs text-gray-500 mb-1">最高评分</label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={filters.qualityRange[1]}
                  onChange={(e) => handleQualityRangeChange(1, parseFloat(e.target.value))}
                  className="w-full"
                />
                <div className="text-xs text-gray-600 mt-1">
                  {(filters.qualityRange[1] * 100).toFixed(0)}%
                </div>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              显示评分在 {(filters.qualityRange[0] * 100).toFixed(0)}% -{' '}
              {(filters.qualityRange[1] * 100).toFixed(0)}% 之间的代理
            </div>
          </div>
        </div>

        {/* 筛选结果统计 */}
        {hasActiveFilters && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="text-sm text-blue-800">
              <strong>当前筛选条件：</strong>
              <ul className="mt-1 space-y-1">
                {filters.status.length > 0 && <li>• 状态: {filters.status.join(', ')}</li>}
                {filters.types.length > 0 && <li>• 类型: {filters.types.join(', ')}</li>}
                {filters.tags.length > 0 && <li>• 标签: {filters.tags.join(', ')}</li>}
                {(filters.qualityRange[0] > 0 || filters.qualityRange[1] < 1) && (
                  <li>
                    • 质量评分: {(filters.qualityRange[0] * 100).toFixed(0)}% -{' '}
                    {(filters.qualityRange[1] * 100).toFixed(0)}%
                  </li>
                )}
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default AdvancedFilters