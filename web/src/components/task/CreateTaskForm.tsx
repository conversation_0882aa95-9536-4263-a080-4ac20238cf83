import React, { useState, useEffect } from 'react'
import { taskAPI } from '../../services/api'
import {
  HTTP_METHOD_OPTIONS,
  PROXY_STRATEGY_OPTIONS,
  TASK_PRIORITY_OPTIONS,
} from '../../utils/constants'
import { validateUrl } from '../../utils/helpers'
import toast from 'react-hot-toast'
import type { TaskFormData, FormErrors, HttpMethod, ProxyStrategy, TaskPriority } from '../../types'

interface CreateTaskFormProps {
  onSuccess: () => void
  onCancel: () => void
  initialData?: any
  isEditing?: boolean
}

interface FormData extends Omit<TaskFormData, 'headers' | 'max_retries' | 'timeout'> {
  headers: string
  max_retries: string
  timeout: string
}

const CreateTaskForm: React.FC<CreateTaskFormProps> = ({
  onSuccess,
  onCancel,
  initialData,
  isEditing = false,
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: initialData?.name || '',
    url: initialData?.url || '',
    method: (initialData?.method || 'GET') as HttpMethod,
    headers: initialData?.headers ? JSON.stringify(initialData.headers, null, 2) : '{}',
    body: initialData?.body || '',
    proxy_strategy: (initialData?.proxy_strategy || 'round_robin') as ProxyStrategy,
    priority: (initialData?.priority || 2) as TaskPriority,
    max_retries: String(initialData?.max_retries || 3),
    timeout: String(initialData?.timeout || 30),
  })
  const [errors, setErrors] = useState<FormErrors<FormData>>({})
  const [loading, setLoading] = useState<boolean>(false)

  // 当 initialData 变化时，重新初始化表单数据
  useEffect(() => {
    if (initialData) {
      console.log('初始化表单数据:', initialData) // 调试信息
      setFormData({
        name: initialData.name || '',
        url: initialData.url || '',
        method: (initialData.method || 'GET') as HttpMethod,
        headers: initialData.headers ? JSON.stringify(initialData.headers, null, 2) : '{}',
        body: initialData.body || '',
        proxy_strategy: (initialData.proxy_strategy || 'round_robin') as ProxyStrategy,
        priority: (initialData.priority || 2) as TaskPriority,
        max_retries: String(initialData.max_retries || 3),
        timeout: String(initialData.timeout || 30),
      })
    }
  }, [initialData])

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))

    // 清除对应字段的错误
    if (errors[name as keyof FormData]) {
      setErrors((prev) => ({
        ...prev,
        [name]: '',
      }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors<FormData> = {}

    if (!formData.name.trim()) {
      newErrors.name = '请输入任务名称'
    }

    if (!formData.url.trim()) {
      newErrors.url = '请输入URL'
    } else if (!validateUrl(formData.url)) {
      newErrors.url = '请输入有效的URL'
    }

    // 验证 JSON 格式的 headers
    if (formData.headers.trim()) {
      try {
        JSON.parse(formData.headers)
      } catch {
        newErrors.headers = '请输入有效的JSON格式'
      }
    }

    const maxRetries = parseInt(formData.max_retries)
    if (isNaN(maxRetries) || maxRetries < 0 || maxRetries > 10) {
      newErrors.max_retries = '重试次数必须在0-10之间'
    }

    const timeout = parseInt(formData.timeout)
    if (isNaN(timeout) || timeout < 1 || timeout > 300) {
      newErrors.timeout = '超时时间必须在1-300秒之间'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!validateForm()) return

    try {
      setLoading(true)

      const taskData: TaskFormData = {
        name: formData.name,
        url: formData.url,
        method: formData.method,
        headers: formData.headers ? JSON.parse(formData.headers) : {},
        body: formData.body || undefined,
        proxy_strategy: formData.proxy_strategy,
        priority: formData.priority,
        max_retries: parseInt(formData.max_retries),
        timeout: parseInt(formData.timeout),
      }

      if (isEditing && initialData?.id) {
        // 编辑模式：调用更新API
        await taskAPI.updateTask(initialData.id, taskData)
        toast.success('任务更新成功')
      } else {
        // 创建模式：调用创建API
        await taskAPI.createTask(taskData)
        toast.success('任务创建成功')
      }

      onSuccess()
    } catch (error) {
      toast.error('创建任务失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700">
          任务名称 *
        </label>
        <input
          type="text"
          id="name"
          name="name"
          required
          className={`input ${errors.name ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
          placeholder="请输入任务名称"
          value={formData.name}
          onChange={handleChange}
        />
        {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
      </div>

      <div>
        <label htmlFor="url" className="block text-sm font-medium text-gray-700">
          请求URL *
        </label>
        <input
          type="url"
          id="url"
          name="url"
          required
          className={`input ${errors.url ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
          placeholder="https://api.example.com/data"
          value={formData.url}
          onChange={handleChange}
        />
        {errors.url && <p className="mt-1 text-sm text-red-600">{errors.url}</p>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="method" className="block text-sm font-medium text-gray-700">
            请求方法 *
          </label>
          <select
            id="method"
            name="method"
            required
            className="input"
            value={formData.method}
            onChange={handleChange}
          >
            {HTTP_METHOD_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="proxy_strategy" className="block text-sm font-medium text-gray-700">
            代理策略 *
          </label>
          <select
            id="proxy_strategy"
            name="proxy_strategy"
            required
            className="input"
            value={formData.proxy_strategy}
            onChange={handleChange}
          >
            {PROXY_STRATEGY_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
            优先级 *
          </label>
          <select
            id="priority"
            name="priority"
            required
            className="input"
            value={formData.priority}
            onChange={handleChange}
          >
            {TASK_PRIORITY_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="max_retries" className="block text-sm font-medium text-gray-700">
            最大重试次数
          </label>
          <input
            type="number"
            id="max_retries"
            name="max_retries"
            min="0"
            max="10"
            className={`input ${errors.max_retries ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
            value={formData.max_retries}
            onChange={handleChange}
          />
          {errors.max_retries && <p className="mt-1 text-sm text-red-600">{errors.max_retries}</p>}
        </div>

        <div>
          <label htmlFor="timeout" className="block text-sm font-medium text-gray-700">
            超时时间(秒)
          </label>
          <input
            type="number"
            id="timeout"
            name="timeout"
            min="1"
            max="300"
            className={`input ${errors.timeout ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
            value={formData.timeout}
            onChange={handleChange}
          />
          {errors.timeout && <p className="mt-1 text-sm text-red-600">{errors.timeout}</p>}
        </div>
      </div>

      <div>
        <label htmlFor="headers" className="block text-sm font-medium text-gray-700">
          请求头 (JSON格式)
        </label>
        <textarea
          id="headers"
          name="headers"
          rows={3}
          className={`input ${errors.headers ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
          placeholder='{"Content-Type": "application/json"}'
          value={formData.headers}
          onChange={handleChange}
        />
        {errors.headers && <p className="mt-1 text-sm text-red-600">{errors.headers}</p>}
      </div>

      {(formData.method === 'POST' || formData.method === 'PUT' || formData.method === 'PATCH') && (
        <div>
          <label htmlFor="body" className="block text-sm font-medium text-gray-700">
            请求体
          </label>
          <textarea
            id="body"
            name="body"
            rows={4}
            className="input"
            placeholder="请求体内容"
            value={formData.body}
            onChange={handleChange}
          />
        </div>
      )}

      <div className="flex justify-end space-x-3 pt-4">
        <button type="button" onClick={onCancel} className="btn-outline">
          取消
        </button>
        <button type="submit" disabled={loading} className="btn-primary">
          {loading ? (isEditing ? '更新中...' : '创建中...') : isEditing ? '更新任务' : '创建任务'}
        </button>
      </div>
    </form>
  )
}

export default CreateTaskForm