import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Target,
  Alert<PERSON><PERSON>gle,
  CheckCircle,
  Activity,
  Download,
  Filter,
  Calendar,
  Globe,
  Zap,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  RefreshC<PERSON>,
} from 'lucide-react'
import { taskAPI } from '../../services/api'
import LoadingSpinner from '../common/LoadingSpinner'
import { formatDate, formatDuration } from '../../utils/helpers'
import toast from 'react-hot-toast'

interface TaskAnalyticsDashboardProps {
  className?: string
}

interface TaskAnalytics {
  overview: {
    total_tasks: number
    success_rate: number
    avg_response_time: number
    total_execution_time: number
    active_tasks: number
  }
  trends: {
    daily_stats: Array<{
      date: string
      total: number
      success: number
      failed: number
      avg_response_time: number
    }>
    hourly_distribution: Array<{
      hour: number
      count: number
    }>
  }
  performance: {
    proxy_performance: Array<{
      proxy_id: string
      proxy_host: string
      success_rate: number
      avg_response_time: number
      total_requests: number
    }>
    url_performance: Array<{
      url: string
      success_rate: number
      avg_response_time: number
      total_requests: number
    }>
  }
  geographic: {
    country_stats: Array<{
      country: string
      success_rate: number
      avg_response_time: number
      total_requests: number
    }>
  }
  errors: {
    common_errors: Array<{
      error_type: string
      count: number
      percentage: number
    }>
    recent_failures: Array<{
      task_id: string
      task_name: string
      error_message: string
      timestamp: string
    }>
  }
}

const TaskAnalyticsDashboard: React.FC<TaskAnalyticsDashboardProps> = ({ className = '' }) => {
  const [analytics, setAnalytics] = useState<TaskAnalytics | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [refreshing, setRefreshing] = useState<boolean>(false)
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h')
  const [selectedMetric, setSelectedMetric] = useState<
    'success_rate' | 'response_time' | 'request_count'
  >('success_rate')

  // 获取分析数据
  const fetchAnalytics = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true)
      else setRefreshing(true)

      // 尝试调用真实API，失败时使用模拟数据
      try {
        const response = await taskAPI.getTaskAnalytics(timeRange)
        setAnalytics(response.data.data)
        return
      } catch (apiError) {
        console.warn('API调用失败，使用模拟数据:', apiError)
      }

      // 模拟数据作为fallback
      const mockData: TaskAnalytics = {
        overview: {
          total_tasks: 1247,
          success_rate: 94.2,
          avg_response_time: 1250,
          total_execution_time: 156780,
          active_tasks: 12,
        },
        trends: {
          daily_stats: Array.from({ length: 7 }, (_, i) => ({
            date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            total: Math.floor(Math.random() * 200) + 50,
            success: Math.floor(Math.random() * 180) + 40,
            failed: Math.floor(Math.random() * 20) + 5,
            avg_response_time: Math.floor(Math.random() * 2000) + 500,
          })).reverse(),
          hourly_distribution: Array.from({ length: 24 }, (_, i) => ({
            hour: i,
            count: Math.floor(Math.random() * 50) + 10,
          })),
        },
        performance: {
          proxy_performance: [
            {
              proxy_id: '1',
              proxy_host: '*************:8080',
              success_rate: 96.5,
              avg_response_time: 1100,
              total_requests: 245,
            },
            {
              proxy_id: '2',
              proxy_host: '*************:8080',
              success_rate: 94.2,
              avg_response_time: 1350,
              total_requests: 198,
            },
            {
              proxy_id: '3',
              proxy_host: '*************:8080',
              success_rate: 92.8,
              avg_response_time: 1450,
              total_requests: 167,
            },
          ],
          url_performance: [
            {
              url: 'https://api.example.com/health',
              success_rate: 98.5,
              avg_response_time: 850,
              total_requests: 456,
            },
            {
              url: 'https://www.google.com',
              success_rate: 97.2,
              avg_response_time: 1200,
              total_requests: 234,
            },
            {
              url: 'https://httpbin.org/ip',
              success_rate: 95.8,
              avg_response_time: 1100,
              total_requests: 189,
            },
          ],
        },
        geographic: {
          country_stats: [
            {
              country: 'United States',
              success_rate: 96.2,
              avg_response_time: 1150,
              total_requests: 345,
            },
            { country: 'China', success_rate: 94.8, avg_response_time: 1350, total_requests: 267 },
            {
              country: 'Germany',
              success_rate: 95.5,
              avg_response_time: 1250,
              total_requests: 198,
            },
          ],
        },
        errors: {
          common_errors: [
            { error_type: 'Connection Timeout', count: 23, percentage: 45.1 },
            { error_type: 'HTTP 500 Error', count: 12, percentage: 23.5 },
            { error_type: 'DNS Resolution Failed', count: 8, percentage: 15.7 },
            { error_type: 'Proxy Connection Failed', count: 8, percentage: 15.7 },
          ],
          recent_failures: [
            {
              task_id: 'task_123',
              task_name: 'API Health Check',
              error_message: 'Connection timeout after 30s',
              timestamp: new Date().toISOString(),
            },
            {
              task_id: 'task_124',
              task_name: 'Website Monitor',
              error_message: 'HTTP 500 Internal Server Error',
              timestamp: new Date(Date.now() - 300000).toISOString(),
            },
          ],
        },
      }

      setAnalytics(mockData)
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
      toast.error('获取分析数据失败')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // 导出报告
  const exportReport = async () => {
    try {
      // 实际应该调用导出API
      toast.success('报告导出功能开发中...')
    } catch (error) {
      toast.error('导出报告失败')
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [timeRange])

  if (loading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <LoadingSpinner size="lg" text="加载分析数据..." />
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="text-center py-8">
          <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">无法加载分析数据</h3>
          <button onClick={() => fetchAnalytics()} className="btn-primary">
            重新加载
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 控制面板 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h2 className="text-xl font-bold text-gray-900 flex items-center">
              <BarChart3 className="h-6 w-6 mr-2" />
              任务分析仪表板
            </h2>
            <p className="text-sm text-gray-600 mt-1">全面分析任务执行情况和性能指标</p>
          </div>

          <div className="flex flex-wrap items-center gap-3">
            {/* 时间范围选择 */}
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <select
                className="input text-sm"
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as any)}
              >
                <option value="1h">最近1小时</option>
                <option value="24h">最近24小时</option>
                <option value="7d">最近7天</option>
                <option value="30d">最近30天</option>
              </select>
            </div>

            {/* 刷新按钮 */}
            <button
              onClick={() => fetchAnalytics(false)}
              disabled={refreshing}
              className="btn-outline text-sm"
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${refreshing ? 'animate-spin' : ''}`} />
              刷新
            </button>

            {/* 导出按钮 */}
            <button onClick={exportReport} className="btn-primary text-sm">
              <Download className="h-4 w-4 mr-1" />
              导出报告
            </button>
          </div>
        </div>
      </div>

      {/* 概览指标 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总任务数</p>
              <p className="text-2xl font-bold text-gray-900">
                {analytics.overview.total_tasks.toLocaleString()}
              </p>
            </div>
            <Target className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">成功率</p>
              <p className="text-2xl font-bold text-green-600">
                {analytics.overview.success_rate.toFixed(1)}%
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">平均响应时间</p>
              <p className="text-2xl font-bold text-purple-600">
                {analytics.overview.avg_response_time}ms
              </p>
            </div>
            <Clock className="h-8 w-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总执行时间</p>
              <p className="text-2xl font-bold text-orange-600">
                {formatDuration(analytics.overview.total_execution_time)}
              </p>
            </div>
            <Activity className="h-8 w-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">活跃任务</p>
              <p className="text-2xl font-bold text-blue-600">{analytics.overview.active_tasks}</p>
            </div>
            <Zap className="h-8 w-8 text-blue-500" />
          </div>
        </div>
      </div>

      {/* 趋势图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 每日统计趋势 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              每日执行趋势
            </h3>
            <select
              className="input text-sm"
              value={selectedMetric}
              onChange={(e) => setSelectedMetric(e.target.value as any)}
            >
              <option value="success_rate">成功率</option>
              <option value="response_time">响应时间</option>
              <option value="request_count">请求数量</option>
            </select>
          </div>

          {/* 这里应该是图表组件 */}
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center text-gray-500">
              <LineChart className="h-12 w-12 mx-auto mb-2" />
              <p>趋势图表</p>
              <p className="text-sm">（需要集成图表库）</p>
            </div>
          </div>
        </div>

        {/* 小时分布 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            24小时分布
          </h3>

          {/* 这里应该是柱状图组件 */}
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center text-gray-500">
              <BarChart3 className="h-12 w-12 mx-auto mb-2" />
              <p>小时分布图</p>
              <p className="text-sm">（需要集成图表库）</p>
            </div>
          </div>
        </div>
      </div>

      {/* 性能分析 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 代理性能排行 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Target className="h-5 w-5 mr-2" />
            代理性能排行
          </h3>

          <div className="space-y-3">
            {analytics.performance.proxy_performance.map((proxy, index) => (
              <div
                key={proxy.proxy_id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div
                    className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                      index === 0
                        ? 'bg-yellow-100 text-yellow-800'
                        : index === 1
                          ? 'bg-gray-100 text-gray-800'
                          : index === 2
                            ? 'bg-orange-100 text-orange-800'
                            : 'bg-blue-100 text-blue-800'
                    }`}
                  >
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{proxy.proxy_host}</p>
                    <p className="text-sm text-gray-500">{proxy.total_requests} 次请求</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-green-600">
                    {proxy.success_rate.toFixed(1)}%
                  </p>
                  <p className="text-xs text-gray-500">{proxy.avg_response_time}ms</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* URL性能分析 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Globe className="h-5 w-5 mr-2" />
            URL性能分析
          </h3>

          <div className="space-y-3">
            {analytics.performance.url_performance.map((url, index) => (
              <div key={index} className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <p className="font-medium text-gray-900 truncate flex-1 mr-2">{url.url}</p>
                  <span className="text-sm font-medium text-green-600">
                    {url.success_rate.toFixed(1)}%
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>{url.total_requests} 次请求</span>
                  <span>{url.avg_response_time}ms 平均响应</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 错误分析 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 常见错误类型 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" />
            常见错误类型
          </h3>

          <div className="space-y-3">
            {analytics.errors.common_errors.map((error, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="font-medium text-gray-900">{error.error_type}</p>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                    <div
                      className="bg-red-500 h-2 rounded-full"
                      style={{ width: `${error.percentage}%` }}
                    ></div>
                  </div>
                </div>
                <div className="ml-4 text-right">
                  <p className="text-sm font-medium text-gray-900">{error.count}</p>
                  <p className="text-xs text-gray-500">{error.percentage.toFixed(1)}%</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 最近失败任务 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" />
            最近失败任务
          </h3>

          <div className="space-y-3">
            {analytics.errors.recent_failures.map((failure, index) => (
              <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <p className="font-medium text-gray-900">{failure.task_name}</p>
                  <span className="text-xs text-gray-500">
                    {formatDate(failure.timestamp, 'HH:mm')}
                  </span>
                </div>
                <p className="text-sm text-red-600">{failure.error_message}</p>
                <p className="text-xs text-gray-500 mt-1">任务ID: {failure.task_id}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default TaskAnalyticsDashboard