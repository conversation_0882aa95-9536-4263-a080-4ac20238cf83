import toast from 'react-hot-toast'
import { X,  Info, AlertTriangle } from 'lucide-react'

// 简单的可关闭toast实现
export const simpleToast = {
  success: (message: string, options?: { persistent?: boolean }) => {
    return toast.success(
      (t) => (
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            {/* <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" /> */}
            <span className="text-sm font-medium text-gray-900">{message}</span>
          </div>
          <button
            onClick={() => toast.dismiss(t.id)}
            className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      ),
      {
        duration: options?.persistent ? Infinity : 3000,
        style: {
          background: '#f0fdf4',
          border: '1px solid #bbf7d0',
          color: '#166534',
          maxWidth: '500px',
          padding: '12px 16px',
        },
      }
    )
  },

  error: (message: string, options?: { persistent?: boolean }) => {
    return toast.error(
      (t) => (
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            {/* <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" /> */}
            <span className="text-sm font-medium text-gray-900">{message}</span>
          </div>
          <button
            onClick={() => toast.dismiss(t.id)}
            className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      ),
      {
        duration: options?.persistent ? Infinity : 5000,
        style: {
          background: '#fef2f2',
          border: '1px solid #fecaca',
          color: '#991b1b',
          maxWidth: '500px',
          padding: '12px 16px',
        },
      }
    )
  },

  warning: (message: string, options?: { persistent?: boolean }) => {
    return toast(
      (t) => (
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2 flex-shrink-0" />
            <span className="text-sm font-medium text-gray-900">{message}</span>
          </div>
          <button
            onClick={() => toast.dismiss(t.id)}
            className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      ),
      {
        duration: options?.persistent ? Infinity : 4000,
        style: {
          background: '#fffbeb',
          border: '1px solid #fed7aa',
          color: '#92400e',
          maxWidth: '500px',
          padding: '12px 16px',
        },
      }
    )
  },

  info: (message: string, options?: { persistent?: boolean }) => {
    return toast(
      (t) => (
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <Info className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" />
            <span className="text-sm font-medium text-gray-900">{message}</span>
          </div>
          <button
            onClick={() => toast.dismiss(t.id)}
            className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      ),
      {
        duration: options?.persistent ? Infinity : 4000,
        style: {
          background: '#eff6ff',
          border: '1px solid #bfdbfe',
          color: '#1e40af',
          maxWidth: '500px',
          padding: '12px 16px',
        },
      }
    )
  },

  // 持久toast的便捷方法
  persistent: {
    success: (message: string) => simpleToast.success(message, { persistent: true }),
    error: (message: string) => simpleToast.error(message, { persistent: true }),
    warning: (message: string) => simpleToast.warning(message, { persistent: true }),
    info: (message: string) => simpleToast.info(message, { persistent: true }),
  }
}

export default simpleToast
