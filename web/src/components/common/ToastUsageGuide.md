# Toast 使用指南

ProxyFlow 现在支持两种类型的 Toast 通知：

## 1. 原生 Toast（react-hot-toast）

```typescript
import toast from 'react-hot-toast'

// 基本用法
toast.success('操作成功')
toast.error('操作失败')
toast.loading('正在处理...')

// 自定义选项
toast.success('操作成功', {
  duration: 3000,
  position: 'top-center',
})
```

**特点：**
- 自动消失
- 无关闭按钮
- 轻量级
- 适合简单通知

## 2. 可关闭 Toast（推荐）

```typescript
import { simpleToast } from '../components/common/SimpleToast'

// 基本用法
simpleToast.success('操作成功！可以点击 X 关闭')
simpleToast.error('操作失败！可以点击 X 关闭')
simpleToast.warning('警告信息！可以点击 X 关闭')
simpleToast.info('提示信息！可以点击 X 关闭')

// 持久 Toast（不自动消失）
simpleToast.persistent.success('重要成功信息')
simpleToast.persistent.error('重要错误信息')
simpleToast.persistent.warning('重要警告信息')
simpleToast.persistent.info('重要提示信息')
```

**特点：**
- 可手动关闭（X 按钮）
- 美观的视觉设计
- 支持持久显示
- 稳定的动画效果
- 适合重要通知

## 3. 使用建议

### 何时使用原生 Toast：
- 简单的成功/失败反馈
- 临时状态更新
- 不需要用户交互的通知

### 何时使用可关闭 Toast：
- 重要的错误信息
- 需要用户确认的通知
- 复杂的操作结果
- 需要持久显示的信息

## 4. 最佳实践

```typescript
// ✅ 好的做法
simpleToast.success('代理添加成功')
simpleToast.error('网络连接失败，请检查网络设置')
simpleToast.persistent.warning('系统将在 5 分钟后维护，请保存工作')

// ❌ 避免的做法
simpleToast.info('') // 空消息
simpleToast.persistent.success('操作成功') // 成功消息通常不需要持久显示
```

## 5. 全局控制

```typescript
import toast from 'react-hot-toast'

// 关闭所有 Toast
toast.dismiss()

// 关闭特定 Toast
const toastId = toast.success('消息')
toast.dismiss(toastId)
```

## 6. 配置选项

Toast 的全局配置在 `main.tsx` 中：

```typescript
<Toaster
  position="top-right"
  toastOptions={{
    duration: 4000,
    style: {
      background: '#363636',
      color: '#fff',
    },
  }}
/>
```
