import React from 'react'
import toast, { Toast } from 'react-hot-toast'
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'

interface CustomToastProps {
  t: Toast
  message: string
  type?: 'success' | 'error' | 'info' | 'warning'
}

const CustomToast: React.FC<CustomToastProps> = ({ t, message, type = 'info' }) => {
  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-400" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-400" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-400" />
      default:
        return <Info className="h-5 w-5 text-blue-400" />
    }
  }

  const getBackgroundColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200'
      case 'error':
        return 'bg-red-50 border-red-200'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200'
      default:
        return 'bg-blue-50 border-blue-200'
    }
  }

  const getTextColor = () => {
    switch (type) {
      case 'success':
        return 'text-green-800'
      case 'error':
        return 'text-red-800'
      case 'warning':
        return 'text-yellow-800'
      default:
        return 'text-blue-800'
    }
  }

  // 处理关闭动画
  const handleDismiss = () => {
    // 立即关闭，不使用动画
    toast.dismiss(t.id)
  }

  return (
    <div
      className={`max-w-md w-full ${getBackgroundColor()} border shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5`}
      style={{
        opacity: t.visible ? 1 : 0,
        transform: t.visible ? 'translateX(0px)' : 'translateX(100%)',
        transition: 'all 0.3s ease-in-out',
      }}
    >
      <div className="flex-1 w-0 p-2">
        <div className="flex items-center">
          <div className="flex-shrink-0">{getIcon()}</div>
          <div className="ml-3 flex-1">
            <p className={`text-sm font-medium ${getTextColor()}`}>{message}</p>
          </div>
        </div>
      </div>
      <div className="flex border-l border-gray-200">
        <button
          onClick={handleDismiss}
          className="w-full border border-transparent rounded-none rounded-r-lg p-4 flex items-center justify-center text-sm font-medium text-gray-600 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  )
}

export default CustomToast