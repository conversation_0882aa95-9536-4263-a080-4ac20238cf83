import { Component, ErrorInfo, ReactNode } from 'react'
import ErrorPage from './ErrorPage'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  }

  public static getDerivedStateFromError(error: Error): State {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { hasError: true, error }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)

    // 这里可以将错误日志发送到错误报告服务
    // logErrorToService(error, errorInfo)
  }

  public render() {
    if (this.state.hasError) {
      return (
        <ErrorPage
          title="500"
          message="应用程序出现错误"
          showHomeButton={true}
          showBackButton={false}
          showRefreshButton={true}
        />
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary