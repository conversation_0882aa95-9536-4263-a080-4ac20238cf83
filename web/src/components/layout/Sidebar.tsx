import React from 'react'
import { NavLink, useLocation, useNavigate } from 'react-router-dom'
import {
  LayoutDashboard,
  Server,
  Magnet,
  ListTodo,
  BarChart3,
  Settings,
  X,
  Key,
  Info,
  LucideIcon,
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'

interface NavigationItem {
  name: string
  href: string
  icon: LucideIcon
}

interface SidebarProps {
  sidebarOpen: boolean
  setSidebarOpen: (open: boolean) => void
}

const navigation: NavigationItem[] = [
  { name: '仪表板', href: '/dashboard', icon: LayoutDashboard },
  { name: '代理管理', href: '/proxies', icon: Server },
  { name: '采集器', href: '/collector', icon: Magnet },
  { name: '任务管理', href: '/tasks', icon: ListTodo },
  { name: '系统监控', href: '/monitoring', icon: BarChart3 },
  { name: 'API 密钥', href: '/keys', icon: Key },
  { name: '设置', href: '/settings', icon: Settings },
  { name: '关于', href: '/about', icon: Info },
]

const Sidebar: React.FC<SidebarProps> = ({ sidebarOpen, setSidebarOpen }) => {
  const { user } = useAuth()
  const location = useLocation()
  const navigate = useNavigate()

  const SidebarContent: React.FC = () => (
    <div className="flex flex-col h-full">
      {/* Logo */}
      <div className="flex items-center h-16 flex-shrink-0 px-4 bg-primary-600">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <img src="/logo.svg" alt="Logo" className="h-8 w-8" />
          </div>
          <div className="ml-3">
            <h1 className="text-white text-lg font-semibold">ProxyFlow</h1>
          </div>
        </div>
        {/* 移动端关闭按钮 */}
        <button
          className="ml-auto md:hidden text-white hover:text-gray-200"
          onClick={() => setSidebarOpen(false)}
        >
          <X className="h-6 w-6" />
        </button>
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 px-2 py-4 bg-white overflow-y-auto">
        <div className="space-y-2">
          {navigation.map((item) => {
            const isActive = location.pathname === item.href
            return (
              <NavLink
                key={item.name}
                to={item.href}
                className={`group flex items-center px-3 py-3 text-sm font-medium rounded-md transition-colors duration-200 ${
                  isActive
                    ? 'bg-primary-100 text-primary-900'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
                onClick={() => setSidebarOpen(false)}
              >
                <item.icon
                  className={`mr-3 flex-shrink-0 h-5 w-5 ${
                    isActive ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'
                  }`}
                />
                {item.name}
              </NavLink>
            )
          })}
        </div>
      </nav>

      {/* 用户信息 */}
      <div className="flex-shrink-0 flex border-t border-gray-200 p-4 bg-white">
        <button
          className="flex items-center w-full hover:bg-gray-50 rounded-lg p-2 -m-2 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          onClick={() => {
            navigate('/settings?tab=profile')
            setSidebarOpen(false)
          }}
          title="点击查看个人资料"
        >
          <div className="flex-shrink-0">
            <div className="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-primary-600">
                {user?.username?.charAt(0).toUpperCase()}
              </span>
            </div>
          </div>
          <div className="ml-3 text-left flex-1 min-w-0">
            <p className="text-md font-medium text-gray-700 truncate">{user?.username}</p>
            <p className="text-xs text-gray-500 truncate">{user?.role || '用户'}</p>
          </div>
        </button>
      </div>
    </div>
  )

  return (
    <>
      {/* 桌面端侧边栏 */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-64">
          <SidebarContent />
        </div>
      </div>

      {/* 移动端侧边栏 */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out md:hidden ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full bg-white shadow-xl">
          <SidebarContent />
        </div>
      </div>
    </>
  )
}

export default Sidebar