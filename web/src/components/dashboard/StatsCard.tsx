import React from 'react'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'
import type { StatsCardProps } from '../../types'

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon: Icon,
  color = 'blue',
  trend = 'neutral',
  trendValue,
}) => {
  const colorClasses: Record<string, string> = {
    blue: 'text-blue-600 bg-blue-100',
    green: 'text-green-600 bg-green-100',
    purple: 'text-purple-600 bg-purple-100',
    red: 'text-red-600 bg-red-100',
    yellow: 'text-yellow-600 bg-yellow-100',
  }

  const trendIcons = {
    up: TrendingUp,
    down: TrendingDown,
    neutral: Minus,
  }

  const trendColors = {
    up: 'text-green-600',
    down: 'text-red-600',
    neutral: 'text-gray-600',
  }

  const TrendIcon = trendIcons[trend]

  return (
    <div className="card">
      <div className="card-body">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
              <Icon className="h-6 w-6" />
            </div>
          </div>
          <div className="ml-4 flex-1">
            <p className="text-sm font-medium text-gray-500">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {trendValue && (
              <div className="flex items-center mt-1">
                <TrendIcon className={`h-4 w-4 mr-1 ${trendColors[trend]}`} />
                <span className={`text-sm ${trendColors[trend]}`}>{trendValue}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default StatsCard