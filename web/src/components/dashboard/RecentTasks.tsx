import React from 'react'
import { Link } from 'react-router-dom'
import StatusBadge from '../common/StatusBadge'
import { formatRelativeTime } from '../../utils/helpers'
import { ExternalLink } from 'lucide-react'
import type { Task } from '../../types'

interface RecentTasksProps {
  tasks: Task[]
}

const RecentTasks: React.FC<RecentTasksProps> = ({ tasks }) => {
  if (tasks.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">暂无最近任务</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {tasks.map((task) => (
        <div
          key={task.id}
          className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3">
              <h4 className="text-sm font-medium text-gray-900 truncate">{task.name}</h4>
              <StatusBadge status={task.status} type="task" />
            </div>
            <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
              <span className="truncate max-w-xs" title={task.url}>
                {task.url}
              </span>
              <span>{task.method}</span>
              <span>{formatRelativeTime(task.created_at)}</span>
            </div>
          </div>
          <div className="flex-shrink-0 ml-4">
            <Link
              to="/tasks"
              className="text-primary-600 hover:text-primary-900"
              title="查看所有任务"
            >
              <ExternalLink className="h-4 w-4" />
            </Link>
          </div>
        </div>
      ))}

      {tasks.length >= 10 && (
        <div className="text-center pt-4">
          <Link to="/tasks" className="text-primary-600 hover:text-primary-900 text-sm font-medium">
            查看所有任务 →
          </Link>
        </div>
      )}
    </div>
  )
}

export default RecentTasks