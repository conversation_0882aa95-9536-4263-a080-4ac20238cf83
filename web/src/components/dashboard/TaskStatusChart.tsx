import React from 'react'
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import type { TaskStats } from '../../types'

interface TaskStatusChartProps {
  data: TaskStats
}

interface ChartDataItem {
  name: string
  value: number
  color: string
}

const TaskStatusChart: React.FC<TaskStatusChartProps> = ({ data }) => {
  const chartData: ChartDataItem[] = [
    { name: '等待中', value: data.pending || 0, color: '#6b7280' },
    { name: '运行中', value: data.running || 0, color: '#3b82f6' },
    { name: '已完成', value: data.completed || 0, color: '#22c55e' },
    { name: '失败', value: data.failed || 0, color: '#ef4444' },
    { name: '已取消', value: data.cancelled || 0, color: '#f59e0b' },
  ]

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0]
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium">{label}</p>
          <p className="text-sm text-gray-600">数量: {data.value}</p>
        </div>
      )
    }
    return null
  }

  if (chartData.every((item) => item.value === 0)) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">暂无数据</p>
      </div>
    )
  }

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" tick={{ fontSize: 12 }} interval={0} />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="value" fill="#3b82f6" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}

export default TaskStatusChart