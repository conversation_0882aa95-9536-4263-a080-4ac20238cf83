import React, { useState, useEffect } from 'react'
import { Settings, Database, Wifi, WifiOff, Code, RefreshCw } from 'lucide-react'
import { shouldUseMockAPI, enableMockAPI, disableMockAPI } from '../../services/mockApiService'
import toast from 'react-hot-toast'

interface DevToolsProps {
  className?: string
}

const DevTools: React.FC<DevToolsProps> = ({ className = '' }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [useMockAPI, setUseMockAPI] = useState(shouldUseMockAPI())
  const [showInProduction, setShowInProduction] = useState(false)

  // 检查是否在生产环境
  const isProduction = import.meta.env.PROD

  useEffect(() => {
    // 在生产环境中默认隐藏开发工具
    if (isProduction && !localStorage.getItem('show-dev-tools')) {
      return
    }

    // 监听键盘快捷键 Ctrl+Shift+D 打开开发工具
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        e.preventDefault()
        setIsOpen((prev) => !prev)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isProduction])

  // 切换模拟API模式
  const toggleMockAPI = () => {
    if (useMockAPI) {
      disableMockAPI()
      setUseMockAPI(false)
      toast.success('已切换到真实API模式')
    } else {
      enableMockAPI()
      setUseMockAPI(true)
      toast.success('已切换到模拟API模式')
    }
  }

  // 刷新页面
  const refreshPage = () => {
    window.location.reload()
  }

  // 清除所有localStorage数据
  const clearLocalStorage = () => {
    if (confirm('确定要清除所有本地存储数据吗？这将重置所有设置。')) {
      localStorage.clear()
      toast.success('本地存储已清除')
      setTimeout(refreshPage, 1000)
    }
  }

  // 显示系统信息
  const showSystemInfo = () => {
    const info = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      localStorage: !!window.localStorage,
      sessionStorage: !!window.sessionStorage,
      indexedDB: !!window.indexedDB,
      webSocket: !!window.WebSocket,
      serviceWorker: 'serviceWorker' in navigator,
      geolocation: 'geolocation' in navigator,
      notification: 'Notification' in window,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
    }

    console.group('🔧 系统信息')
    console.table(info)
    console.groupEnd()
    toast.success('系统信息已输出到控制台')
  }

  // 在生产环境中且未显示开发工具时不渲染
  if (isProduction && !showInProduction && !localStorage.getItem('show-dev-tools')) {
    return null
  }

  return (
    <>
      {/* 开发工具触发按钮 */}
      {!isOpen && (
        <button
          onClick={() => setIsOpen(true)}
          className={`fixed bottom-4 right-4 z-50 p-3 bg-gray-800 text-white rounded-full shadow-lg hover:bg-gray-700 transition-colors ${className}`}
          title="开发工具 (Ctrl+Shift+D)"
        >
          <Code className="h-5 w-5" />
        </button>
      )}

      {/* 开发工具面板 */}
      {isOpen && (
        <div className="fixed bottom-4 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-xl p-4 w-80">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              <h3 className="font-semibold text-gray-900 dark:text-white">开发工具</h3>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ✕
            </button>
          </div>

          <div className="space-y-3">
            {/* API模式切换 */}
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center space-x-2">
                {useMockAPI ? (
                  <Database className="h-4 w-4 text-blue-500" />
                ) : (
                  <Wifi className="h-4 w-4 text-green-500" />
                )}
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {useMockAPI ? '模拟API' : '真实API'}
                </span>
              </div>
              <button
                onClick={toggleMockAPI}
                className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                  useMockAPI
                    ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                    : 'bg-green-100 text-green-700 hover:bg-green-200'
                }`}
              >
                切换
              </button>
            </div>

            {/* 快捷操作 */}
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={refreshPage}
                className="flex items-center justify-center space-x-1 p-2 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
              >
                <RefreshCw className="h-4 w-4" />
                <span className="text-xs">刷新</span>
              </button>

              <button
                onClick={showSystemInfo}
                className="flex items-center justify-center space-x-1 p-2 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
              >
                <Settings className="h-4 w-4" />
                <span className="text-xs">系统信息</span>
              </button>
            </div>

            {/* 危险操作 */}
            <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
              <button
                onClick={clearLocalStorage}
                className="w-full p-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors text-sm"
              >
                清除本地存储
              </button>
            </div>

            {/* 环境信息 */}
            <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
              <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                <div>环境: {isProduction ? '生产' : '开发'}</div>
                <div>模式: {useMockAPI ? '模拟数据' : '真实数据'}</div>
                <div>快捷键: Ctrl+Shift+D</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default DevTools