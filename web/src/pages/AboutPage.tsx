import React from 'react'
import {
  Info,
  GitBranch,
  ExternalLink,
  Bell,
  Heart,
  Code,
  Database,
  Globe,
  Shield,
  Zap,
  Users,
} from 'lucide-react'

const AboutPage: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Info className="h-8 w-8 text-indigo-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">关于 ProxyFlow</h1>
            <p className="text-gray-600">高性能代理服务管理系统</p>
          </div>
        </div>
        <p className="text-gray-700 leading-relaxed">
          ProxyFlow 是一个现代化的代理服务管理平台，专为高性能、高可用性的代理服务而设计。
          它提供了完整的代理管理、监控、分析和自动化功能，帮助您轻松管理大规模的代理服务。
        </p>
      </div>

      {/* 主要功能特性 */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
          <Zap className="h-6 w-6 text-yellow-500 mr-2" />
          核心特性
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="flex items-start space-x-3">
            <Globe className="h-6 w-6 text-blue-500 mt-1" />
            <div>
              <h3 className="font-medium text-gray-900">智能代理管理</h3>
              <p className="text-sm text-gray-600 mt-1">支持多种代理协议，智能路由和负载均衡</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Shield className="h-6 w-6 text-green-500 mt-1" />
            <div>
              <h3 className="font-medium text-gray-900">安全可靠</h3>
              <p className="text-sm text-gray-600 mt-1">多层安全防护，API密钥管理，访问控制</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Database className="h-6 w-6 text-purple-500 mt-1" />
            <div>
              <h3 className="font-medium text-gray-900">实时监控</h3>
              <p className="text-sm text-gray-600 mt-1">全面的性能监控和健康检查机制</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Code className="h-6 w-6 text-indigo-500 mt-1" />
            <div>
              <h3 className="font-medium text-gray-900">RESTful API</h3>
              <p className="text-sm text-gray-600 mt-1">完整的API接口，支持自动化集成</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Users className="h-6 w-6 text-orange-500 mt-1" />
            <div>
              <h3 className="font-medium text-gray-900">多用户支持</h3>
              <p className="text-sm text-gray-600 mt-1">角色权限管理，团队协作功能</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <Heart className="h-6 w-6 text-red-500 mt-1" />
            <div>
              <h3 className="font-medium text-gray-900">开源免费</h3>
              <p className="text-sm text-gray-600 mt-1">MIT许可证，完全开源，社区驱动</p>
            </div>
          </div>
        </div>
      </div>

      {/* 版本信息和技术栈 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 版本信息 */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">版本信息</h2>
          <div className="space-y-4">
            <div className="flex justify-between items-center py-2 border-b border-gray-100">
              <span className="text-gray-600">当前版本</span>
              <span className="font-medium text-gray-900">v1.0.0</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-100">
              <span className="text-gray-600">发布日期</span>
              <span className="font-medium text-gray-900">2024年12月</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-100">
              <span className="text-gray-600">许可证</span>
              <span className="font-medium text-gray-900">MIT License</span>
            </div>
            <div className="flex justify-between items-center py-2">
              <span className="text-gray-600">构建状态</span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                稳定版本
              </span>
            </div>
          </div>
        </div>

        {/* 技术栈 */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">技术栈</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">前端技术</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p>• React 18 + TypeScript</p>
                <p>• Vite + Tailwind CSS</p>
                <p>• Recharts + Lucide Icons</p>
                <p>• React Router + Axios</p>
              </div>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">后端技术</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p>• Go + Gin Framework</p>
                <p>• PostgreSQL + Redis</p>
                <p>• JWT + CORS</p>
                <p>• Docker + Docker Compose</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 开源项目信息 */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">开源项目</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <p className="text-gray-700 mb-4">
              ProxyFlow 是一个开源项目，我们欢迎社区的贡献和反馈。
              无论是代码贡献、问题报告还是功能建议，都能帮助我们不断改进这个项目。
            </p>
            <p className="text-gray-700">
              项目遵循 MIT 许可证，您可以自由使用、修改和分发。
              我们相信开源的力量能够创造更好的软件。
            </p>
          </div>
          <div className="space-y-3">
            <a
              href={import.meta.env.VITE_GITHUB_URL || 'https://github.com/cosin2077/proxyflow'}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <GitBranch className="h-5 w-5 text-gray-600 mr-3" />
              <div className="flex-1">
                <div className="font-medium text-gray-900">查看源码</div>
                <div className="text-sm text-gray-600">GitHub 仓库</div>
              </div>
              <ExternalLink className="h-4 w-4 text-gray-400" />
            </a>
            <a
              href={`${import.meta.env.VITE_GITHUB_URL || 'https://github.com/cosin2077/proxyflow'}/issues`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Bell className="h-5 w-5 text-gray-600 mr-3" />
              <div className="flex-1">
                <div className="font-medium text-gray-900">报告问题</div>
                <div className="text-sm text-gray-600">提交 Issue</div>
              </div>
              <ExternalLink className="h-4 w-4 text-gray-400" />
            </a>
          </div>
        </div>
      </div>

      {/* 版权信息 */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="text-center">
          <p className="text-gray-600">
            © {new Date().getFullYear()} ProxyFlow. 基于 MIT 许可证开源。
          </p>
          <p className="text-sm text-gray-500 mt-2">感谢所有为这个项目做出贡献的开发者们 ❤️</p>
        </div>
      </div>
    </div>
  )
}

export default AboutPage