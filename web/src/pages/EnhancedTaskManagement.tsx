import React, { useState, useEffect } from 'react'
import {
  Plus,
  RefreshCw,
  Search,
  Eye,
  X,
  ListTodo,
  Filter,
  BarChart3,
  Settings,
  Clock,
  Target,
  Layers,
  Download,
  Upload,
  Play,
  Pause,
  ChevronDown,
  Grid3X3,
  List,
  Edit,
  Copy,
  MoreHorizontal,
  Trash2,
  Activity,
} from 'lucide-react'
import { taskAPI } from '../services/api'
import LoadingSpinner from '../components/common/LoadingSpinner'
import StatusBadge from '../components/common/StatusBadge'
import Modal from '../components/common/Modal'
import CreateTaskForm from '../components/task/CreateTaskForm'
import TaskDetailModal from '../components/task/TaskDetailModal'
import BatchUrlTestForm from '../components/task/BatchUrlTestForm'
import ProxyComparisonForm from '../components/task/ProxyComparisonForm'
import TaskTemplateForm from '../components/task/TaskTemplateForm'
import EnhancedBatchTaskForm from '../components/task/EnhancedBatchTaskForm'
import { formatDate, formatRelativeTime } from '../utils/helpers'
import { REFRESH_INTERVALS, TASK_STATUS } from '../utils/constants'
import toast from 'react-hot-toast'
import type { Task, TaskStatus } from '../types'

// 任务类型定义
export type TaskType =
  | 'single_url'
  | 'batch_url'
  | 'proxy_comparison'
  | 'scheduled'
  | 'template'
  | 'enhanced_batch'

// 视图模式
export type ViewMode = 'list' | 'grid'

// 任务类型配置
export const TASK_TYPE_CONFIG = {
  single_url: {
    label: '单URL测试',
    description: '测试单个URL的可访问性',
    icon: Target,
    color: 'blue',
  },
  batch_url: {
    label: '批量URL测试',
    description: '批量测试多个URL',
    icon: Layers,
    color: 'green',
  },
  proxy_comparison: {
    label: '代理性能对比',
    description: '对比多个代理的性能',
    icon: BarChart3,
    color: 'purple',
  },
  scheduled: {
    label: '定时任务',
    description: '按计划执行的任务',
    icon: Clock,
    color: 'orange',
  },
  template: {
    label: '模板任务',
    description: '基于模板创建的任务',
    icon: Settings,
    color: 'gray',
  },
  enhanced_batch: {
    label: '增强批量任务',
    description: '支持多URL、多代理的批量任务创建',
    icon: Layers,
    color: 'indigo',
  },
} as const

const EnhancedTaskManagement: React.FC = () => {
  // 基础状态
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [refreshing, setRefreshing] = useState<boolean>(false)

  // 筛选和搜索状态
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [statusFilter, setStatusFilter] = useState<TaskStatus | 'all'>('all')
  const [taskTypeFilter, setTaskTypeFilter] = useState<TaskType | 'all'>('all')
  const [priorityFilter, setPriorityFilter] = useState<number | 'all'>('all')

  // 视图状态
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [selectedTasks, setSelectedTasks] = useState<Set<string>>(new Set())

  // 模态框状态
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false)
  const [createTaskType, setCreateTaskType] = useState<TaskType>('single_url')
  const [selectedTask, setSelectedTask] = useState<Task | null>(null)
  const [showDetailModal, setShowDetailModal] = useState<boolean>(false)
  const [showBatchActionsModal, setShowBatchActionsModal] = useState<boolean>(false)
  const [editingTask, setEditingTask] = useState<Task | null>(null)

  // 操作状态
  const [cancellingId, setCancellingId] = useState<string | null>(null)
  const [deletingId, setDeletingId] = useState<string | null>(null)
  const [batchOperationLoading, setBatchOperationLoading] = useState<boolean>(false)

  // 获取任务列表
  const fetchTasks = async (showLoading = true): Promise<void> => {
    try {
      if (showLoading) setLoading(true)
      else setRefreshing(true)

      const response = await taskAPI.getTasks()
      setTasks(response.data.data || [])
    } catch (error) {
      console.error('Failed to fetch tasks:', error)
      toast.error('获取任务列表失败')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // 取消任务
  const handleCancel = async (id: string): Promise<void> => {
    if (!confirm('确定要取消这个任务吗？')) return

    try {
      setCancellingId(id)
      await taskAPI.cancelTask(id)
      toast.success('任务已取消')
      fetchTasks(false)
    } catch (error) {
      toast.error('取消任务失败')
    } finally {
      setCancellingId(null)
    }
  }

  // 批量取消任务
  const handleBatchCancel = async (): Promise<void> => {
    if (selectedTasks.size === 0) return
    if (!confirm(`确定要取消选中的 ${selectedTasks.size} 个任务吗？`)) return

    try {
      setBatchOperationLoading(true)
      const promises = Array.from(selectedTasks).map((id) => taskAPI.cancelTask(id))
      await Promise.all(promises)
      toast.success(`已取消 ${selectedTasks.size} 个任务`)
      setSelectedTasks(new Set())
      fetchTasks(false)
    } catch (error) {
      toast.error('批量取消任务失败')
    } finally {
      setBatchOperationLoading(false)
      setShowBatchActionsModal(false)
    }
  }

  // 查看任务详情
  const handleViewDetail = (task: Task): void => {
    setSelectedTask(task)
    setShowDetailModal(true)
  }

  // 测试任务
  const handleTestTask = async (task: Task): Promise<void> => {
    try {
      // 创建一个测试任务副本
      const testTaskData = {
        name: `测试-${task.name}`,
        url: task.url,
        method: task.method,
        headers: task.headers || {},
        body: task.body || '',
        proxy_strategy: 'round_robin' as const,
        priority: task.priority,
        max_retries: 1, // 测试任务只重试1次
        timeout: task.timeout || 30,
      }

      await taskAPI.createTask(testTaskData)
      toast.success('测试任务已创建并开始执行')
      fetchTasks(false)
    } catch (error: any) {
      console.error('Failed to test task:', error)
      const errorMessage = error?.response?.data?.message || error?.message || '创建测试任务失败'
      toast.error(errorMessage)
    }
  }

  // 编辑任务
  const handleEditTask = (task: Task): void => {
    console.log('编辑任务:', task) // 调试信息
    setEditingTask(task)

    // 当前所有任务都是基础的单URL任务类型
    // 未来可以根据任务数据结构来判断类型
    setCreateTaskType('single_url')
    setShowCreateModal(true)
  }

  // 复制任务
  const handleCopyTask = async (task: Task): Promise<void> => {
    try {
      // 创建任务副本，转换为后端期望的格式
      const taskCopy = {
        name: `${task.name} - 副本`,
        url: task.url,
        method: task.method,
        headers: task.headers || {},
        body: task.body || '',
        proxy_strategy: task.proxy_strategy,
        priority: task.priority,
        max_retries: task.max_retries || 3,
        timeout: task.timeout || 30,
      }

      console.log('复制任务数据:', taskCopy) // 调试信息

      // 调用创建任务API
      await taskAPI.createTask(taskCopy)
      toast.success('任务复制成功')
      fetchTasks(false)
    } catch (error: any) {
      console.error('Failed to copy task:', error)
      // 显示详细错误信息
      const errorMessage = error?.response?.data?.message || error?.message || '复制任务失败'
      toast.error(errorMessage)
    }
  }

  // 删除任务（逻辑删除）
  const handleDeleteTask = async (task: Task): Promise<void> => {
    if (!confirm(`确定要删除任务"${task.name}"吗？任务将被标记为已删除。`)) return

    try {
      setDeletingId(task.id)
      // 使用逻辑删除，如果后端不支持则回退到物理删除
      try {
        await taskAPI.softDeleteTask(task.id)
        toast.success('任务已删除')
      } catch (softDeleteError) {
        console.warn('逻辑删除失败，尝试物理删除:', softDeleteError)
        await taskAPI.deleteTask(task.id)
        toast.success('任务删除成功')
      }
      fetchTasks(false)
    } catch (error: any) {
      console.error('Failed to delete task:', error)
      const errorMessage = error?.response?.data?.message || error?.message || '删除任务失败'
      toast.error(errorMessage)
    } finally {
      setDeletingId(null)
    }
  }

  // 任务选择处理
  const handleTaskSelect = (taskId: string, selected: boolean): void => {
    const newSelected = new Set(selectedTasks)
    if (selected) {
      newSelected.add(taskId)
    } else {
      newSelected.delete(taskId)
    }
    setSelectedTasks(newSelected)
  }

  // 全选/取消全选
  const handleSelectAll = (selected: boolean): void => {
    if (selected) {
      setSelectedTasks(new Set(filteredTasks.map((task) => task.id)))
    } else {
      setSelectedTasks(new Set())
    }
  }

  // 过滤任务
  const filteredTasks = tasks.filter((task) => {
    const matchesSearch =
      task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.url.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.method.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || task.status === statusFilter
    const matchesType = taskTypeFilter === 'all' || (task as any).type === taskTypeFilter
    const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter

    return matchesSearch && matchesStatus && matchesType && matchesPriority
  })

  // 获取状态统计
  const getStatusCount = (status: TaskStatus): number => {
    return tasks.filter((task) => task.status === status).length
  }

  // 获取类型统计
  const getTypeCount = (type: TaskType): number => {
    return tasks.filter((task) => (task as any).type === type).length
  }

  // 创建任务处理
  const handleCreateTask = (type: TaskType): void => {
    setCreateTaskType(type)
    setShowCreateModal(true)
  }

  // 初始加载和定时刷新
  useEffect(() => {
    fetchTasks()

    const interval = setInterval(() => {
      fetchTasks(false)
    }, REFRESH_INTERVALS.TASK_LIST)

    return () => clearInterval(interval)
  }, [])

  if (loading) {
    return <LoadingSpinner size="lg" text="加载任务列表..." />
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和快速操作 */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">智能任务管理</h1>
          <p className="mt-1 text-sm text-gray-500">企业级代理测试和监控任务管理平台</p>
        </div>

        <div className="mt-4 lg:mt-0 flex flex-wrap gap-3">
          <button onClick={() => fetchTasks(false)} disabled={refreshing} className="btn-outline">
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            刷新
          </button>

          {selectedTasks.size > 0 && (
            <button onClick={() => setShowBatchActionsModal(true)} className="btn-secondary">
              <Settings className="h-4 w-4 mr-2" />
              批量操作 ({selectedTasks.size})
            </button>
          )}

          <div className="relative">
            <button
              onClick={() => handleCreateTask('single_url')}
              className="btn-primary flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              创建任务
            </button>
          </div>
        </div>
      </div>

      {/* 任务类型快速创建卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {Object.entries(TASK_TYPE_CONFIG).map(([type, config]) => {
          const Icon = config.icon
          return (
            <div
              key={type}
              onClick={() => handleCreateTask(type as TaskType)}
              className="bg-white rounded-lg border border-gray-200 p-4 hover:border-blue-300 hover:shadow-md transition-all duration-200 cursor-pointer group"
            >
              <div className="flex items-center space-x-3">
                <div
                  className={`p-2 rounded-lg bg-${config.color}-100 group-hover:bg-${config.color}-200 transition-colors`}
                >
                  <Icon className={`h-5 w-5 text-${config.color}-600`} />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-900 truncate">{config.label}</h3>
                  <p className="text-xs text-gray-500 mt-1">
                    {getTypeCount(type as TaskType)} 个任务
                  </p>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总任务</p>
              <p className="text-2xl font-bold text-gray-900">{tasks.length}</p>
            </div>
            <ListTodo className="h-8 w-8 text-gray-400" />
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">运行中</p>
              <p className="text-2xl font-bold text-blue-600">{getStatusCount('running')}</p>
            </div>
            <Play className="h-8 w-8 text-blue-400" />
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">已完成</p>
              <p className="text-2xl font-bold text-green-600">{getStatusCount('completed')}</p>
            </div>
            <Target className="h-8 w-8 text-green-400" />
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">失败</p>
              <p className="text-2xl font-bold text-red-600">{getStatusCount('failed')}</p>
            </div>
            <X className="h-8 w-8 text-red-400" />
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">等待中</p>
              <p className="text-2xl font-bold text-yellow-600">{getStatusCount('pending')}</p>
            </div>
            <Clock className="h-8 w-8 text-yellow-400" />
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          {/* 搜索框 */}
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                className="pl-10 input w-full"
                placeholder="搜索任务名称、URL或方法..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* 筛选器 */}
          <div className="flex flex-wrap gap-3">
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <select
                className="pl-10 input w-32"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as TaskStatus | 'all')}
              >
                <option value="all">所有状态</option>
                <option value="pending">等待中</option>
                <option value="running">运行中</option>
                <option value="completed">已完成</option>
                <option value="failed">失败</option>
                <option value="cancelled">已取消</option>
              </select>
            </div>

            <select
              className="input w-36"
              value={taskTypeFilter}
              onChange={(e) => setTaskTypeFilter(e.target.value as TaskType | 'all')}
            >
              <option value="all">所有类型</option>
              {Object.entries(TASK_TYPE_CONFIG).map(([type, config]) => (
                <option key={type} value={type}>
                  {config.label}
                </option>
              ))}
            </select>

            <select
              className="input w-28"
              value={priorityFilter}
              onChange={(e) =>
                setPriorityFilter(e.target.value === 'all' ? 'all' : parseInt(e.target.value))
              }
            >
              <option value="all">所有优先级</option>
              <option value="4">紧急</option>
              <option value="3">高</option>
              <option value="2">普通</option>
              <option value="1">低</option>
            </select>
          </div>

          {/* 视图切换 */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
            >
              <List className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
            >
              <Grid3X3 className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* 任务列表/网格 */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">任务列表 ({filteredTasks.length})</h3>

            {filteredTasks.length > 0 && (
              <div className="flex items-center space-x-3">
                <label className="flex items-center space-x-2 text-sm text-gray-600">
                  <input
                    type="checkbox"
                    checked={
                      selectedTasks.size === filteredTasks.length && filteredTasks.length > 0
                    }
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span>全选</span>
                </label>

                {selectedTasks.size > 0 && (
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setShowBatchActionsModal(true)}
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      批量操作
                    </button>
                    <button
                      onClick={() => setSelectedTasks(new Set())}
                      className="text-sm text-gray-500 hover:text-gray-700"
                    >
                      取消选择
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 任务内容区域 */}
        <div className="p-6">
          {filteredTasks.length === 0 ? (
            <div className="text-center py-12">
              <ListTodo className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                {searchTerm ||
                statusFilter !== 'all' ||
                taskTypeFilter !== 'all' ||
                priorityFilter !== 'all'
                  ? '没有找到匹配的任务'
                  : '暂无任务'}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ||
                statusFilter !== 'all' ||
                taskTypeFilter !== 'all' ||
                priorityFilter !== 'all'
                  ? '尝试调整搜索或筛选条件'
                  : '点击上方按钮创建第一个任务'}
              </p>
            </div>
          ) : (
            <div
              className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
                  : 'space-y-4'
              }
            >
              {/* 这里会渲染任务项 */}
              {filteredTasks.map((task) => (
                <div key={task.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedTasks.has(task.id)}
                        onChange={(e) => handleTaskSelect(task.id, e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <h4 className="font-medium text-gray-900">{task.name}</h4>
                    </div>
                    <StatusBadge status={task.status} type="task" />
                  </div>

                  <p className="text-sm text-gray-600 mb-2">{task.url}</p>

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{task.method}</span>
                    <span>{formatRelativeTime(task.created_at)}</span>
                  </div>

                  {/* 质量评分显示 */}
                  {(task as any).quality_score && (
                    <div className="mt-2 flex items-center space-x-2">
                      <span className="text-xs text-gray-500">质量评分:</span>
                      <div className="flex items-center space-x-1">
                        <div
                          className={`w-2 h-2 rounded-full ${
                            (task as any).quality_score >= 80
                              ? 'bg-green-500'
                              : (task as any).quality_score >= 60
                                ? 'bg-yellow-500'
                                : 'bg-red-500'
                          }`}
                        ></div>
                        <span className="text-xs font-medium">
                          {(task as any).quality_score}/100
                        </span>
                      </div>
                    </div>
                  )}

                  {/* 响应时间和健康度状态 */}
                  <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                    {(task as any).response_time && (
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{(task as any).response_time}ms</span>
                      </div>
                    )}
                    {(task as any).health_status && (
                      <div className="flex items-center space-x-1">
                        <Activity className="h-3 w-3" />
                        <span
                          className={
                            (task as any).health_status === 'healthy'
                              ? 'text-green-600'
                              : (task as any).health_status === 'warning'
                                ? 'text-yellow-600'
                                : 'text-red-600'
                          }
                        >
                          {(task as any).health_status === 'healthy'
                            ? '健康'
                            : (task as any).health_status === 'warning'
                              ? '警告'
                              : '异常'}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="mt-3 flex items-center justify-between">
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <span>优先级: {task.priority}</span>
                      <span>•</span>
                      <span>重试: {task.max_retries || 3}次</span>
                    </div>

                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => handleViewDetail(task)}
                        className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded"
                        title="查看详情"
                      >
                        <Eye className="h-4 w-4" />
                      </button>

                      <button
                        onClick={() => handleTestTask(task)}
                        className="p-1 text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded"
                        title="测试任务"
                      >
                        <Play className="h-4 w-4" />
                      </button>

                      <button
                        onClick={() => handleCopyTask(task)}
                        className="p-1 text-green-600 hover:text-green-800 hover:bg-green-50 rounded"
                        title="复制任务"
                      >
                        <Copy className="h-4 w-4" />
                      </button>

                      {task.status !== 'running' && task.status !== 'completed' && (
                        <button
                          onClick={() => handleEditTask(task)}
                          className="p-1 text-orange-600 hover:text-orange-800 hover:bg-orange-50 rounded"
                          title="编辑任务"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                      )}

                      {task.status !== 'running' && (
                        <button
                          onClick={() => handleDeleteTask(task)}
                          disabled={deletingId === task.id}
                          className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded disabled:opacity-50"
                          title={deletingId === task.id ? '删除中...' : '删除任务'}
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}

                      {(task.status === 'pending' || task.status === 'running') && (
                        <button
                          onClick={() => handleCancel(task.id)}
                          disabled={cancellingId === task.id}
                          className="p-1 text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50 rounded disabled:opacity-50"
                          title={cancellingId === task.id ? '取消中...' : '取消任务'}
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 创建/编辑任务模态框 */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false)
          setEditingTask(null)
        }}
        title={`${editingTask ? '编辑' : '创建'}${TASK_TYPE_CONFIG[createTaskType]?.label || '任务'}`}
        size="lg"
      >
        {(createTaskType === 'single_url' || !createTaskType) && (
          <CreateTaskForm
            initialData={editingTask}
            isEditing={!!editingTask}
            onSuccess={() => {
              setShowCreateModal(false)
              setEditingTask(null)
              fetchTasks(false)
              toast.success(editingTask ? '任务更新成功' : '任务创建成功')
            }}
            onCancel={() => {
              setShowCreateModal(false)
              setEditingTask(null)
            }}
          />
        )}

        {createTaskType === 'batch_url' && (
          <BatchUrlTestForm
            initialData={editingTask}
            isEditing={!!editingTask}
            onSuccess={() => {
              setShowCreateModal(false)
              setEditingTask(null)
              fetchTasks(false)
              toast.success(editingTask ? '批量URL测试任务更新成功' : '批量URL测试任务创建成功')
            }}
            onCancel={() => {
              setShowCreateModal(false)
              setEditingTask(null)
            }}
          />
        )}

        {createTaskType === 'proxy_comparison' && (
          <ProxyComparisonForm
            initialData={editingTask}
            isEditing={!!editingTask}
            onSuccess={() => {
              setShowCreateModal(false)
              setEditingTask(null)
              fetchTasks(false)
              toast.success(editingTask ? '代理性能对比任务更新成功' : '代理性能对比任务创建成功')
            }}
            onCancel={() => {
              setShowCreateModal(false)
              setEditingTask(null)
            }}
          />
        )}

        {createTaskType === 'template' && (
          <TaskTemplateForm
            onSuccess={() => {
              setShowCreateModal(false)
              fetchTasks(false)
              toast.success('模板任务创建成功')
            }}
            onCancel={() => setShowCreateModal(false)}
          />
        )}

        {createTaskType === 'enhanced_batch' && (
          <EnhancedBatchTaskForm
            onSuccess={() => {
              setShowCreateModal(false)
              fetchTasks(false)
              toast.success('增强批量任务创建成功')
            }}
            onCancel={() => setShowCreateModal(false)}
          />
        )}

        {createTaskType === 'scheduled' && (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">定时任务功能</h3>
            <p className="text-gray-600">定时任务功能正在开发中，敬请期待...</p>
          </div>
        )}

        {/* 如果没有匹配的任务类型，显示默认的单URL表单 */}
        {!['single_url', 'batch_url', 'proxy_comparison', 'template', 'scheduled'].includes(
          createTaskType
        ) && (
          <CreateTaskForm
            initialData={editingTask}
            isEditing={!!editingTask}
            onSuccess={() => {
              setShowCreateModal(false)
              setEditingTask(null)
              fetchTasks(false)
              toast.success(editingTask ? '任务更新成功' : '任务创建成功')
            }}
            onCancel={() => {
              setShowCreateModal(false)
              setEditingTask(null)
            }}
          />
        )}
      </Modal>

      {/* 任务详情模态框 */}
      {selectedTask && (
        <TaskDetailModal
          task={selectedTask}
          isOpen={showDetailModal}
          onClose={() => {
            setShowDetailModal(false)
            setSelectedTask(null)
          }}
        />
      )}

      {/* 批量操作模态框 */}
      <Modal
        isOpen={showBatchActionsModal}
        onClose={() => setShowBatchActionsModal(false)}
        title="批量操作"
      >
        <div className="space-y-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              已选择 <strong>{selectedTasks.size}</strong> 个任务
            </p>
          </div>

          <div className="space-y-3">
            <button
              onClick={handleBatchCancel}
              disabled={batchOperationLoading}
              className="w-full btn-outline text-red-600 border-red-300 hover:bg-red-50"
            >
              {batchOperationLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  取消中...
                </>
              ) : (
                <>
                  <X className="h-4 w-4 mr-2" />
                  批量取消任务
                </>
              )}
            </button>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button onClick={() => setShowBatchActionsModal(false)} className="btn-outline">
              关闭
            </button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default EnhancedTaskManagement