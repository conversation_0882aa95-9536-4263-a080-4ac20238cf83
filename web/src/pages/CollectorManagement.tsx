import React, { useState, useEffect } from 'react'
import { 
  Play, 
  Pause, 
  Square, 
  RefreshCw, 
  Plus,
  Activity,
  BarChart3,
  Settings,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react'
import { collectorAPI } from '../services/api'
import CollectionProgress from '../components/collector/CollectionProgress'

interface CollectorStats {
  total_sources: number
  success_sources: number
  total_proxies: number
  valid_proxies: number
  duplicate_count: number
  current_phase: string
  processed_count: number
  total_count: number
  progress_percent: number
  speed: number
  eta: string
  memory_usage_mb: number
  shard_count: number
  active_shards: number
  completed_shards: number
}

interface CollectionTask {
  id: string
  status: string
  created_at: string
  started_at?: string
  completed_at?: string
  error?: string
  progress: {
    task_id: string
    phase: string
    processed_count: number
    total_count: number
    progress_percent: number
    speed: number
    eta: string
    status: string
    message: string
    memory_usage_mb: number
    worker_count: number
  }
}

const CollectorManagement: React.FC = () => {
  const [stats, setStats] = useState<CollectorStats | null>(null)
  const [tasks, setTasks] = useState<CollectionTask[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedTask, setSelectedTask] = useState<string | null>(null)

  // 获取采集器统计
  const fetchStats = async () => {
    try {
      const response = await collectorAPI.getStats()
      if (response.data.success) {
        setStats(response.data.data)
      }
    } catch (err) {
      console.error('Failed to fetch collector stats:', err)
    }
  }

  // 获取任务列表
  const fetchTasks = async () => {
    try {
      const response = await collectorAPI.tasks.list()
      if (response.data.success) {
        setTasks(response.data.data.tasks || [])
      }
    } catch (err) {
      console.error('Failed to fetch tasks:', err)
    }
  }

  // 创建新任务
  const createTask = async () => {
    try {
      const config = {
        test_mode: false,
        sources: ['all']
      }
      const response = await collectorAPI.tasks.create(config)
      if (response.data.success) {
        await fetchTasks()
        setSelectedTask(response.data.data.id)
      }
    } catch (err) {
      setError('Failed to create task')
      console.error('Failed to create task:', err)
    }
  }

  // 任务控制操作
  const handleTaskAction = async (taskId: string, action: 'pause' | 'resume' | 'cancel') => {
    try {
      switch (action) {
        case 'pause':
          await collectorAPI.tasks.pause(taskId)
          break
        case 'resume':
          await collectorAPI.tasks.resume(taskId)
          break
        case 'cancel':
          await collectorAPI.tasks.cancel(taskId)
          break
      }
      await fetchTasks()
    } catch (err) {
      setError(`Failed to ${action} task`)
      console.error(`Failed to ${action} task:`, err)
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Play className="h-4 w-4 text-green-500" />
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-blue-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case 'cancelled':
        return <Square className="h-4 w-4 text-gray-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      try {
        await Promise.all([fetchStats(), fetchTasks()])
      } catch (err) {
        setError('Failed to load data')
      } finally {
        setIsLoading(false)
      }
    }

    loadData()

    // 定期刷新数据
    const interval = setInterval(() => {
      fetchStats()
      fetchTasks()
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-500" />
        <span className="ml-2 text-gray-600">Loading collector data...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Collector Management</h1>
          <p className="text-gray-600">Monitor and manage proxy collection tasks</p>
        </div>
        <button
          onClick={createTask}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Collection Task
        </button>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <span className="text-red-700">{error}</span>
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-500 hover:text-red-700"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* 统计概览 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Proxies</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_proxies.toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Valid Proxies</p>
                <p className="text-2xl font-bold text-gray-900">{stats.valid_proxies.toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-purple-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Memory Usage</p>
                <p className="text-2xl font-bold text-gray-900">{stats.memory_usage_mb.toFixed(0)}MB</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Settings className="h-8 w-8 text-orange-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Shards</p>
                <p className="text-2xl font-bold text-gray-900">{stats.active_shards}/{stats.shard_count}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 任务列表 */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Collection Tasks</h2>
        </div>
        <div className="divide-y divide-gray-200">
          {tasks.length === 0 ? (
            <div className="px-6 py-8 text-center text-gray-500">
              No collection tasks found. Create a new task to get started.
            </div>
          ) : (
            tasks.map((task) => (
              <div key={task.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(task.status)}
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        Task {task.id.slice(-8)}
                      </p>
                      <p className="text-sm text-gray-500">
                        Created: {new Date(task.created_at).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {task.status === 'running' && (
                      <button
                        onClick={() => handleTaskAction(task.id, 'pause')}
                        className="p-2 text-yellow-600 hover:text-yellow-800"
                        title="Pause Task"
                      >
                        <Pause className="h-4 w-4" />
                      </button>
                    )}
                    
                    {task.status === 'paused' && (
                      <button
                        onClick={() => handleTaskAction(task.id, 'resume')}
                        className="p-2 text-green-600 hover:text-green-800"
                        title="Resume Task"
                      >
                        <Play className="h-4 w-4" />
                      </button>
                    )}
                    
                    {['running', 'paused'].includes(task.status) && (
                      <button
                        onClick={() => handleTaskAction(task.id, 'cancel')}
                        className="p-2 text-red-600 hover:text-red-800"
                        title="Cancel Task"
                      >
                        <Square className="h-4 w-4" />
                      </button>
                    )}
                    
                    <button
                      onClick={() => setSelectedTask(selectedTask === task.id ? null : task.id)}
                      className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800"
                    >
                      {selectedTask === task.id ? 'Hide Details' : 'Show Details'}
                    </button>
                  </div>
                </div>
                
                {selectedTask === task.id && (
                  <div className="mt-4">
                    <CollectionProgress
                      taskId={task.id}
                      autoRefresh={true}
                      refreshInterval={2000}
                    />
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}

export default CollectorManagement
