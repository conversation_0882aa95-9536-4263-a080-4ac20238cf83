import type {
  ProxyStatus,
  TaskStatus,
  ProxyType,
  HttpMethod,
  ProxyStrategy,
  TaskPriority,
} from '../types'

// 代理状态
export const PROXY_STATUS: Record<string, ProxyStatus> = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  FAILED: 'failed',
} as const

// 代理状态显示配置
export const PROXY_STATUS_CONFIG = {
  active: {
    label: '正常',
    color: 'success',
    bgColor: 'bg-success-100',
    textColor: 'text-success-800',
  },
  inactive: {
    label: '未激活',
    color: 'warning',
    bgColor: 'bg-warning-100',
    textColor: 'text-warning-800',
  },
  failed: {
    label: '失败',
    color: 'danger',
    bgColor: 'bg-danger-100',
    textColor: 'text-danger-800',
  },
} as const

// 代理类型
export const PROXY_TYPES: Record<string, ProxyType> = {
  HTTP: 'http',
  HTTPS: 'https',
  SOCKS5: 'socks5',
} as const

// 代理类型选项
export const PROXY_TYPE_OPTIONS: Array<{ value: ProxyType; label: string }> = [
  { value: PROXY_TYPES.HTTP, label: 'HTTP' },
  { value: PROXY_TYPES.HTTPS, label: 'HTTPS' },
  { value: PROXY_TYPES.SOCKS5, label: 'SOCKS5' },
]

// 任务状态
export const TASK_STATUS: Record<string, TaskStatus> = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const

// 任务状态显示配置
export const TASK_STATUS_CONFIG = {
  pending: {
    label: '等待中',
    color: 'gray',
    bgColor: 'bg-gray-100',
    textColor: 'text-gray-800',
  },
  running: {
    label: '运行中',
    color: 'primary',
    bgColor: 'bg-primary-100',
    textColor: 'text-primary-800',
  },
  completed: {
    label: '已完成',
    color: 'success',
    bgColor: 'bg-success-100',
    textColor: 'text-success-800',
  },
  failed: {
    label: '失败',
    color: 'danger',
    bgColor: 'bg-danger-100',
    textColor: 'text-danger-800',
  },
  cancelled: {
    label: '已取消',
    color: 'warning',
    bgColor: 'bg-warning-100',
    textColor: 'text-warning-800',
  },
} as const

// 任务优先级
export const TASK_PRIORITY: Record<string, TaskPriority> = {
  LOW: 1,
  NORMAL: 2,
  HIGH: 3,
  URGENT: 4,
} as const

// 任务优先级选项
export const TASK_PRIORITY_OPTIONS: Array<{ value: TaskPriority; label: string }> = [
  { value: TASK_PRIORITY.LOW, label: '低' },
  { value: TASK_PRIORITY.NORMAL, label: '普通' },
  { value: TASK_PRIORITY.HIGH, label: '高' },
  { value: TASK_PRIORITY.URGENT, label: '紧急' },
]

// 代理策略
export const PROXY_STRATEGIES: Record<string, ProxyStrategy> = {
  ROUND_ROBIN: 'round_robin',
  LEAST_USED: 'least_used',
  RANDOM: 'random',
  WEIGHTED: 'weighted',
} as const

// 代理策略选项
export const PROXY_STRATEGY_OPTIONS: Array<{ value: ProxyStrategy; label: string }> = [
  { value: PROXY_STRATEGIES.ROUND_ROBIN, label: '轮询' },
  { value: PROXY_STRATEGIES.LEAST_USED, label: '最少使用' },
  { value: PROXY_STRATEGIES.RANDOM, label: '随机' },
  { value: PROXY_STRATEGIES.WEIGHTED, label: '加权' },
]

// HTTP方法
export const HTTP_METHODS: Record<string, HttpMethod> = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH',
} as const

// HTTP方法选项
export const HTTP_METHOD_OPTIONS: Array<{ value: HttpMethod; label: string }> = [
  { value: HTTP_METHODS.GET, label: 'GET' },
  { value: HTTP_METHODS.POST, label: 'POST' },
  { value: HTTP_METHODS.PUT, label: 'PUT' },
  { value: HTTP_METHODS.DELETE, label: 'DELETE' },
  { value: HTTP_METHODS.PATCH, label: 'PATCH' },
]

// 用户角色
export const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  GUEST: 'guest',
} as const

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
} as const

// 刷新间隔（毫秒）
export const REFRESH_INTERVALS = {
  DASHBOARD: 30000, // 30秒
  PROXY_LIST: 60000, // 1分钟
  TASK_LIST: 10000, // 10秒
} as const
