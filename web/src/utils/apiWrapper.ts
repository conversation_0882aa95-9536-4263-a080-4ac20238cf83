import { AxiosResponse } from 'axios'
import { enhancedToast, specialToasts } from './toastConfig'
import { handleApiError, AppError } from './errorHandler'
import { validateDataType, preprocessFormData } from './validation'
import type { ApiResponse } from '../types'

// API调用选项
export interface ApiCallOptions {
  showLoading?: boolean
  loadingMessage?: string
  showSuccessToast?: boolean
  successMessage?: string
  showErrorToast?: boolean
  customErrorMessage?: string
  silent?: boolean // 完全静默，不显示任何通知
}

// API调用结果
export interface ApiCallResult<T> {
  success: boolean
  data?: T
  error?: AppError
  response?: AxiosResponse<ApiResponse<T>>
}

// 通用API调用包装器
export const apiCall = async <T>(
  apiFunction: () => Promise<AxiosResponse<ApiResponse<T>>>,
  options: ApiCallOptions = {}
): Promise<ApiCallResult<T>> => {
  const {
    showLoading = false,
    loadingMessage = '处理中...',
    showSuccessToast = false,
    successMessage,
    showErrorToast = true,
    customErrorMessage,
    silent = false,
  } = options

  let loadingToastId: string | undefined

  try {
    // 显示加载提示
    if (showLoading && !silent) {
      loadingToastId = enhancedToast.loading(loadingMessage)
    }

    // 执行API调用
    const response = await apiFunction()

    // 关闭加载提示
    if (loadingToastId) {
      enhancedToast.dismiss(loadingToastId)
    }

    // 检查响应数据
    const responseData = response.data
    if (!responseData.success && responseData.error) {
      // 业务层面的错误
      const error: AppError = {
        message: responseData.error,
        code: 'BUSINESS_ERROR',
        status: response.status,
      }

      if (showErrorToast && !silent) {
        enhancedToast.error(customErrorMessage || error.message)
      }

      return {
        success: false,
        error,
        response,
      }
    }

    // 显示成功提示
    if (showSuccessToast && !silent) {
      const message = successMessage || '操作成功'
      enhancedToast.success(message)
    }

    return {
      success: true,
      data: responseData.data,
      response,
    }
  } catch (error) {
    // 关闭加载提示
    if (loadingToastId) {
      enhancedToast.dismiss(loadingToastId)
    }

    // 处理错误
    const appError = handleApiError(error, customErrorMessage)

    // 如果不是静默模式且需要显示错误提示
    if (showErrorToast && !silent) {
      // handleApiError 已经显示了错误提示，这里不需要重复显示
    }

    return {
      success: false,
      error: appError,
    }
  }
}

// 批量API调用包装器
export const batchApiCall = async <T>(
  apiCalls: Array<{
    name: string
    apiFunction: () => Promise<AxiosResponse<ApiResponse<T>>>
    options?: ApiCallOptions
  }>,
  options: {
    showProgress?: boolean
    showSummary?: boolean
    continueOnError?: boolean
  } = {}
): Promise<{
  results: Array<ApiCallResult<T> & { name: string }>
  summary: {
    total: number
    success: number
    failed: number
  }
}> => {
  const { showProgress = false, showSummary = true, continueOnError = true } = options

  const results: Array<ApiCallResult<T> & { name: string }> = []
  let progressToastId: string | undefined

  if (showProgress) {
    progressToastId = enhancedToast.loading(`处理中... (0/${apiCalls.length})`)
  }

  for (let i = 0; i < apiCalls.length; i++) {
    const { name, apiFunction, options: callOptions } = apiCalls[i]

    try {
      // 更新进度
      if (progressToastId) {
        enhancedToast.dismiss(progressToastId)
        progressToastId = enhancedToast.loading(`处理中... (${i + 1}/${apiCalls.length})`)
      }

      const result = await apiCall(apiFunction, {
        ...callOptions,
        showLoading: false, // 由批量处理显示进度
        silent: true, // 批量处理时静默单个操作
      })

      results.push({ ...result, name })

      // 如果失败且不继续执行
      if (!result.success && !continueOnError) {
        break
      }
    } catch (error) {
      const appError = handleApiError(error)
      results.push({
        success: false,
        error: appError,
        name,
      })

      if (!continueOnError) {
        break
      }
    }
  }

  // 关闭进度提示
  if (progressToastId) {
    enhancedToast.dismiss(progressToastId)
  }

  // 计算汇总信息
  const summary = {
    total: results.length,
    success: results.filter((r) => r.success).length,
    failed: results.filter((r) => !r.success).length,
  }

  // 显示汇总信息
  if (showSummary) {
    specialToasts.batchOperationComplete(summary.success, summary.total)
  }

  return { results, summary }
}

// 重试API调用包装器
export const retryApiCall = async <T>(
  apiFunction: () => Promise<AxiosResponse<ApiResponse<T>>>,
  options: ApiCallOptions & {
    maxRetries?: number
    retryDelay?: number
    retryCondition?: (error: AppError) => boolean
  } = {}
): Promise<ApiCallResult<T>> => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    retryCondition = (error) => error.code === 'NETWORK_ERROR' || error.status === 500,
    ...apiCallOptions
  } = options

  let lastError: AppError | undefined

  for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
    const result = await apiCall(apiFunction, {
      ...apiCallOptions,
      showErrorToast: attempt === maxRetries + 1, // 只在最后一次尝试时显示错误
    })

    if (result.success) {
      return result
    }

    lastError = result.error

    // 检查是否应该重试
    if (attempt <= maxRetries && lastError && retryCondition(lastError)) {
      // 显示重试提示
      enhancedToast.info(`操作失败，正在重试... (${attempt}/${maxRetries})`)

      // 等待重试延迟
      await new Promise((resolve) => setTimeout(resolve, retryDelay * attempt))
      continue
    }

    break
  }

  return {
    success: false,
    error: lastError,
  }
}

// 缓存API调用包装器
const apiCache = new Map<string, { data: any; timestamp: number; ttl: number }>()

export const cachedApiCall = async <T>(
  cacheKey: string,
  apiFunction: () => Promise<AxiosResponse<ApiResponse<T>>>,
  options: ApiCallOptions & {
    ttl?: number // 缓存时间（毫秒）
    forceRefresh?: boolean
  } = {}
): Promise<ApiCallResult<T>> => {
  const {
    ttl = 5 * 60 * 1000, // 默认5分钟
    forceRefresh = false,
    ...apiCallOptions
  } = options

  // 检查缓存
  if (!forceRefresh) {
    const cached = apiCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return {
        success: true,
        data: cached.data,
      }
    }
  }

  // 执行API调用
  const result = await apiCall(apiFunction, apiCallOptions)

  // 缓存成功的结果
  if (result.success && result.data) {
    apiCache.set(cacheKey, {
      data: result.data,
      timestamp: Date.now(),
      ttl,
    })
  }

  return result
}

// 清除API缓存
export const clearApiCache = (cacheKey?: string) => {
  if (cacheKey) {
    apiCache.delete(cacheKey)
  } else {
    apiCache.clear()
  }
}

// 批量清除API缓存
export const clearApiCacheBatch = (cacheKeys: string[]) => {
  cacheKeys.forEach((key) => apiCache.delete(key))
}

// 获取缓存信息
export const getCacheInfo = () => {
  const entries = Array.from(apiCache.entries())
  return entries.map(([key, value]) => ({
    key,
    timestamp: value.timestamp,
    ttl: value.ttl,
    age: Date.now() - value.timestamp,
    expired: Date.now() - value.timestamp > value.ttl,
  }))
}

// 类型安全的API调用选项
export interface TypeSafeApiCallOptions<TRequest = any> extends ApiCallOptions {
  validateRequest?: boolean
  preprocessRequest?: boolean
  validateResponse?: boolean
  requestValidator?: (data: TRequest) => { valid: boolean; errors: string[] }
  responseValidator?: (data: any) => { valid: boolean; errors: string[] }
}

// 类型安全的API调用包装器
export const typeSafeApiCall = async <TRequest, TResponse>(
  apiFunction: (data: TRequest) => Promise<AxiosResponse<ApiResponse<TResponse>>>,
  requestData: TRequest,
  options: TypeSafeApiCallOptions<TRequest> = {}
): Promise<ApiCallResult<TResponse>> => {
  const {
    validateRequest = true,
    preprocessRequest = true,
    validateResponse = true,
    requestValidator,
    responseValidator,
    ...apiCallOptions
  } = options

  try {
    let processedData = requestData

    // 预处理请求数据
    if (preprocessRequest && validateDataType.isObject(requestData)) {
      processedData = preprocessFormData(requestData as any) as TRequest
    }

    // 验证请求数据
    if (validateRequest && requestValidator) {
      const validation = requestValidator(processedData)
      if (!validation.valid) {
        const errorMessage = `请求数据验证失败: ${validation.errors.join(', ')}`
        return {
          success: false,
          error: {
            message: errorMessage,
            code: 'VALIDATION_ERROR',
          },
        }
      }
    }

    // 执行API调用
    const result = await apiCall(() => apiFunction(processedData), apiCallOptions)

    // 验证响应数据
    if (result.success && validateResponse && responseValidator && result.data) {
      const validation = responseValidator(result.data)
      if (!validation.valid) {
        const errorMessage = `响应数据验证失败: ${validation.errors.join(', ')}`
        return {
          success: false,
          error: {
            message: errorMessage,
            code: 'RESPONSE_VALIDATION_ERROR',
          },
        }
      }
    }

    return result
  } catch (error) {
    const appError = handleApiError(error)
    return {
      success: false,
      error: appError,
    }
  }
}

// 类型安全的批量API调用
export const typeSafeBatchApiCall = async <TRequest, TResponse>(
  apiCalls: Array<{
    name: string
    apiFunction: (data: TRequest) => Promise<AxiosResponse<ApiResponse<TResponse>>>
    requestData: TRequest
    options?: TypeSafeApiCallOptions<TRequest>
  }>,
  batchOptions: {
    showProgress?: boolean
    showSummary?: boolean
    continueOnError?: boolean
  } = {}
): Promise<{
  results: Array<{ success: boolean; data?: TResponse; error?: AppError; name: string }>
  summary: { total: number; success: number; failed: number }
}> => {
  const { showProgress = false, showSummary = true, continueOnError = true } = batchOptions

  const results: Array<{ success: boolean; data?: TResponse; error?: AppError; name: string }> = []
  let progressToastId: string | undefined

  if (showProgress) {
    progressToastId = enhancedToast.loading(`处理中... (0/${apiCalls.length})`)
  }

  for (let i = 0; i < apiCalls.length; i++) {
    const { name, apiFunction, requestData, options } = apiCalls[i]

    try {
      // 更新进度
      if (progressToastId) {
        enhancedToast.dismiss(progressToastId)
        progressToastId = enhancedToast.loading(`处理中... (${i + 1}/${apiCalls.length})`)
      }

      const result = await typeSafeApiCall(apiFunction, requestData, {
        ...options,
        silent: true, // 批量处理时静默单个操作
      })

      results.push({ ...result, name })

      // 如果失败且不继续执行
      if (!result.success && !continueOnError) {
        break
      }
    } catch (error) {
      const appError = handleApiError(error)
      results.push({
        success: false,
        error: appError,
        name,
      })

      if (!continueOnError) {
        break
      }
    }
  }

  // 关闭进度提示
  if (progressToastId) {
    enhancedToast.dismiss(progressToastId)
  }

  // 计算汇总信息
  const summary = {
    total: results.length,
    success: results.filter((r) => r.success).length,
    failed: results.filter((r) => !r.success).length,
  }

  // 显示汇总信息
  if (showSummary) {
    specialToasts.batchOperationComplete(summary.success, summary.total)
  }

  return { results, summary }
}

// 预定义的API调用配置
export const apiCallConfigs = {
  // 静默调用（不显示任何通知）
  silent: {
    silent: true,
  },

  // 显示加载和成功提示
  withLoadingAndSuccess: (successMessage?: string) => ({
    showLoading: true,
    showSuccessToast: true,
    successMessage,
  }),

  // 只显示错误提示
  errorOnly: {
    showErrorToast: true,
  },

  // 保存操作
  save: {
    showLoading: true,
    loadingMessage: '保存中...',
    showSuccessToast: true,
    successMessage: '保存成功',
  },

  // 删除操作
  delete: {
    showLoading: true,
    loadingMessage: '删除中...',
    showSuccessToast: true,
    successMessage: '删除成功',
  },

  // 更新操作
  update: {
    showLoading: true,
    loadingMessage: '更新中...',
    showSuccessToast: true,
    successMessage: '更新成功',
  },

  // 创建操作
  create: {
    showLoading: true,
    loadingMessage: '创建中...',
    showSuccessToast: true,
    successMessage: '创建成功',
  },

  // 类型安全的配置
  typeSafe: {
    validateRequest: true,
    preprocessRequest: true,
    validateResponse: true,
  },
}