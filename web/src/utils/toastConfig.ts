import toast, { ToastOptions, Renderable } from 'react-hot-toast'

// 默认Toast配置
export const defaultToastOptions: ToastOptions = {
  duration: 4000,
  position: 'top-right',
  style: {
    background: '#fff',
    color: '#374151',
    border: '1px solid #E5E7EB',
    borderRadius: '8px',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    maxWidth: '500px',
    padding: '16px',
    fontSize: '14px',
    lineHeight: '1.5',
  },
  // 允许用户手动关闭
  iconTheme: {
    primary: '#3B82F6',
    secondary: '#fff',
  },
}

// 成功通知配置
export const successToastOptions: ToastOptions = {
  ...defaultToastOptions,
  duration: 3000,
  style: {
    ...defaultToastOptions.style,
    background: '#F0FDF4',
    color: '#166534',
    border: '1px solid #22C55E',
  },
  iconTheme: {
    primary: '#22C55E',
    secondary: '#fff',
  },
}

// 错误通知配置
export const errorToastOptions: ToastOptions = {
  ...defaultToastOptions,
  duration: 5000,
  style: {
    ...defaultToastOptions.style,
    background: '#FEF2F2',
    color: '#DC2626',
    border: '1px solid #EF4444',
  },
  iconTheme: {
    primary: '#EF4444',
    secondary: '#fff',
  },
}

// 警告通知配置
export const warningToastOptions: ToastOptions = {
  ...defaultToastOptions,
  duration: 4000,
  style: {
    ...defaultToastOptions.style,
    background: '#FFFBEB',
    color: '#D97706',
    border: '1px solid #F59E0B',
  },
  iconTheme: {
    primary: '#F59E0B',
    secondary: '#fff',
  },
}

// 信息通知配置
export const infoToastOptions: ToastOptions = {
  ...defaultToastOptions,
  duration: 3000,
  style: {
    ...defaultToastOptions.style,
    background: '#EFF6FF',
    color: '#1D4ED8',
    border: '1px solid #3B82F6',
  },
  iconTheme: {
    primary: '#3B82F6',
    secondary: '#fff',
  },
}

// 加载通知配置
export const loadingToastOptions: ToastOptions = {
  ...defaultToastOptions,
  duration: Infinity, // 手动关闭
  style: {
    ...defaultToastOptions.style,
    background: '#F9FAFB',
    color: '#6B7280',
    border: '1px solid #D1D5DB',
  },
}

// 增强的Toast方法
export const enhancedToast = {
  success: (message: Renderable, options?: Partial<ToastOptions>) =>
    toast.success(message, { ...successToastOptions, ...options }),
  
  error: (message: Renderable, options?: Partial<ToastOptions>) =>
    toast.error(message, { ...errorToastOptions, ...options }),
  
  warning: (message: Renderable, options?: Partial<ToastOptions>) =>
    toast(message, { 
      ...warningToastOptions, 
      icon: '⚠️',
      ...options 
    }),
  
  info: (message: Renderable, options?: Partial<ToastOptions>) =>
    toast(message, { 
      ...infoToastOptions, 
      icon: 'ℹ️',
      ...options 
    }),
  
  loading: (message: Renderable, options?: Partial<ToastOptions>) =>
    toast.loading(message, { ...loadingToastOptions, ...options }),
  
  // 可关闭的通知
  dismissible: {
    success: (message: Renderable, options?: Partial<ToastOptions>) =>
      toast.success(message, { 
        ...successToastOptions, 
        duration: Infinity,
        ...options 
      }),
    
    error: (message: Renderable, options?: Partial<ToastOptions>) =>
      toast.error(message, { 
        ...errorToastOptions, 
        duration: Infinity,
        ...options 
      }),
    
    warning: (message: Renderable, options?: Partial<ToastOptions>) =>
      toast(message, { 
        ...warningToastOptions, 
        icon: '⚠️',
        duration: Infinity,
        ...options 
      }),
    
    info: (message: Renderable, options?: Partial<ToastOptions>) =>
      toast(message, { 
        ...infoToastOptions, 
        icon: 'ℹ️',
        duration: Infinity,
        ...options 
      }),
  },
  
  // 自定义通知
  custom: (message: Renderable, options?: ToastOptions) =>
    toast(message, { ...defaultToastOptions, ...options }),
  
  // 关闭所有通知
  dismiss: (toastId?: string) => toast.dismiss(toastId),
  
  // 移除所有通知
  remove: (toastId?: string) => toast.remove(toastId),
}

// 特殊用途的通知方法
export const specialToasts = {
  // 网络错误通知
  networkError: () =>
    enhancedToast.error('网络连接失败，请检查网络设置', {
      duration: 6000,
    }),
  
  // 权限错误通知
  permissionError: () =>
    enhancedToast.error('权限不足，无法执行此操作', {
      duration: 5000,
    }),
  
  // 登录过期通知
  sessionExpired: () =>
    enhancedToast.error('登录已过期，请重新登录', {
      duration: 5000,
    }),
  
  // 操作成功通知
  operationSuccess: (operation: string) =>
    enhancedToast.success(`${operation}成功`),
  
  // 操作失败通知
  operationFailed: (operation: string, reason?: string) =>
    enhancedToast.error(`${operation}失败${reason ? `：${reason}` : ''}`),
  
  // 数据保存成功
  saveSuccess: () =>
    enhancedToast.success('数据保存成功'),
  
  // 数据删除成功
  deleteSuccess: () =>
    enhancedToast.success('删除成功'),
  
  // 数据更新成功
  updateSuccess: () =>
    enhancedToast.success('更新成功'),
  
  // 复制成功
  copySuccess: () =>
    enhancedToast.success('复制成功'),
  
  // 上传成功
  uploadSuccess: () =>
    enhancedToast.success('上传成功'),
  
  // 下载开始
  downloadStarted: () =>
    enhancedToast.info('下载已开始'),
  
  // 批量操作完成
  batchOperationComplete: (successCount: number, totalCount: number) => {
    if (successCount === totalCount) {
      enhancedToast.success(`批量操作完成，成功处理 ${successCount} 项`)
    } else {
      enhancedToast.warning(`批量操作完成，成功 ${successCount} 项，失败 ${totalCount - successCount} 项`)
    }
  },
  
  // 验证错误
  validationError: (message: string) =>
    enhancedToast.error(`数据验证失败：${message}`, {
      duration: 5000,
    }),
  
  // 确认操作 - 使用简单的确认对话框
  confirmAction: (message: string, onConfirm: () => void) => {
    if (window.confirm(message)) {
      onConfirm()
      enhancedToast.success('操作已确认')
    }
  },
}

// 初始化Toast配置
export const initializeToastConfig = () => {
  // react-hot-toast 的配置通过 Toaster 组件进行设置
  // 这个函数保留用于将来可能的配置需求
  console.log('Toast configuration initialized')
}
