import { z } from 'zod'

// 基础验证规则
export const ValidationRules = {
  // 用户名验证
  username: z
    .string()
    .min(3, '用户名至少需要3个字符')
    .max(50, '用户名不能超过50个字符')
    .regex(/^[a-zA-Z0-9_-]+$/, '用户名只能包含字母、数字、下划线和连字符'),

  // 邮箱验证
  email: z.string().email('请输入有效的邮箱地址').max(255, '邮箱地址不能超过255个字符'),

  // 显示名称验证
  displayName: z.string().min(1, '显示名称不能为空').max(100, '显示名称不能超过100个字符'),

  // 时区验证
  timezone: z.string().min(1, '请选择时区'),

  // 语言验证
  language: z.enum(['zh-CN', 'en-US', 'ja-JP'], {
    errorMap: () => ({ message: '请选择支持的语言' }),
  }),

  // 主题验证
  theme: z.enum(['light', 'dark', 'auto'], {
    errorMap: () => ({ message: '请选择有效的主题' }),
  }),

  // 刷新间隔验证
  refreshInterval: z.number().min(5, '刷新间隔不能少于5秒').max(300, '刷新间隔不能超过300秒'),

  // 每页项目数验证
  itemsPerPage: z.number().min(10, '每页至少显示10项').max(100, '每页最多显示100项'),

  // 代理超时验证
  proxyTimeout: z.number().min(1, '超时时间至少1秒').max(300, '超时时间不能超过300秒'),

  // 最大重试次数验证
  maxRetries: z.number().min(0, '重试次数不能为负数').max(10, '重试次数不能超过10次'),

  // 代理策略验证
  proxyStrategy: z.enum(['round_robin', 'random', 'least_used', 'fastest'], {
    errorMap: () => ({ message: '请选择有效的代理策略' }),
  }),

  // 质量评分阈值验证
  qualityThreshold: z.number().min(0, '质量评分阈值不能为负数').max(1, '质量评分阈值不能超过1'),

  // IP地址验证
  ipAddress: z
    .string()
    .regex(
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      '请输入有效的IP地址'
    ),

  // 端口验证
  port: z.number().min(1, '端口号必须大于0').max(65535, '端口号不能超过65535'),

  // URL验证
  url: z.string().url('请输入有效的URL地址'),

  // Webhook URL验证
  webhookUrl: z.string().url('请输入有效的Webhook URL').optional().or(z.literal('')),

  // 时间格式验证
  timeFormat: z.enum(['12h', '24h'], {
    errorMap: () => ({ message: '请选择有效的时间格式' }),
  }),

  // 日期格式验证
  dateFormat: z.enum(['YYYY-MM-DD', 'MM/DD/YYYY', 'DD/MM/YYYY'], {
    errorMap: () => ({ message: '请选择有效的日期格式' }),
  }),
}

// 用户设置验证模式
export const UserSettingsSchema = z.object({
  profile: z.object({
    username: ValidationRules.username.optional(),
    email: ValidationRules.email.optional(),
    displayName: ValidationRules.displayName.optional(),
    timezone: ValidationRules.timezone.optional(),
    language: ValidationRules.language.optional()
  }).optional(),

  preferences: z.object({
    theme: ValidationRules.theme.optional(),
    autoRefresh: z.boolean().optional(),
    refreshInterval: ValidationRules.refreshInterval.optional(),
    itemsPerPage: ValidationRules.itemsPerPage.optional(),
    dateFormat: ValidationRules.dateFormat.optional(),
    timeFormat: ValidationRules.timeFormat.optional()
  }).optional()
})



// 代理设置验证模式
export const ProxySettingsSchema = z.object({
  defaultSettings: z.object({
    timeout: ValidationRules.proxyTimeout.optional(),
    maxRetries: ValidationRules.maxRetries.optional(),
    retryDelay: z.number().min(0.1, '重试延迟至少0.1秒').max(60, '重试延迟不能超过60秒').optional(),
    strategy: ValidationRules.proxyStrategy.optional(),
    healthCheckInterval: z.number().min(60, '健康检查间隔至少60秒').max(3600, '健康检查间隔不能超过3600秒').optional(),
    qualityCheckInterval: z.number().min(300, '质量检查间隔至少300秒').max(86400, '质量检查间隔不能超过86400秒').optional()
  }).optional(),

  connectionSettings: z.object({
    maxConcurrentConnections: z.number().min(1, '并发连接数至少为1').max(1000, '并发连接数不能超过1000').optional(),
    connectionPoolSize: z.number().min(1, '连接池大小至少为1').max(500, '连接池大小不能超过500').optional(),
    keepAliveTimeout: z.number().min(1, 'Keep-Alive超时至少1秒').max(300, 'Keep-Alive超时不能超过300秒').optional(),
    dnsTimeout: z.number().min(1, 'DNS超时至少1秒').max(30, 'DNS超时不能超过30秒').optional()
  }).optional(),

  qualitySettings: z.object({
    minQualityScore: ValidationRules.qualityThreshold.optional(),
    autoDisableFailed: z.boolean().optional(),
    failedThreshold: z.number().min(1, '失败阈值至少为1').max(100, '失败阈值不能超过100').optional(),
    recoveryCheckInterval: z.number().min(300, '恢复检查间隔至少300秒').max(7200, '恢复检查间隔不能超过7200秒').optional()
  }).optional(),

  geographicSettings: z.object({
    preferredCountries: z.array(z.string().length(2, '国家代码必须是2位字符')).optional(),
    blockedCountries: z.array(z.string().length(2, '国家代码必须是2位字符')).optional(),
    autoGeoDetection: z.boolean().optional()
  }).optional()
})

// 验证函数
export const validateUserSettings = (data: any) => {
  return UserSettingsSchema.safeParse(data)
}



export const validateProxySettings = (data: any) => {
  return ProxySettingsSchema.safeParse(data)
}

// 通用验证函数
export const validateSettings = (data: any, type: 'user' | 'proxy') => {
  switch (type) {
    case 'user':
      return validateUserSettings(data)
    case 'proxy':
      return validateProxySettings(data)
    default:
      return { success: false, error: { issues: [{ message: '未知的设置类型' }] } }
  }
}

// 获取验证错误消息
export const getValidationErrors = (result: any): string[] => {
  if (result.success) return []
  
  return result.error.issues.map((issue: any) => {
    const path = issue.path.length > 0 ? `${issue.path.join('.')}: ` : ''
    return `${path}${issue.message}`
  })
}

// 字段级验证函数
export const validateField = (fieldName: string, value: any): string | null => {
  try {
    const rule = ValidationRules[fieldName as keyof typeof ValidationRules]
    if (!rule) return null
    
    rule.parse(value)
    return null
  } catch (error: any) {
    if (error.issues && error.issues.length > 0) {
      return error.issues[0].message
    }
    return '验证失败'
  }
}

// 批量字段验证
export const validateFields = (fields: Record<string, any>): Record<string, string | null> => {
  const errors: Record<string, string | null> = {}
  
  Object.entries(fields).forEach(([fieldName, value]) => {
    errors[fieldName] = validateField(fieldName, value)
  })
  
  return errors
}

// 检查是否有验证错误
export const hasValidationErrors = (errors: Record<string, string | null>): boolean => {
  return Object.values(errors).some(error => error !== null)
}

// 自定义验证规则
export const customValidations = {
  // 验证静默时间设置
  validateQuietHours: (start: string, end: string): string | null => {
    if (!start || !end) return null
    
    const startTime = new Date(`2000-01-01 ${start}:00`)
    const endTime = new Date(`2000-01-01 ${end}:00`)
    
    if (startTime >= endTime) {
      return '结束时间必须晚于开始时间'
    }
    
    return null
  },

  // 验证IP地址列表
  validateIPList: (ips: string[]): string | null => {
    for (const ip of ips) {
      const result = ValidationRules.ipAddress.safeParse(ip)
      if (!result.success) {
        return `无效的IP地址: ${ip}`
      }
    }
    return null
  },

  // 验证国家代码列表
  validateCountryCodes: (codes: string[]): string | null => {
    const validCodes = /^[A-Z]{2}$/
    for (const code of codes) {
      if (!validCodes.test(code)) {
        return `无效的国家代码: ${code}`
      }
    }
    return null
  }
}

export default {
  ValidationRules,
  UserSettingsSchema,
  ProxySettingsSchema,
  validateUserSettings,
  validateProxySettings,
  validateSettings,
  getValidationErrors,
  validateField,
  validateFields,
  hasValidationErrors,
  customValidations
}
