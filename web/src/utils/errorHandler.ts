import toast from 'react-hot-toast'
import { AxiosError } from 'axios'

// 错误类型定义
export interface AppError {
  message: string
  code?: string
  status?: number
  details?: any
}

// 错误代码映射
export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const

// 用户友好的错误信息映射
const ERROR_MESSAGES = {
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ERROR_CODES.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [ERROR_CODES.VALIDATION_ERROR]: '输入数据有误，请检查后重试',
  [ERROR_CODES.PERMISSION_ERROR]: '权限不足，无法执行此操作',
  [ERROR_CODES.NOT_FOUND_ERROR]: '请求的资源不存在',
  [ERROR_CODES.SERVER_ERROR]: '服务器错误，请稍后重试',
  [ERROR_CODES.RATE_LIMIT_ERROR]: '请求过于频繁，请稍后再试',
  [ERROR_CODES.UNKNOWN_ERROR]: '未知错误，请稍后重试',
}

// 从Axios错误中提取错误信息
export const extractErrorFromAxios = (error: AxiosError): AppError => {
  const response = error.response
  
  if (response?.data) {
    const data = response.data as any
    
    // 提取后端返回的错误信息
    const message = data.error || data.message || data.detail
    
    return {
      message: message || getDefaultErrorMessage(response.status),
      code: getErrorCode(response.status),
      status: response.status,
      details: data.errors || data.details,
    }
  }
  
  // 网络错误
  if (error.request) {
    if (error.code === 'ECONNABORTED') {
      return {
        message: ERROR_MESSAGES[ERROR_CODES.TIMEOUT_ERROR],
        code: ERROR_CODES.TIMEOUT_ERROR,
      }
    }
    
    return {
      message: ERROR_MESSAGES[ERROR_CODES.NETWORK_ERROR],
      code: ERROR_CODES.NETWORK_ERROR,
    }
  }
  
  // 请求配置错误
  return {
    message: error.message || ERROR_MESSAGES[ERROR_CODES.UNKNOWN_ERROR],
    code: ERROR_CODES.UNKNOWN_ERROR,
  }
}

// 根据HTTP状态码获取错误代码
const getErrorCode = (status: number): string => {
  switch (status) {
    case 400:
    case 422:
      return ERROR_CODES.VALIDATION_ERROR
    case 401:
    case 403:
      return ERROR_CODES.PERMISSION_ERROR
    case 404:
      return ERROR_CODES.NOT_FOUND_ERROR
    case 429:
      return ERROR_CODES.RATE_LIMIT_ERROR
    case 500:
    case 502:
    case 503:
    case 504:
      return ERROR_CODES.SERVER_ERROR
    default:
      return ERROR_CODES.UNKNOWN_ERROR
  }
}

// 根据HTTP状态码获取默认错误信息
const getDefaultErrorMessage = (status: number): string => {
  switch (status) {
    case 400:
      return '请求参数错误'
    case 401:
      return '登录已过期，请重新登录'
    case 403:
      return '权限不足，无法执行此操作'
    case 404:
      return '请求的资源不存在'
    case 409:
      return '资源冲突，请检查数据'
    case 422:
      return '数据验证失败，请检查输入'
    case 429:
      return '请求过于频繁，请稍后再试'
    case 500:
      return '服务器内部错误，请稍后重试'
    case 502:
      return '网关错误，服务暂时不可用'
    case 503:
      return '服务暂时不可用，请稍后重试'
    case 504:
      return '请求超时，请稍后重试'
    default:
      return '请求失败，请稍后重试'
  }
}

// 显示错误通知
export const showErrorToast = (error: AppError | string, options?: {
  duration?: number
  dismissible?: boolean
}) => {
  const message = typeof error === 'string' ? error : error.message
  const duration = options?.duration || 4000
  
  return toast.error(message, {
    duration,
    style: {
      maxWidth: '500px',
    },
    // 允许用户手动关闭
    ...(options?.dismissible !== false && {
      icon: '❌',
    }),
  })
}

// 显示成功通知
export const showSuccessToast = (message: string, options?: {
  duration?: number
}) => {
  const duration = options?.duration || 3000
  
  return toast.success(message, {
    duration,
    style: {
      maxWidth: '500px',
    },
    icon: '✅',
  })
}

// 显示警告通知
export const showWarningToast = (message: string, options?: {
  duration?: number
}) => {
  const duration = options?.duration || 4000
  
  return toast(message, {
    duration,
    style: {
      maxWidth: '500px',
      background: '#FEF3C7',
      color: '#92400E',
      border: '1px solid #F59E0B',
    },
    icon: '⚠️',
  })
}

// 显示信息通知
export const showInfoToast = (message: string, options?: {
  duration?: number
}) => {
  const duration = options?.duration || 3000
  
  return toast(message, {
    duration,
    style: {
      maxWidth: '500px',
      background: '#DBEAFE',
      color: '#1E40AF',
      border: '1px solid #3B82F6',
    },
    icon: 'ℹ️',
  })
}

// 处理API调用错误的通用函数
export const handleApiError = (error: unknown, customMessage?: string): AppError => {
  let appError: AppError
  
  if (error instanceof Error && 'isAxiosError' in error) {
    appError = extractErrorFromAxios(error as AxiosError)
  } else if (error instanceof Error) {
    appError = {
      message: error.message,
      code: ERROR_CODES.UNKNOWN_ERROR,
    }
  } else {
    appError = {
      message: customMessage || ERROR_MESSAGES[ERROR_CODES.UNKNOWN_ERROR],
      code: ERROR_CODES.UNKNOWN_ERROR,
    }
  }
  
  // 显示错误通知
  showErrorToast(customMessage || appError.message)
  
  return appError
}

// 异步操作的错误处理包装器
export const withErrorHandling = async <T>(
  operation: () => Promise<T>,
  customErrorMessage?: string
): Promise<T | null> => {
  try {
    return await operation()
  } catch (error) {
    handleApiError(error, customErrorMessage)
    return null
  }
}

// 表单验证错误处理
export const handleValidationErrors = (errors: Record<string, string[]>): void => {
  const errorMessages = Object.entries(errors)
    .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
    .join('\n')
  
  showErrorToast(`数据验证失败:\n${errorMessages}`, { duration: 6000 })
}
