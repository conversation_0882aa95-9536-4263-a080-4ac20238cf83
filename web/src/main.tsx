import React from 'react'
import ReactDOM from 'react-dom/client'
import './index.css'
import App from './App'
import { BrowserRouter } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'
import { Toaster } from 'react-hot-toast'
import { defaultToastOptions } from './utils/toastConfig'

const rootElement = document.getElementById('root')
if (!rootElement) throw new Error('Failed to find the root element')

const root = ReactDOM.createRoot(rootElement)
root.render(
  <React.StrictMode>
    <BrowserRouter>
      <AuthProvider>
        <App />
        <Toaster
          position="top-right"
          gutter={8}
          containerClassName=""
          containerStyle={{
            top: 20,
            right: 20,
          }}
          toastOptions={defaultToastOptions}
        />
      </AuthProvider>
    </BrowserRouter>
  </React.StrictMode>
)