// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'user' | 'guest';
  status?: string;
  created_at?: string;
  updated_at?: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  role?: string;
}

export interface AuthResponse {
  access_token: string;
  user: User;
}

// API Key 相关类型
export interface APIKeyInfo {
  id: string;
  name: string;
  key_prefix: string;
  masked_api_key: string;
  permissions: Record<string, any>;
  is_active: boolean;
  expires_at?: string;
  last_used_at?: string;
  usage_count: number;
  rate_limit_per_minute: number;
  created_at: string;

  // 兼容旧版本前端字段
  api_key_created: string;
  api_key_last_used: string;
  api_key_usage_count: number;
}

export interface APIKeyResponse extends APIKeyInfo {
  api_key: string; // 完整的 API Key，仅在生成/重新生成时返回
}

export interface CreateAPIKeyRequest {
  name: string;
  expires_in_days?: number;
  permissions?: Record<string, any>;
  rate_limit_per_minute?: number;
  allowed_ips?: string[];
}

// 批量导入代理的请求类型
export interface BatchImportProxiesRequest {
  proxies: ProxyFormData[];
}

// 代理相关类型
export interface Proxy {
  id: string;
  host: string;
  port: number;
  type: 'http' | 'https' | 'socks5';
  username?: string;
  password?: string;
  weight?: number;
  status: 'active' | 'inactive' | 'failed';
  response_time?: number;
  use_count?: number;
  failures?: number;
  last_check?: string;
  created_at?: string;
  updated_at?: string;
  // 地理位置信息
  country_code?: string;
  city_name?: string;
  asn_name?: string;
  asn_number?: number;
  high_country_confidence?: boolean;
  // 质量评分信息（与后端models.Proxy保持一致）
  quality_score?: number;  // 综合质量评分（对应后端QualityScore）
  speed_score?: number;    // 速度评分
  stability_score?: number; // 稳定性评分
  reliability_score?: number; // 可靠性评分（注意：后端Proxy模型中暂无此字段，但QualityScore中有）
  anonymity_level?: 'transparent' | 'anonymous' | 'elite' | 'unknown';
  last_quality_check?: string;
  // 标签和场景信息
  tags?: ProxyTag[];
  scenario?: string;
  priority?: number;
}

// 质量评分类型（与后端models.QualityScore保持一致）
export interface QualityScore {
  overall: number;        // 综合评分
  speed: number;          // 速度评分
  stability: number;      // 稳定性评分
  reliability: number;    // 可靠性评分
  anonymity_level: 'transparent' | 'anonymous' | 'elite' | 'unknown';
}

// 代理标签类型
export interface ProxyTag {
  id: string;
  name: string;
  description?: string;
  color: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

// 标签请求类型
export interface ProxyTagRequest {
  name: string;
  description?: string;
  color?: string;
}

// 标签分配请求类型
export interface ProxyTagAssignRequest {
  proxy_ids: string[];
  tag_ids: string[];
}

// 智能路由筛选类型
export interface ProxyFilterByTags {
  tags?: string[];
  scenario?: string;
  strategy?: 'quality' | 'speed' | 'stability' | 'priority' | 'round_robin' | 'random' | 'least_used';
}

export interface ProxyFormData {
  host: string;
  port: string | number;
  type: 'http' | 'https' | 'socks5';
  username?: string;
  password?: string;
  weight?: number;
  // 地理位置信息（可选）
  country_code?: string;
  city_name?: string;
  asn_name?: string;
  asn_number?: number;
  high_country_confidence?: boolean;
}

export interface ProxyStats {
  total: number;
  active: number;
  inactive: number;
  failed: number;
  http?: number;
  https?: number;
  socks5?: number;
}

// 地理位置统计类型
export interface LocationStats {
  location: string;      // 位置名称
  country_code: string;  // 国家代码
  city_name: string;     // 城市名称
  total: number;         // 总数
  active: number;        // 活跃数
  inactive: number;      // 非活跃数
  failed: number;        // 失败数
  avg_response: number;  // 平均响应时间
  success_rate: number;  // 成功率
}

// 代理地理位置统计
export interface ProxyLocationStats {
  countries: LocationStats[]; // 按国家统计
  cities: LocationStats[];    // 按城市统计
  unknown: LocationStats;     // 未知位置统计
}

// 按地理位置分组的代理
export interface ProxyGroupByLocation {
  location: string;      // 位置标识
  name: string;          // 位置名称
  count: number;         // 代理数量
  proxies: Proxy[];      // 代理列表
  stats: LocationStats;  // 统计信息
}

// 地理位置筛选参数
export interface LocationFilter {
  countries: string[];   // 选中的国家代码
  cities: string[];      // 选中的城市（格式：country_code_city_name）
  searchTerm: string;    // 搜索关键词
}

// 任务相关类型
export interface Task {
  id: string;
  name: string;
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: string;
  proxy_strategy: 'round_robin' | 'least_used' | 'random' | 'weighted';
  priority: 1 | 2 | 3 | 4;
  max_retries?: number;
  timeout?: number;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  retry_count?: number;
  result?: string;
  error?: string;
  created_at?: string;
  started_at?: string;
  completed_at?: string;
  updated_at?: string;
}

export interface TaskFormData {
  name: string;
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers: Record<string, string>;
  body?: string;
  proxy_strategy: 'round_robin' | 'least_used' | 'random' | 'weighted';
  priority: 1 | 2 | 3 | 4;
  max_retries: number;
  timeout: number;
}

export interface TaskStats {
  total: number;
  pending: number;
  running: number;
  completed: number;
  failed: number;
  cancelled: number;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// 后端分页响应格式（与后端models.PaginatedResponse保持一致）
export interface BackendPaginatedResponse<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

// 代理列表查询参数
export interface ProxyListParams {
  page?: number;
  limit?: number;
  status?: 'active' | 'inactive' | 'failed';
  type?: 'http' | 'https' | 'socks5';
  country_code?: string;
  search?: string;
}

// 系统监控类型
export interface SystemHealth {
  status: 'ok' | 'error';
  uptime?: string;
  memory?: {
    used: number;
    total: number;
  };
  cpu?: {
    usage: number;
  };
}

// 组件 Props 类型
export interface StatusBadgeProps {
  status: string;
  type?: 'proxy' | 'task';
  className?: string;
}

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  text?: string;
  className?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  showCloseButton?: boolean;
}

export interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<any>;
  color?: 'blue' | 'green' | 'purple' | 'red' | 'yellow';
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
}

// 常量类型
export type ProxyStatus = 'active' | 'inactive' | 'failed';
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
export type ProxyType = 'http' | 'https' | 'socks5';
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
export type ProxyStrategy = 'round_robin' | 'least_used' | 'random' | 'weighted';
export type TaskPriority = 1 | 2 | 3 | 4;
export type UserRole = 'admin' | 'user' | 'guest';

// 错误类型
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

// 表单错误类型
export type FormErrors<T> = Partial<Record<keyof T, string>>;

// 环境变量类型
export interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string;
  readonly VITE_APP_TITLE: string;
}


