{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "create": "Create", "update": "Update", "refresh": "Refresh", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "loading": "Loading...", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "select": "Select", "selectAll": "Select All", "deselectAll": "Deselect All", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "settings": "Settings", "help": "Help", "about": "About", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register"}, "navigation": {"dashboard": "Dashboard", "proxyManagement": "Proxy Management", "taskManagement": "Task Management", "systemMonitoring": "System Monitoring", "apiKeys": "API Keys", "settings": "Settings"}, "dashboard": {"title": "Dashboard", "subtitle": "System overview and key metrics", "totalProxies": "Total Proxies", "activeProxies": "Active Proxies", "totalTasks": "Total Tasks", "runningTasks": "Running Tasks", "systemHealth": "System Health", "lastUpdate": "Last Update", "refreshData": "Refresh Data", "proxyStatus": "Proxy Status", "taskStatus": "Task Status", "qualityScore": "Quality Score", "recentTasks": "Recent Tasks"}, "proxy": {"title": "Proxy Management", "subtitle": "Manage and monitor proxy servers", "addProxy": "Add Proxy", "importProxies": "Import Proxies", "exportProxies": "Export Proxies", "batchOperations": "Batch Operations", "healthCheck": "Health Check", "qualityAssessment": "Quality Assessment", "host": "Host", "port": "Port", "type": "Type", "status": "Status", "location": "Location", "responseTime": "Response Time", "lastCheck": "Last Check", "active": "Active", "inactive": "Inactive", "failed": "Failed", "unknown": "Unknown", "selectMode": "Select Mode", "gridView": "Grid View", "listView": "List View", "compactView": "Compact View"}, "task": {"title": "Task Management", "subtitle": "Create and manage HTTP request tasks", "createTask": "Create Task", "batchTask": "Batch Task", "enhancedBatch": "<PERSON><PERSON><PERSON>", "taskName": "Task Name", "url": "URL", "method": "Method", "headers": "Headers", "body": "Body", "priority": "Priority", "timeout": "Timeout", "maxRetries": "Max Retries", "proxyStrategy": "Proxy Strategy", "status": "Status", "result": "Result", "createdAt": "Created At", "completedAt": "Completed At", "pending": "Pending", "running": "Running", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "testTask": "Test Task", "copyTask": "Copy Task", "viewDetails": "View Details"}, "monitoring": {"title": "System Monitoring", "subtitle": "Real-time system status and performance metrics", "systemHealth": "System Health", "performanceMetrics": "Performance Metrics", "systemLogs": "System Logs", "systemEvents": "System Events", "networkStatus": "Network Status", "diskUsage": "Disk Usage", "cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "uptime": "Uptime", "connections": "Connections", "realtimeConnection": "Realtime Connection", "offline": "Offline", "connecting": "Connecting"}, "apiKeys": {"title": "API Key Management", "subtitle": "Manage your API keys for programmatic access", "createKey": "Create Key", "keyName": "Key Name", "expiresIn": "Expires In", "neverExpires": "Never Expires", "permissions": "Permissions", "lastUsed": "Last Used", "usageCount": "Usage Count", "created": "Created", "active": "Active", "disabled": "Disabled", "expired": "Expired", "showKey": "Show Key", "hideKey": "Hide Key", "copyKey": "Copy Key", "regenerate": "Regenerate", "noKeys": "No API Keys", "noKeysDescription": "You don't have any API keys yet. Create an API key to start using programmatic access to system features.", "createFirstKey": "Create Your First API Key"}, "settings": {"title": "Settings", "subtitle": "Manage your account and application settings", "profile": "Profile", "notifications": "Notifications", "system": "System", "appearance": "Appearance", "proxy": "Proxy", "about": "About", "username": "Username", "email": "Email", "displayName": "Display Name", "timezone": "Timezone", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "auto": "Auto", "autoRefresh": "Auto Refresh", "refreshInterval": "Refresh Interval", "emailNotifications": "Email Notifications", "taskNotifications": "Task Notifications", "systemNotifications": "System Notifications", "defaultTimeout": "Default Timeout", "maxRetries": "Max Retries", "defaultStrategy": "Default Strategy", "saveSettings": "Save Settings", "resetSettings": "Reset Settings", "validationErrors": "Settings Validation Errors"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "loginFailed": "Login Failed", "invalidCredentials": "Invalid username or password", "sessionExpired": "Session expired, please login again"}, "validation": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidUrl": "Please enter a valid URL", "minLength": "Must be at least {min} characters", "maxLength": "Cannot exceed {max} characters", "minValue": "Value cannot be less than {min}", "maxValue": "Value cannot be greater than {max}", "invalidFormat": "Invalid format", "passwordMismatch": "Passwords do not match", "invalidJson": "Please enter valid JSON format"}, "messages": {"saveSuccess": "Saved successfully", "saveFailed": "Save failed", "deleteSuccess": "Deleted successfully", "deleteFailed": "Delete failed", "createSuccess": "Created successfully", "createFailed": "Create failed", "updateSuccess": "Updated successfully", "updateFailed": "Update failed", "loadFailed": "Load failed", "networkError": "Network error", "serverError": "Server error", "unknownError": "Unknown error", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "confirmDelete": "Are you sure you want to delete? This action cannot be undone.", "confirmReset": "Are you sure you want to reset? All changes will be lost.", "noData": "No data available", "noResults": "No results found", "emptyState": "Nothing here yet"}, "time": {"seconds": "seconds", "minutes": "minutes", "hours": "hours", "days": "days", "weeks": "weeks", "months": "months", "years": "years", "ago": "ago", "now": "just now", "today": "today", "yesterday": "yesterday", "tomorrow": "tomorrow"}, "units": {"bytes": "bytes", "kb": "KB", "mb": "MB", "gb": "GB", "tb": "TB", "ms": "ms", "percent": "%", "count": "", "times": "times"}}