import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react'
import { clearApiCache, getCacheInfo } from '../utils/apiWrapper'
import { enhancedToast } from '../utils/toastConfig'
import type { Proxy, ProxyStats, ProxyTag, User } from '../types'

// 应用状态类型定义
export interface AppState {
  // 用户状态
  user: User | null
  isAuthenticated: boolean

  // 代理相关状态
  proxies: {
    data: Proxy[]
    stats: ProxyStats | null
    lastUpdated: number | null
    loading: boolean
  }

  // 标签状态
  tags: {
    data: ProxyTag[]
    lastUpdated: number | null
    loading: boolean
  }

  // 全局UI状态
  ui: {
    sidebarCollapsed: boolean
    theme: 'light' | 'dark'
    notifications: boolean
    autoRefresh: boolean
    refreshInterval: number
  }

  // 缓存状态
  cache: {
    enabled: boolean
    size: number
    lastCleared: number | null
  }

  // 错误状态
  errors: {
    global: string | null
    api: string | null
  }
}

// 动作类型定义
export type AppAction =
  // 用户动作
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_AUTHENTICATED'; payload: boolean }

  // 代理动作
  | { type: 'SET_PROXIES'; payload: Proxy[] }
  | { type: 'SET_PROXY_STATS'; payload: ProxyStats | null }
  | { type: 'SET_PROXIES_LOADING'; payload: boolean }
  | { type: 'UPDATE_PROXY'; payload: Proxy }
  | { type: 'REMOVE_PROXY'; payload: string }
  | { type: 'ADD_PROXY'; payload: Proxy }

  // 标签动作
  | { type: 'SET_TAGS'; payload: ProxyTag[] }
  | { type: 'SET_TAGS_LOADING'; payload: boolean }
  | { type: 'UPDATE_TAG'; payload: ProxyTag }
  | { type: 'REMOVE_TAG'; payload: string }
  | { type: 'ADD_TAG'; payload: ProxyTag }

  // UI动作
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_THEME'; payload: 'light' | 'dark' }
  | { type: 'SET_NOTIFICATIONS'; payload: boolean }
  | { type: 'SET_AUTO_REFRESH'; payload: boolean }
  | { type: 'SET_REFRESH_INTERVAL'; payload: number }

  // 缓存动作
  | { type: 'SET_CACHE_ENABLED'; payload: boolean }
  | { type: 'UPDATE_CACHE_SIZE'; payload: number }
  | { type: 'CLEAR_CACHE' }

  // 错误动作
  | { type: 'SET_GLOBAL_ERROR'; payload: string | null }
  | { type: 'SET_API_ERROR'; payload: string | null }
  | { type: 'CLEAR_ERRORS' }

  // 重置动作
  | { type: 'RESET_STATE' }

// 初始状态
const initialState: AppState = {
  user: null,
  isAuthenticated: false,

  proxies: {
    data: [],
    stats: null,
    lastUpdated: null,
    loading: false,
  },

  tags: {
    data: [],
    lastUpdated: null,
    loading: false,
  },

  ui: {
    sidebarCollapsed: false,
    theme: 'light',
    notifications: true,
    autoRefresh: false,
    refreshInterval: 30000, // 30秒
  },

  cache: {
    enabled: true,
    size: 0,
    lastCleared: null,
  },

  errors: {
    global: null,
    api: null,
  },
}

// 状态reducer
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    // 用户动作
    case 'SET_USER':
      return { ...state, user: action.payload }

    case 'SET_AUTHENTICATED':
      return { ...state, isAuthenticated: action.payload }

    // 代理动作
    case 'SET_PROXIES':
      return {
        ...state,
        proxies: {
          ...state.proxies,
          data: action.payload,
          lastUpdated: Date.now(),
        },
      }

    case 'SET_PROXY_STATS':
      return {
        ...state,
        proxies: {
          ...state.proxies,
          stats: action.payload,
        },
      }

    case 'SET_PROXIES_LOADING':
      return {
        ...state,
        proxies: {
          ...state.proxies,
          loading: action.payload,
        },
      }

    case 'UPDATE_PROXY':
      return {
        ...state,
        proxies: {
          ...state.proxies,
          data: state.proxies.data.map((proxy) =>
            proxy.id === action.payload.id ? action.payload : proxy
          ),
        },
      }

    case 'REMOVE_PROXY':
      return {
        ...state,
        proxies: {
          ...state.proxies,
          data: state.proxies.data.filter((proxy) => proxy.id !== action.payload),
        },
      }

    case 'ADD_PROXY':
      return {
        ...state,
        proxies: {
          ...state.proxies,
          data: [...state.proxies.data, action.payload],
        },
      }

    // 标签动作
    case 'SET_TAGS':
      return {
        ...state,
        tags: {
          ...state.tags,
          data: action.payload,
          lastUpdated: Date.now(),
        },
      }

    case 'SET_TAGS_LOADING':
      return {
        ...state,
        tags: {
          ...state.tags,
          loading: action.payload,
        },
      }

    case 'UPDATE_TAG':
      return {
        ...state,
        tags: {
          ...state.tags,
          data: state.tags.data.map((tag) => (tag.id === action.payload.id ? action.payload : tag)),
        },
      }

    case 'REMOVE_TAG':
      return {
        ...state,
        tags: {
          ...state.tags,
          data: state.tags.data.filter((tag) => tag.id !== action.payload),
        },
      }

    case 'ADD_TAG':
      return {
        ...state,
        tags: {
          ...state.tags,
          data: [...state.tags.data, action.payload],
        },
      }

    // UI动作
    case 'TOGGLE_SIDEBAR':
      return {
        ...state,
        ui: {
          ...state.ui,
          sidebarCollapsed: !state.ui.sidebarCollapsed,
        },
      }

    case 'SET_THEME':
      return {
        ...state,
        ui: {
          ...state.ui,
          theme: action.payload,
        },
      }

    case 'SET_NOTIFICATIONS':
      return {
        ...state,
        ui: {
          ...state.ui,
          notifications: action.payload,
        },
      }

    case 'SET_AUTO_REFRESH':
      return {
        ...state,
        ui: {
          ...state.ui,
          autoRefresh: action.payload,
        },
      }

    case 'SET_REFRESH_INTERVAL':
      return {
        ...state,
        ui: {
          ...state.ui,
          refreshInterval: action.payload,
        },
      }

    // 缓存动作
    case 'SET_CACHE_ENABLED':
      return {
        ...state,
        cache: {
          ...state.cache,
          enabled: action.payload,
        },
      }

    case 'UPDATE_CACHE_SIZE':
      return {
        ...state,
        cache: {
          ...state.cache,
          size: action.payload,
        },
      }

    case 'CLEAR_CACHE':
      return {
        ...state,
        cache: {
          ...state.cache,
          size: 0,
          lastCleared: Date.now(),
        },
      }

    // 错误动作
    case 'SET_GLOBAL_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          global: action.payload,
        },
      }

    case 'SET_API_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          api: action.payload,
        },
      }

    case 'CLEAR_ERRORS':
      return {
        ...state,
        errors: {
          global: null,
          api: null,
        },
      }

    // 重置动作
    case 'RESET_STATE':
      return initialState

    default:
      return state
  }
}

// Context类型定义
interface AppStateContextType {
  state: AppState
  dispatch: React.Dispatch<AppAction>

  // 便捷方法
  clearCache: () => void
  updateCacheInfo: () => void
  resetApp: () => void
}

// 创建Context
const AppStateContext = createContext<AppStateContextType | undefined>(undefined)

// Provider组件
interface AppStateProviderProps {
  children: ReactNode
}

export const AppStateProvider: React.FC<AppStateProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState)

  // 便捷方法
  const clearCache = () => {
    clearApiCache()
    dispatch({ type: 'CLEAR_CACHE' })
    enhancedToast.success('缓存已清除')
  }

  const updateCacheInfo = () => {
    const cacheInfo = getCacheInfo()
    dispatch({ type: 'UPDATE_CACHE_SIZE', payload: cacheInfo.length })
  }

  const resetApp = () => {
    clearCache()
    dispatch({ type: 'RESET_STATE' })
    enhancedToast.info('应用状态已重置')
  }

  // 从localStorage加载设置
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem('app_settings')
      if (savedSettings) {
        const settings = JSON.parse(savedSettings)

        if (settings.ui) {
          if (typeof settings.ui.sidebarCollapsed === 'boolean') {
            dispatch({ type: 'TOGGLE_SIDEBAR' })
          }
          if (settings.ui.theme) {
            dispatch({ type: 'SET_THEME', payload: settings.ui.theme })
          }
          if (typeof settings.ui.notifications === 'boolean') {
            dispatch({ type: 'SET_NOTIFICATIONS', payload: settings.ui.notifications })
          }
          if (typeof settings.ui.autoRefresh === 'boolean') {
            dispatch({ type: 'SET_AUTO_REFRESH', payload: settings.ui.autoRefresh })
          }
          if (typeof settings.ui.refreshInterval === 'number') {
            dispatch({ type: 'SET_REFRESH_INTERVAL', payload: settings.ui.refreshInterval })
          }
        }

        if (settings.cache && typeof settings.cache.enabled === 'boolean') {
          dispatch({ type: 'SET_CACHE_ENABLED', payload: settings.cache.enabled })
        }
      }
    } catch (error) {
      console.error('Failed to load app settings:', error)
    }
  }, [])

  // 保存设置到localStorage
  useEffect(() => {
    try {
      const settings = {
        ui: state.ui,
        cache: { enabled: state.cache.enabled },
      }
      localStorage.setItem('app_settings', JSON.stringify(settings))
    } catch (error) {
      console.error('Failed to save app settings:', error)
    }
  }, [state.ui, state.cache.enabled])

  // 定期更新缓存信息
  useEffect(() => {
    updateCacheInfo()
    const interval = setInterval(updateCacheInfo, 10000) // 每10秒更新一次
    return () => clearInterval(interval)
  }, [])

  const value: AppStateContextType = {
    state,
    dispatch,
    clearCache,
    updateCacheInfo,
    resetApp,
  }

  return <AppStateContext.Provider value={value}>{children}</AppStateContext.Provider>
}

// Hook
export const useAppState = (): AppStateContextType => {
  const context = useContext(AppStateContext)
  if (!context) {
    throw new Error('useAppState must be used within an AppStateProvider')
  }
  return context
}