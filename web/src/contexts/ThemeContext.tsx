import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// 主题类型定义
export type Theme = 'light' | 'dark' | 'auto'
export type ResolvedTheme = 'light' | 'dark'

// 主题配置
export interface ThemeConfig {
  theme: Theme
  resolvedTheme: ResolvedTheme
  systemTheme: ResolvedTheme
}

// 主题上下文类型
interface ThemeContextType {
  theme: Theme
  resolvedTheme: ResolvedTheme
  systemTheme: ResolvedTheme
  setTheme: (theme: Theme) => void
  toggleTheme: () => void
  isDark: boolean
  isLight: boolean
  isAuto: boolean
}

// 创建主题上下文
const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

// 检测系统主题
function getSystemTheme(): ResolvedTheme {
  if (typeof window === 'undefined') return 'light'
  
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
}

// 获取存储的主题
function getStoredTheme(): Theme {
  if (typeof window === 'undefined') return 'auto'
  
  const stored = localStorage.getItem('theme') as Theme
  return stored && ['light', 'dark', 'auto'].includes(stored) ? stored : 'auto'
}

// 解析主题
function resolveTheme(theme: Theme, systemTheme: ResolvedTheme): ResolvedTheme {
  return theme === 'auto' ? systemTheme : theme
}

// 应用主题到DOM
function applyTheme(resolvedTheme: ResolvedTheme) {
  const root = document.documentElement
  
  if (resolvedTheme === 'dark') {
    root.classList.add('dark')
    root.style.colorScheme = 'dark'
  } else {
    root.classList.remove('dark')
    root.style.colorScheme = 'light'
  }
  
  // 更新meta标签
  const metaThemeColor = document.querySelector('meta[name="theme-color"]')
  if (metaThemeColor) {
    metaThemeColor.setAttribute('content', resolvedTheme === 'dark' ? '#1f2937' : '#ffffff')
  }
}

// 主题提供者组件
interface ThemeProviderProps {
  children: ReactNode
  defaultTheme?: Theme
  enableSystem?: boolean
  disableTransitionOnChange?: boolean
}

export function ThemeProvider({
  children,
  defaultTheme = 'auto',
  enableSystem = true,
  disableTransitionOnChange = false
}: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>(() => {
    return getStoredTheme() || defaultTheme
  })
  
  const [systemTheme, setSystemTheme] = useState<ResolvedTheme>(() => {
    return getSystemTheme()
  })
  
  const resolvedTheme = resolveTheme(theme, systemTheme)

  // 设置主题
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme)
    localStorage.setItem('theme', newTheme)
  }

  // 切换主题
  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark')
    } else if (theme === 'dark') {
      setTheme('auto')
    } else {
      setTheme('light')
    }
  }

  // 监听系统主题变化
  useEffect(() => {
    if (!enableSystem) return

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light')
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [enableSystem])

  // 应用主题变化
  useEffect(() => {
    // 禁用过渡动画以避免闪烁
    if (disableTransitionOnChange) {
      const css = document.createElement('style')
      css.appendChild(
        document.createTextNode(
          `*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}`
        )
      )
      document.head.appendChild(css)

      // 强制重绘
      (() => window.getComputedStyle(document.body))()

      // 移除样式
      setTimeout(() => {
        document.head.removeChild(css)
      }, 1)
    }

    applyTheme(resolvedTheme)
  }, [resolvedTheme, disableTransitionOnChange])

  // 初始化主题
  useEffect(() => {
    applyTheme(resolvedTheme)
  }, [])

  const contextValue: ThemeContextType = {
    theme,
    resolvedTheme,
    systemTheme,
    setTheme,
    toggleTheme,
    isDark: resolvedTheme === 'dark',
    isLight: resolvedTheme === 'light',
    isAuto: theme === 'auto'
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  )
}

// 使用主题的Hook
export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

// 主题选择器组件
interface ThemeOption {
  value: Theme
  label: string
  icon: string
  description: string
}

const themeOptions: ThemeOption[] = [
  {
    value: 'light',
    label: '浅色模式',
    icon: '☀️',
    description: '始终使用浅色主题'
  },
  {
    value: 'dark',
    label: '深色模式',
    icon: '🌙',
    description: '始终使用深色主题'
  },
  {
    value: 'auto',
    label: '跟随系统',
    icon: '🖥️',
    description: '根据系统设置自动切换'
  }
]

interface ThemeSelectorProps {
  className?: string
  showLabels?: boolean
  variant?: 'dropdown' | 'buttons' | 'radio'
}

export function ThemeSelector({ 
  className = '', 
  showLabels = true,
  variant = 'dropdown'
}: ThemeSelectorProps) {
  const { theme, setTheme, resolvedTheme } = useTheme()

  if (variant === 'buttons') {
    return (
      <div className={`flex space-x-2 ${className}`}>
        {themeOptions.map((option) => (
          <button
            key={option.value}
            onClick={() => setTheme(option.value)}
            className={`p-2 rounded-lg border transition-colors ${
              theme === option.value
                ? 'bg-primary-100 border-primary-300 text-primary-700'
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
            title={option.description}
          >
            <span className="text-lg">{option.icon}</span>
            {showLabels && (
              <span className="ml-2 text-sm">{option.label}</span>
            )}
          </button>
        ))}
      </div>
    )
  }

  if (variant === 'radio') {
    return (
      <div className={`space-y-3 ${className}`}>
        {themeOptions.map((option) => (
          <label key={option.value} className="flex items-center space-x-3 cursor-pointer">
            <input
              type="radio"
              name="theme"
              value={option.value}
              checked={theme === option.value}
              onChange={(e) => setTheme(e.target.value as Theme)}
              className="form-radio text-primary-600"
            />
            <span className="text-lg">{option.icon}</span>
            <div>
              <div className="text-sm font-medium text-gray-900">{option.label}</div>
              <div className="text-xs text-gray-500">{option.description}</div>
            </div>
          </label>
        ))}
      </div>
    )
  }

  // 默认下拉选择器
  return (
    <select
      value={theme}
      onChange={(e) => setTheme(e.target.value as Theme)}
      className={`form-select ${className}`}
    >
      {themeOptions.map((option) => (
        <option key={option.value} value={option.value}>
          {option.icon} {option.label}
        </option>
      ))}
    </select>
  )
}

// 主题切换按钮组件
interface ThemeToggleProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
  showLabel?: boolean
}

export function ThemeToggle({ 
  className = '', 
  size = 'md',
  showLabel = false 
}: ThemeToggleProps) {
  const { theme, toggleTheme, resolvedTheme } = useTheme()

  const sizeClasses = {
    sm: 'p-1.5 text-sm',
    md: 'p-2 text-base',
    lg: 'p-3 text-lg'
  }

  const currentOption = themeOptions.find(option => option.value === theme)

  return (
    <button
      onClick={toggleTheme}
      className={`
        inline-flex items-center justify-center rounded-lg border border-gray-300 
        bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 
        focus:ring-primary-500 focus:ring-offset-2 transition-colors
        dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700
        ${sizeClasses[size]} ${className}
      `}
      title={`当前: ${currentOption?.label} (点击切换)`}
    >
      <span className="text-lg">{currentOption?.icon}</span>
      {showLabel && (
        <span className="ml-2">{currentOption?.label}</span>
      )}
    </button>
  )
}

// 主题状态指示器
export function ThemeIndicator() {
  const { theme, resolvedTheme, systemTheme } = useTheme()

  return (
    <div className="text-xs text-gray-500 dark:text-gray-400">
      <div>主题设置: {theme}</div>
      <div>当前主题: {resolvedTheme}</div>
      {theme === 'auto' && (
        <div>系统主题: {systemTheme}</div>
      )}
    </div>
  )
}

export default {
  ThemeProvider,
  useTheme,
  ThemeSelector,
  ThemeToggle,
  ThemeIndicator
}
