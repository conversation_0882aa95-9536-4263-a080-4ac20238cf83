# Proxy Manager Web Dashboard

这是 Proxy Manager 的 React + TypeScript 前端应用，基于 Vite 构建，提供了一个现代化的 Web 界面来管理代理服务器和任务。

## 功能特性

- 🔐 **用户认证** - JWT token 认证，支持登录/注册
- 🖥️ **代理管理** - 添加、删除、查看代理列表和状态
- 📋 **任务管理** - 创建任务、查看任务状态和结果
- 📊 **系统监控** - 实时监控代理统计、任务统计、系统状态
- 📱 **响应式设计** - 支持桌面和移动端访问
- 🎨 **现代化UI** - 使用 Tailwind CSS 构建的美观界面
- ⚡ **高性能** - Vite 提供极快的开发体验
- 📝 **类型安全** - TypeScript 提供完整的类型检查

## 技术栈

- **Vite** - 现代化构建工具
- **React 18** - 前端框架
- **TypeScript** - 类型安全的 JavaScript
- **React Router** - 路由管理
- **Tailwind CSS** - 样式框架
- **Axios** - HTTP 客户端
- **Recharts** - 图表库
- **React Hot Toast** - 通知组件
- **Lucide React** - 图标库

## 开发环境设置

### 前置要求

- Node.js 16+
- npm、yarn 或 pnpm

### 安装依赖

```bash
cd web
# 使用 pnpm (推荐)
pnpm install

# 或使用 npm
npm install

# 或使用 yarn
yarn install
```

### 开发模式运行

```bash
# 使用 pnpm
pnpm dev

# 或使用 npm
npm run dev

# 或使用 yarn
yarn dev
```

应用将在 <http://localhost:3000> 启动，并自动代理 API 请求到 <http://localhost:8080>

### 构建生产版本

```bash
# 使用 pnpm
pnpm build

# 或使用 npm
npm run build

# 或使用 yarn
yarn build
```

构建文件将输出到 `build/` 目录。

### 预览生产版本

```bash
# 使用 pnpm
pnpm preview

# 或使用 npm
npm run preview

# 或使用 yarn
yarn preview
```

### 类型检查

```bash
# 使用 pnpm
pnpm type-check

# 或使用 npm
npm run type-check

# 或使用 yarn
yarn type-check
```

## 项目结构

```
web/
├── public/                 # 静态资源
├── src/
│   ├── components/         # React 组件
│   │   ├── auth/          # 认证相关组件
│   │   ├── common/        # 通用组件
│   │   ├── dashboard/     # 仪表板组件
│   │   ├── layout/        # 布局组件
│   │   ├── proxy/         # 代理管理组件
│   │   └── task/          # 任务管理组件
│   ├── contexts/          # React Context
│   ├── pages/             # 页面组件
│   ├── services/          # API 服务
│   ├── utils/             # 工具函数
│   ├── App.js             # 主应用组件
│   └── index.js           # 应用入口
├── package.json
└── tailwind.config.js     # Tailwind 配置
```

## API 集成

前端通过 Axios 与后端 RESTful API 集成：

- **认证接口**: `/api/v1/auth/*`
- **代理管理**: `/api/v1/proxies/*`
- **任务管理**: `/api/v1/tasks/*`
- **系统监控**: `/api/v1/health`, `/metrics`

## 主要页面

### 1. 登录/注册页面
- 用户认证界面
- JWT token 管理
- 表单验证和错误处理

### 2. 仪表板
- 系统概览
- 实时统计数据
- 图表展示

### 3. 代理管理
- 代理列表展示
- 添加/删除代理
- 代理状态监控
- 健康检查

### 4. 任务管理
- 任务列表
- 创建新任务
- 任务详情查看
- 任务状态跟踪

### 5. 系统监控
- 实时系统状态
- 性能指标
- 运行中任务监控

## 响应式设计

应用采用移动优先的响应式设计：

- **桌面端**: 完整的侧边栏导航和多列布局
- **平板端**: 适配中等屏幕尺寸
- **移动端**: 折叠式导航和单列布局

## 状态管理

- **认证状态**: 使用 React Context 管理用户认证状态
- **API 状态**: 使用 React Hooks 管理组件状态
- **本地存储**: JWT token 和用户信息持久化

## 错误处理

- **网络错误**: 自动重试和用户友好的错误提示
- **认证错误**: 自动跳转到登录页面
- **表单验证**: 实时验证和错误提示
- **全局通知**: 使用 React Hot Toast 显示操作结果

## 性能优化

- **代码分割**: 使用 React.lazy 进行路由级别的代码分割
- **图片优化**: 使用适当的图片格式和尺寸
- **缓存策略**: 合理的 HTTP 缓存配置
- **打包优化**: 使用 Create React App 的内置优化

## 部署

### 开发环境
```bash
npm start
```

### 生产环境
1. 构建应用：
```bash
npm run build
```

2. 后端服务器会自动服务静态文件，访问 http://localhost:8080 即可

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
