#!/bin/bash

# Vite + TypeScript 迁移脚本
echo "🚀 开始迁移到 Vite + TypeScript..."

# 备份原有文件
echo "📦 备份原有文件..."
mkdir -p backup
cp -r src backup/src-backup-$(date +%Y%m%d-%H%M%S) 2>/dev/null || true

# 删除旧的依赖和配置文件
echo "🧹 清理旧文件..."
rm -rf node_modules
rm -f package-lock.json pnpm-lock.yaml yarn.lock
rm -f craco.config.js .env fix-dev-server.js package-fallback.json

# 删除旧的 JS 文件（已转换为 TS）
echo "🔄 删除已转换的 JS 文件..."
rm -f src/index.js src/App.js
rm -f src/services/api.js
rm -f src/contexts/AuthContext.js
rm -f src/utils/constants.js

# 安装新依赖
echo "📦 安装 Vite + TypeScript 依赖..."
if command -v pnpm &> /dev/null; then
    pnpm install
elif command -v yarn &> /dev/null; then
    yarn install
else
    npm install
fi

echo "✅ 迁移完成！"
echo ""
echo "🎯 下一步："
echo "1. 启动开发服务器: pnpm dev (或 npm run dev)"
echo "2. 构建生产版本: pnpm build (或 npm run build)"
echo "3. 预览生产版本: pnpm preview (或 npm run preview)"
echo ""
echo "🔧 新功能："
echo "- ⚡ 更快的开发服务器启动"
echo "- 🔥 更快的热重载"
echo "- 📝 TypeScript 类型检查"
echo "- 🛠️ 更好的开发体验"
echo ""
echo "📚 文档："
echo "- Vite: https://vitejs.dev/"
echo "- TypeScript: https://www.typescriptlang.org/"
