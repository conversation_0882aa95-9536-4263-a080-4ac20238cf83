# Prettier 配置说明

本文档详细说明了 ProxyFlow 前端项目的 Prettier 代码格式化配置。

## 配置文件位置

- **主配置文件**: `.prettierrc.json`
- **忽略文件**: `.prettierignore`

## 配置项详解

### 基础格式化选项

#### `printWidth: 100`
- **作用**: 设置每行最大字符数为100
- **选择理由**: 
  - 现代显示器分辨率较高，100字符提供更好的代码可读性
  - 避免过度换行，保持代码紧凑
  - 与团队协作和代码审查工具兼容性好

#### `tabWidth: 2`
- **作用**: 设置缩进宽度为2个空格
- **选择理由**: 
  - 与项目现有代码风格保持一致
  - React/TypeScript 社区标准
  - 节省水平空间，适合嵌套较深的JSX结构

#### `useTabs: false`
- **作用**: 使用空格而不是制表符进行缩进
- **选择理由**: 
  - 确保在不同编辑器和环境中显示一致
  - 避免制表符宽度设置差异导致的格式问题

### 语法风格选项

#### `semi: false`
- **作用**: 不在语句末尾添加分号
- **选择理由**: 
  - 与项目现有代码风格保持一致
  - 利用JavaScript的自动分号插入(ASI)机制
  - 代码更简洁，减少视觉噪音

#### `singleQuote: true`
- **作用**: 在JavaScript/TypeScript中使用单引号
- **选择理由**: 
  - 与项目现有代码风格保持一致
  - 减少转义需求，代码更清洁
  - 符合现代JavaScript社区惯例

#### `jsxSingleQuote: false`
- **作用**: 在JSX属性中使用双引号
- **选择理由**: 
  - 与HTML标准保持一致
  - 区分JSX属性和JavaScript字符串
  - 提高代码可读性

#### `quoteProps: "as-needed"`
- **作用**: 仅在需要时为对象属性添加引号
- **选择理由**: 
  - 保持代码简洁
  - 只在属性名包含特殊字符时添加引号

### 结构格式选项

#### `trailingComma: "es5"`
- **作用**: 在ES5支持的地方添加尾随逗号
- **选择理由**: 
  - 便于版本控制，添加新项时diff更清晰
  - 减少语法错误
  - 与项目现有风格保持一致

#### `bracketSpacing: true`
- **作用**: 在对象字面量的大括号内添加空格
- **选择理由**: 
  - 提高代码可读性
  - 符合JavaScript社区标准
  - 与项目现有风格一致

#### `bracketSameLine: false`
- **作用**: 将JSX元素的结束标签放在新行
- **选择理由**: 
  - 提高多行JSX的可读性
  - 便于代码折叠和导航
  - 符合React社区最佳实践

#### `arrowParens: "always"`
- **作用**: 总是在箭头函数参数周围添加括号
- **选择理由**: 
  - 保持一致性，无论参数数量
  - 便于添加类型注解
  - 避免在添加参数时需要修改括号

### 文件处理选项

#### `endOfLine: "lf"`
- **作用**: 使用LF(\n)作为行结束符
- **选择理由**: 
  - 跨平台兼容性
  - Git和CI/CD系统标准
  - 避免Windows/Unix行结束符混乱

#### `embeddedLanguageFormatting: "auto"`
- **作用**: 自动格式化嵌入的代码块
- **选择理由**: 
  - 自动处理模板字符串中的CSS、HTML等
  - 保持代码整体一致性

### 其他选项

#### `singleAttributePerLine: false`
- **作用**: 不强制每个JSX属性占一行
- **选择理由**: 
  - 保持代码紧凑
  - 避免过度换行
  - 让Prettier根据printWidth智能决定

#### `htmlWhitespaceSensitivity: "css"`
- **作用**: 根据CSS显示属性处理HTML空白
- **选择理由**: 
  - 保持HTML语义正确性
  - 避免意外的布局问题

## 使用方法

### 1. 安装Prettier

```bash
# 使用pnpm安装
pnpm add -D prettier

# 或使用npm
npm install --save-dev prettier
```

### 2. 添加脚本到package.json

```json
{
  "scripts": {
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "format:staged": "prettier --write --cache"
  }
}
```

### 3. 编辑器集成

#### VS Code
安装Prettier扩展并在设置中启用：
```json
{
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true
}
```

#### WebStorm/IntelliJ
在设置中启用Prettier并配置为默认格式化工具。

### 4. Git Hooks集成

可以配合husky和lint-staged使用：
```json
{
  "lint-staged": {
    "*.{js,jsx,ts,tsx,json,css,md}": ["prettier --write"]
  }
}
```

## 注意事项

1. **配置优先级**: `.prettierrc.json` > `package.json` > 默认配置
2. **忽略文件**: 使用`.prettierignore`排除不需要格式化的文件
3. **与ESLint配合**: 确保Prettier规则不与ESLint冲突
4. **团队协作**: 所有团队成员应使用相同的Prettier配置

## 常见问题

### Q: 为什么选择100字符行宽？
A: 平衡了代码可读性和现代显示器的宽度，避免过度换行。

### Q: 为什么JSX使用双引号而JS使用单引号？
A: 区分JSX属性和JavaScript字符串，符合HTML标准。

### Q: 如何处理与ESLint的冲突？
A: 使用`eslint-config-prettier`禁用ESLint中与Prettier冲突的规则。

## 更新日志

- **v1.0.0**: 初始配置，基于项目现有代码风格分析
