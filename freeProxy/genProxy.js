const fs = require('fs');
const path = require('path');

function formatProxiesToFile(inputFile, outputFile) {
  try {
    const data = fs.readFileSync(path.resolve(__dirname, inputFile), 'utf8');
    const json = JSON.parse(data);
    const proxies = json.valid_proxies || [];

    const formattedProxies = proxies.map(proxy =>
      `${ proxy.type }://${ proxy.host }:${ proxy.port }`
    );


    fs.writeFileSync(path.resolve(__dirname, outputFile), formattedProxies.join('\n'), 'utf8');
    console.log(`成功将 ${ formattedProxies.length } 条代理写入 ${ outputFile }`);
  } catch (error) {
    console.error('处理失败:', error.message);
  }
}

// 使用示例
formatProxiesToFile('verification_progress.json', 'proxy.txt');