{"permissions": {"allow": ["Bash(go mod:*)", "Bash(go build:*)", "Bash(grep:*)", "WebFetch(domain:github.com)", "WebFetch(domain:pterm.sh)", "Bash(go get:*)", "Bash(rg:*)", "Bash(find:*)", "<PERSON><PERSON>(make:*)", "Bash(./build/proxyFlow-collector:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(go run:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "<PERSON><PERSON>(timeout:*)", "Ba<PERSON>(go vet:*)", "Bash(cp:*)", "<PERSON><PERSON>(sed:*)", "Bash(grep -n \"progressReporter.*callback ProgressCallback\" internal/concurrent/engine.go)"], "deny": []}}